//! # 工具注册表模块
//!
//! 提供工具的注册、管理和执行功能，支持工具发现、参数验证和沙箱集成。

use crate::error::{OrionError, Result};
use crate::sandbox::Sandbox;
use super::{
    Tool, ToolDefinition, ToolRequest, ToolResult,
    implementations::{FileReadTool, FileWriteTool, HttpRequestTool}
};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 工具注册表
/// 
/// 管理所有已注册的工具，提供工具注册、查找、执行等功能。
/// 支持沙箱集成和工具搜索。
pub struct ToolRegistry {
    /// 已注册的工具映射（工具名称 -> 工具实例）
    tools: Arc<RwLock<HashMap<String, Box<dyn Tool>>>>,
    /// 沙箱实例（用于需要沙箱的工具）
    sandbox: Option<Arc<Sandbox>>,
}

impl ToolRegistry {
    /// 创建新的工具注册表
    pub fn new() -> Self {
        Self {
            tools: Arc::new(RwLock::new(HashMap::new())),
            sandbox: None,
        }
    }
    
    /// 设置沙箱实例
    /// 
    /// # 参数
    /// 
    /// * `sandbox` - 沙箱实例
    pub fn with_sandbox(mut self, sandbox: Arc<Sandbox>) -> Self {
        self.sandbox = Some(sandbox);
        self
    }
    
    /// 注册工具
    /// 
    /// 将工具添加到注册表中，使其可以被发现和执行。
    /// 
    /// # 参数
    /// 
    /// * `tool` - 要注册的工具实例
    /// 
    /// # 返回值
    /// 
    /// 如果注册成功返回 `Ok(())`，否则返回错误。
    pub async fn register_tool(&self, tool: Box<dyn Tool>) -> Result<()> {
        let definition = tool.definition();
        let tool_name = definition.name.clone();
        
        // 检查工具名称是否已存在
        {
            let tools = self.tools.read().await;
            if tools.contains_key(&tool_name) {
                return Err(OrionError::ToolError(format!(
                    "工具 '{}' 已经注册", 
                    tool_name
                )));
            }
        }
        
        // 注册工具
        {
            let mut tools = self.tools.write().await;
            tools.insert(tool_name.clone(), tool);
        }
        
        tracing::info!("工具 '{}' 已成功注册", tool_name);
        Ok(())
    }
    
    /// 注册默认工具集
    /// 
    /// 注册系统提供的默认工具，包括文件操作和网络请求工具。
    pub async fn register_default_tools(&self) -> Result<()> {
        tracing::info!("开始注册默认工具集");
        
        // 注册文件系统工具
        self.register_tool(Box::new(FileReadTool::new())).await?;
        self.register_tool(Box::new(FileWriteTool::new())).await?;
        
        // 注册网络工具
        self.register_tool(Box::new(HttpRequestTool::new())).await?;
        
        tracing::info!("默认工具集注册完成");
        Ok(())
    }
    
    /// 获取工具定义
    /// 
    /// 根据工具名称获取工具的定义信息。
    /// 
    /// # 参数
    /// 
    /// * `tool_name` - 工具名称
    /// 
    /// # 返回值
    /// 
    /// 返回工具定义，如果工具不存在则返回错误。
    pub async fn get_tool_definition(&self, tool_name: &str) -> Result<ToolDefinition> {
        let tools = self.tools.read().await;
        let tool = tools.get(tool_name)
            .ok_or_else(|| OrionError::ToolError(format!(
                "工具 '{}' 未注册", 
                tool_name
            )))?;

        Ok(tool.definition())
    }
    
    /// 检查工具是否存在
    /// 
    /// # 参数
    /// 
    /// * `tool_name` - 工具名称
    pub async fn has_tool(&self, tool_name: &str) -> bool {
        let tools = self.tools.read().await;
        tools.contains_key(tool_name)
    }
    
    /// 列出所有工具
    /// 
    /// 返回所有已注册工具的定义列表。
    pub async fn list_tools(&self) -> Vec<ToolDefinition> {
        let tools = self.tools.read().await;
        tools.values().map(|tool| tool.definition()).collect()
    }
    
    /// 按分类列出工具
    /// 
    /// 返回指定分类下的所有工具定义。
    /// 
    /// # 参数
    /// 
    /// * `category` - 工具分类
    pub async fn list_tools_by_category(&self, category: &str) -> Vec<ToolDefinition> {
        let tools = self.tools.read().await;
        tools.values()
            .map(|tool| tool.definition())
            .filter(|def| def.category == category)
            .collect()
    }
    
    /// 搜索工具
    /// 
    /// 根据查询字符串搜索工具，支持按名称、描述和标签搜索。
    /// 
    /// # 参数
    /// 
    /// * `query` - 搜索查询字符串
    pub async fn search_tools(&self, query: &str) -> Vec<ToolDefinition> {
        let tools = self.tools.read().await;
        let query_lower = query.to_lowercase();
        
        tools.values()
            .map(|tool| tool.definition())
            .filter(|def| {
                def.name.to_lowercase().contains(&query_lower) ||
                def.description.to_lowercase().contains(&query_lower) ||
                def.tags.iter().any(|tag| tag.to_lowercase().contains(&query_lower))
            })
            .collect()
    }
    
    /// 获取工具统计信息
    /// 
    /// 返回注册表的统计信息。
    pub async fn get_stats(&self) -> ToolRegistryStats {
        let tools = self.tools.read().await;
        let total_tools = tools.len();
        
        // 按分类统计
        let mut categories = HashMap::new();
        for tool in tools.values() {
            let definition = tool.definition();
            *categories.entry(definition.category.clone()).or_insert(0) += 1;
        }
        
        // 统计需要沙箱的工具数量
        let sandbox_required = tools.values()
            .map(|tool| tool.definition())
            .filter(|def| def.requires_sandbox)
            .count();
        
        ToolRegistryStats {
            total_tools,
            categories,
            sandbox_required,
            has_sandbox: self.sandbox.is_some(),
        }
    }
    
    /// 执行工具
    /// 
    /// 执行指定的工具，自动处理参数验证和沙箱要求。
    /// 
    /// # 参数
    /// 
    /// * `request` - 工具执行请求
    pub async fn execute_tool(&self, request: ToolRequest) -> Result<ToolResult> {
        let tools = self.tools.read().await;
        let tool = tools.get(&request.tool_name)
            .ok_or_else(|| OrionError::ToolError(format!(
                "工具 '{}' 未注册", 
                request.tool_name
            )))?;

        // 检查是否需要沙箱
        let definition = tool.definition();
        if definition.requires_sandbox && self.sandbox.is_none() {
            return Err(OrionError::ToolError(format!(
                "工具 '{}' 需要沙箱环境但未配置", 
                request.tool_name
            )));
        }

        tracing::debug!("开始执行工具: {}", request.tool_name);
        
        // 执行工具
        let result = tool.execute(request).await?;
        
        tracing::debug!(
            "工具执行完成: {} - 成功: {} - 耗时: {}ms", 
            result.request_id,
            result.success,
            result.execution_time_ms
        );
        
        Ok(result)
    }

    /// 在沙箱中执行工具
    /// 
    /// 强制在沙箱环境中执行工具，即使工具不要求沙箱。
    /// 
    /// # 参数
    /// 
    /// * `request` - 工具执行请求
    pub async fn execute_tool_with_sandbox(&self, request: ToolRequest) -> Result<ToolResult> {
        let tools = self.tools.read().await;
        let tool = tools.get(&request.tool_name)
            .ok_or_else(|| OrionError::ToolError(format!(
                "工具 '{}' 未注册", 
                request.tool_name
            )))?;

        // 检查沙箱是否可用
        if self.sandbox.is_none() {
            return Err(OrionError::ToolError(format!(
                "沙箱未配置，无法执行工具 '{}'", 
                request.tool_name
            )));
        }

        tracing::debug!("在沙箱中执行工具: {}", request.tool_name);
        
        // 在沙箱中执行工具
        // 注意：这里应该实现实际的沙箱执行逻辑
        // 目前先简单地调用普通执行方法
        let result = tool.execute(request).await?;
        
        tracing::debug!(
            "沙箱工具执行完成: {} - 成功: {} - 耗时: {}ms", 
            result.request_id,
            result.success,
            result.execution_time_ms
        );
        
        Ok(result)
    }
}

impl Default for ToolRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 工具注册表统计信息
#[derive(Debug, Clone)]
pub struct ToolRegistryStats {
    /// 总工具数量
    pub total_tools: usize,
    /// 按分类统计的工具数量
    pub categories: HashMap<String, usize>,
    /// 需要沙箱的工具数量
    pub sandbox_required: usize,
    /// 是否配置了沙箱
    pub has_sandbox: bool,
}

impl ToolRegistryStats {
    /// 获取分类列表
    pub fn get_categories(&self) -> Vec<String> {
        self.categories.keys().cloned().collect()
    }
    
    /// 获取指定分类的工具数量
    pub fn get_category_count(&self, category: &str) -> usize {
        self.categories.get(category).copied().unwrap_or(0)
    }
}
