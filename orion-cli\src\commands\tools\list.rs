//! # 工具列表和搜索功能
//!
//! 实现工具的列出和搜索功能。

use crate::error::Result;
use crate::commands::tools::{types::*, utils::*};
use orion_core::tools::ToolDefinition;

impl ListTools {
    /// 执行列出工具
    pub async fn execute(&self) -> Result<()> {
        let tool_registry = create_tool_registry(&self.config).await?;
        
        let tools = tool_registry.list_tools().await;
        
        let filtered_tools: Vec<_> = tools.into_iter()
            .filter(|tool| {
                if let Some(category) = &self.category {
                    tool.tags.contains(category)
                } else {
                    true
                }
            })
            .collect();
        
        self.print_tools(&filtered_tools)?;
        
        Ok(())
    }
    
    /// 打印工具列表
    fn print_tools(&self, tools: &[ToolDefinition]) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(tools, &self.format)?;
            }
            "table" | _ => {
                if tools.is_empty() {
                    println!("没有找到工具");
                    return Ok(());
                }

                if self.verbose {
                    // 详细模式
                    for tool in tools {
                        println!("🔧 {}", tool.name);
                        println!("   描述: {}", tool.description);
                        println!("   版本: {}", tool.version);
                        println!("   分类: {}", tool.category);
                        if !tool.tags.is_empty() {
                            println!("   标签: {}", tool.tags.join(", "));
                        }
                        println!("   参数: {} 个", tool.parameters.len());
                        println!();
                    }
                } else {
                    // 表格模式
                    println!("{:<20} {:<10} {:<15} {:<50}", "名称", "版本", "分类", "描述");
                    println!("{}", "-".repeat(100));

                    for tool in tools {
                        println!(
                            "{:<20} {:<10} {:<15} {:<50}",
                            tool.name,
                            tool.version,
                            tool.category,
                            truncate_string(&tool.description, 47)
                        );
                    }
                }
            }
        }

        Ok(())
    }
}

impl SearchTools {
    /// 执行搜索工具
    pub async fn execute(&self) -> Result<()> {
        let tool_registry = create_tool_registry(&self.config).await?;
        
        let results = tool_registry.search_tools(&self.query).await;
        
        let limited_results: Vec<_> = results.into_iter()
            .take(self.limit)
            .collect();
        
        self.print_search_results(&limited_results)?;
        
        Ok(())
    }
    
    /// 打印搜索结果
    fn print_search_results(&self, results: &[ToolDefinition]) -> Result<()> {
        if results.is_empty() {
            println!("没有找到匹配的工具");
            return Ok(());
        }

        println!("🔍 找到 {} 个匹配的工具:", results.len());
        println!();

        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(results, &self.format)?;
            }
            "table" | _ => {
                println!("{:<20} {:<10} {:<15} {:<50}", "名称", "版本", "分类", "描述");
                println!("{}", "-".repeat(100));

                for tool in results {
                    println!(
                        "{:<20} {:<10} {:<15} {:<50}",
                        tool.name,
                        tool.version,
                        tool.category,
                        truncate_string(&tool.description, 47)
                    );
                }
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_list_tools_command() {
        let cmd = ListTools {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            category: None,
            verbose: false,
            installed: false,
            available: false,
        };
        
        assert_eq!(cmd.format, "table");
        assert!(!cmd.verbose);
    }
    
    #[tokio::test]
    async fn test_search_tools_command() {
        let cmd = SearchTools {
            config: PathBuf::from("test.toml"),
            query: "file".to_string(),
            format: "table".to_string(),
            limit: 10,
            scope: "all".to_string(),
        };
        
        assert_eq!(cmd.query, "file");
        assert_eq!(cmd.limit, 10);
    }

    #[test]
    fn test_print_empty_tools() {
        let cmd = ListTools {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            category: None,
            verbose: false,
            installed: false,
            available: false,
        };

        let empty_tools: Vec<ToolDefinition> = vec![];
        let result = cmd.print_tools(&empty_tools);
        assert!(result.is_ok());
    }
}