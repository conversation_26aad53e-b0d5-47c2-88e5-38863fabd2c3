//! # Agent 状态和统计功能
//!
//! 实现查看 Agent 状态和统计信息的功能。

use crate::error::Result;
use crate::commands::agent::{types::*, utils::*};

impl AgentStatus {
    /// 执行状态查询
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        if self.watch {
            self.watch_status().await
        } else {
            self.show_status().await
        }
    }
    
    /// 显示状态
    async fn show_status(&self) -> Result<()> {
        println!("🔍 Agent '{}' 状态信息:", self.agent_name);
        
        let status_info = self.get_status_info().await;
        
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(&status_info, &self.format)?
            }
            "text" | _ => {
                self.print_text_status(&status_info);
            }
        }
        
        Ok(())
    }
    
    /// 实时监控状态
    async fn watch_status(&self) -> Result<()> {
        println!("📺 实时监控 Agent '{}' 状态 (每 {} 秒刷新)", 
                 self.agent_name, self.interval);
        println!("按 Ctrl+C 退出\n");
        
        loop {
            let status_info = self.get_status_info().await;
            
            // 清屏并重新显示
            print!("\x1B[2J\x1B[1;1H"); // ANSI 清屏码
            println!("📺 Agent '{}' 实时状态 - {}", 
                     self.agent_name, 
                     chrono::Utc::now().format("%Y-%m-%d %H:%M:%S"));
            println!("{}", "-".repeat(50));
            
            self.print_text_status(&status_info);
            
            tokio::time::sleep(tokio::time::Duration::from_secs(self.interval)).await;
        }
    }
    
    /// 打印文本格式状态
    fn print_text_status(&self, status: &AgentStatusInfo) {
        println!("🟢 状态: {}", status.state);
        println!("📊 运行时间: {}", status.uptime);
        println!("💼 当前任务: {}", status.current_task.as_deref().unwrap_or("无"));
        println!("💾 内存使用: {:.1} MB", status.memory_usage);
        println!("🔥 CPU 使用: {:.1}%", status.cpu_usage);
        
        if self.verbose {
            println!();
            println!("📊 详细信息:");
            println!("  模型: {}", status.model);
            println!("  会话数: {}", status.active_sessions);
            println!("  错误数: {}", status.error_count);
            println!("  最后活动: {}", status.last_activity);
        }
    }
    
    /// 获取状态信息
    async fn get_status_info(&self) -> AgentStatusInfo {
        // 这里应该从真实的 Agent 管理器获取状态
        // 现在返回模拟数据
        AgentStatusInfo {
            state: "运行中".to_string(),
            uptime: "2h 30m 15s".to_string(),
            current_task: Some("代码生成".to_string()),
            memory_usage: 156.7,
            cpu_usage: 23.4,
            model: "gpt-4".to_string(),
            active_sessions: 3,
            error_count: 0,
            last_activity: "2 分钟前".to_string(),
        }
    }
}

impl AgentStats {
    /// 执行统计查询
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        if self.reset {
            self.reset_stats().await
        } else {
            self.show_stats().await
        }
    }
    
    /// 显示统计信息
    async fn show_stats(&self) -> Result<()> {
        println!("📊 Agent '{}' 统计信息:", self.agent_name);
        
        let stats_info = self.get_stats_info().await;
        
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(&stats_info, &self.format)?
            }
            "text" | _ => {
                self.print_text_stats(&stats_info);
            }
        }
        
        if let Some(export_path) = &self.export {
            self.export_stats(&stats_info, export_path).await?;
        }
        
        Ok(())
    }
    
    /// 打印文本格式统计
    fn print_text_stats(&self, stats: &AgentStatsInfo) {
        let time_range = if let Some(hours) = self.hours {
            format!("过去 {} 小时", hours)
        } else {
            "全部时间".to_string()
        };
        
        println!("📅 时间范围: {}", time_range);
        println!();
        
        println!("💼 任务统计:");
        println!("  完成任务: {} 个", stats.completed_tasks);
        println!("  失败任务: {} 个", stats.failed_tasks);
        println!("  成功率: {:.1}%", stats.success_rate);
        println!("  平均响应时间: {:.2} 秒", stats.avg_response_time);
        
        println!();
        println!("💾 资源使用:");
        println!("  平均内存: {:.1} MB", stats.avg_memory_usage);
        println!("  峰值内存: {:.1} MB", stats.peak_memory_usage);
        println!("  平均 CPU: {:.1}%", stats.avg_cpu_usage);
        println!("  峰值 CPU: {:.1}%", stats.peak_cpu_usage);
        
        println!();
        println!("🔥 活动统计:");
        println!("  总运行时间: {}", stats.total_uptime);
        println!("  最长连续运行: {}", stats.longest_session);
        println!("  重启次数: {} 次", stats.restart_count);
    }
    
    /// 重置统计
    async fn reset_stats(&self) -> Result<()> {
        println!("⚠️  确认重置 Agent '{}' 的统计信息？", self.agent_name);
        println!("🗑️  正在重置统计数据...");
        
        simulate_processing_delay(500).await;
        
        println!("✅ 统计信息已重置");
        Ok(())
    }
    
    /// 导出统计
    async fn export_stats(&self, stats: &AgentStatsInfo, export_path: &std::path::Path) -> Result<()> {
        println!("💾 正在导出统计数据到: {}", export_path.display());
        
        let content = match export_path.extension().and_then(|s| s.to_str()) {
            Some("json") => serde_json::to_string_pretty(stats).unwrap(),
            Some("yaml") => serde_yaml::to_string(stats).unwrap(),
            _ => serde_json::to_string_pretty(stats).unwrap(),
        };
        
        tokio::fs::write(export_path, content).await
            .map_err(|e| crate::error::CliError::IoError { 
                error: format!("写入文件失败: {}", e) 
            })?;
        
        println!("✅ 统计数据已导出");
        Ok(())
    }
    
    /// 获取统计信息
    async fn get_stats_info(&self) -> AgentStatsInfo {
        // 这里应该从真实的 Agent 统计系统获取数据
        AgentStatsInfo {
            completed_tasks: 156,
            failed_tasks: 3,
            success_rate: 98.1,
            avg_response_time: 1.34,
            avg_memory_usage: 145.6,
            peak_memory_usage: 312.4,
            avg_cpu_usage: 18.7,
            peak_cpu_usage: 67.2,
            total_uptime: "15d 8h 23m".to_string(),
            longest_session: "6h 45m".to_string(),
            restart_count: 2,
        }
    }
}

/// Agent 状态信息
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
struct AgentStatusInfo {
    state: String,
    uptime: String,
    current_task: Option<String>,
    memory_usage: f64,
    cpu_usage: f64,
    model: String,
    active_sessions: u32,
    error_count: u32,
    last_activity: String,
}

/// Agent 统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
struct AgentStatsInfo {
    completed_tasks: u64,
    failed_tasks: u64,
    success_rate: f64,
    avg_response_time: f64,
    avg_memory_usage: f64,
    peak_memory_usage: f64,
    avg_cpu_usage: f64,
    peak_cpu_usage: f64,
    total_uptime: String,
    longest_session: String,
    restart_count: u32,
}

#[cfg(test)]  
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_agent_status() {
        let cmd = AgentStatus {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            format: "text".to_string(),
            verbose: false,
            watch: false,
            interval: 5,
        };

        assert_eq!(cmd.agent_name, "test-agent");
        assert_eq!(cmd.format, "text");
        assert!(!cmd.verbose);
        assert!(!cmd.watch);
    }

    #[tokio::test]
    async fn test_agent_stats() {
        let cmd = AgentStats {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            format: "text".to_string(),
            hours: Some(24),
            reset: false,
            export: None,
        };

        assert_eq!(cmd.agent_name, "test-agent");
        assert_eq!(cmd.format, "text");
        assert_eq!(cmd.hours, Some(24));
        assert!(!cmd.reset);
    }
}