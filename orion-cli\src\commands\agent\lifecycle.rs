//! # Agent 生命周期管理
//!
//! 实现 Agent 的启动、停止、重启功能。

use crate::error::Result;
use crate::commands::agent::{types::*, utils::*};

impl StartAgent {
    /// 执行启动 Agent
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        println!("🚀 启动 Agent: {}", self.agent_name);
        
        if let Some(log_level) = &self.log_level {
            println!("📝 设置日志级别: {}", log_level);
        }
        
        if let Some(workdir) = &self.workdir {
            println!("📁 工作目录: {}", workdir.display());
        }
        
        if self.daemon {
            println!("👻 以守护进程模式运行");
        }
        
        if self.monitor {
            println!("📊 启用监控模式");
        }
        
        // 模拟启动过程
        simulate_processing_delay(1000).await;
        
        println!("✅ Agent '{}' 已成功启动", self.agent_name);
        
        Ok(())
    }
}

impl StopAgent {
    /// 执行停止 Agent
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        println!("🛑 停止 Agent: {}", self.agent_name);
        
        if self.force {
            println!("⚠️  强制停止模式");
        } else {
            println!("🕐 优雅停止，超时时间: {} 秒", self.timeout);
        }
        
        if self.save_state {
            println!("💾 保存 Agent 状态...");
            simulate_processing_delay(500).await;
            println!("✅ 状态已保存");
        }
        
        // 模拟停止过程
        simulate_processing_delay(800).await;
        
        println!("✅ Agent '{}' 已成功停止", self.agent_name);
        
        Ok(())
    }
}

impl RestartAgent {
    /// 执行重启 Agent
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        println!("🔄 重启 Agent: {}", self.agent_name);
        
        if self.force {
            println!("⚠️  强制重启模式");
        }
        
        // 停止阶段
        println!("🛑 正在停止 Agent...");
        simulate_processing_delay(800).await;
        println!("✅ Agent 已停止");
        
        // 等待延迟
        if self.delay > 0 {
            println!("⏳ 等待 {} 秒后重启...", self.delay);
            simulate_processing_delay(self.delay * 1000).await;
        }
        
        // 启动阶段
        println!("🚀 正在启动 Agent...");
        simulate_processing_delay(1000).await;
        println!("✅ Agent '{}' 已成功重启", self.agent_name);
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_start_agent() {
        let cmd = StartAgent {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            daemon: false,
            monitor: false,
            log_level: Some("info".to_string()),
            workdir: None,
        };

        // 测试不应该 panic
        assert_eq!(cmd.agent_name, "test-agent");
        assert_eq!(cmd.log_level, Some("info".to_string()));
    }

    #[tokio::test]
    async fn test_stop_agent() {
        let cmd = StopAgent {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            force: false,
            timeout: 30,
            save_state: true,
        };

        assert_eq!(cmd.agent_name, "test-agent");
        assert!(!cmd.force);
        assert_eq!(cmd.timeout, 30);
        assert!(cmd.save_state);
    }

    #[tokio::test]
    async fn test_restart_agent() {
        let cmd = RestartAgent {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            force: false,
            delay: 5,
        };

        assert_eq!(cmd.agent_name, "test-agent");
        assert!(!cmd.force);
        assert_eq!(cmd.delay, 5);
    }
}