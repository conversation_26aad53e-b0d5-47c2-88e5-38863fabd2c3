//! # 工具命令模块
//!
//! 实现 Orion 工具的管理功能，重构为模块化结构。

// 声明子模块
pub mod types;
pub mod utils;
pub mod list;
pub mod show;
pub mod execute;
pub mod manage;
pub mod develop;
pub mod config;

// 重新导出主要类型
pub use types::*;

use crate::error::Result;

impl ToolsCommand {
    /// 执行工具命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            ToolsAction::List(cmd) => cmd.execute().await,
            ToolsAction::Search(cmd) => cmd.execute().await,
            ToolsAction::Show(cmd) => cmd.execute().await,
            ToolsAction::Install(cmd) => cmd.execute().await,
            ToolsAction::Uninstall(cmd) => cmd.execute().await,
            ToolsAction::Update(cmd) => cmd.execute().await,
            ToolsAction::Run(cmd) => cmd.execute().await,
            ToolsAction::Test(cmd) => cmd.execute().await,
            ToolsAction::Create(cmd) => cmd.execute().await,
            ToolsAction::Validate(cmd) => cmd.execute().await,
            ToolsAction::Config(cmd) => cmd.execute().await,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_tools_command_creation() {
        let list_cmd = ListTools {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            category: None,
            verbose: false,
            installed: false,
            available: false,
        };

        let tools_cmd = ToolsCommand {
            action: ToolsAction::List(list_cmd),
        };

        // 测试基本结构
        match &tools_cmd.action {
            ToolsAction::List(cmd) => {
                assert_eq!(cmd.format, "table");
                assert!(!cmd.verbose);
            }
            _ => panic!("Expected List action"),
        }
    }

    #[tokio::test]
    async fn test_run_tool_command() {
        let run_cmd = RunTool {
            config: PathBuf::from("test.toml"),
            tool_name: "test_tool".to_string(),
            params: Some("{\"key\": \"value\"}".to_string()),
            params_file: None,
            format: "text".to_string(),
            timeout: Some(30),
            sandbox: true,
            verbose: false,
        };

        assert_eq!(run_cmd.tool_name, "test_tool");
        assert!(run_cmd.sandbox);
        assert_eq!(run_cmd.timeout, Some(30));
    }

    #[test]
    fn test_tools_action_variants() {
        // 测试所有 ToolsAction 变体都存在
        let actions = vec![
            "List", "Search", "Show", "Install", "Uninstall", 
            "Update", "Run", "Test", "Create", "Validate", "Config"
        ];

        // 这个测试确保我们没有遗漏任何命令类型
        assert_eq!(actions.len(), 11);
    }

    #[tokio::test]
    async fn test_install_tool_command() {
        let install_cmd = InstallTool {
            config: PathBuf::from("test.toml"),
            tool: "new_tool".to_string(),
            version: Some("2.0.0".to_string()),
            force: false,
            local: false,
            url: None,
            validate: true,
        };

        assert_eq!(install_cmd.tool, "new_tool");
        assert_eq!(install_cmd.version, Some("2.0.0".to_string()));
        assert!(install_cmd.validate);
    }

    #[tokio::test]
    async fn test_create_tool_command() {
        let create_cmd = CreateTool {
            name: "my_new_tool".to_string(),
            description: Some("测试工具创建".to_string()),
            output: PathBuf::from("./tools"),
            template: Some("basic".to_string()),
            interactive: false,
            language: "rust".to_string(),
        };

        assert_eq!(create_cmd.name, "my_new_tool");
        assert_eq!(create_cmd.template, Some("basic".to_string()));
        assert_eq!(create_cmd.language, "rust");
    }
}