//! # 工作流管理器模块
//!
//! 提供工作流的高级管理功能。
//! 包括工作流定义的注册、实例的创建和管理、执行控制等。

use std::collections::HashMap;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use uuid::Uuid;

use crate::error::{OrionError, Result, WorkflowError};

use super::definition::WorkflowDefinition;
use super::execution::WorkflowExecutor;
use super::instance::{ExecutionContext, WorkflowInstance, WorkflowStatus};

/// 工作流管理器
/// 
/// 提供工作流的完整生命周期管理功能，包括：
/// - 工作流定义的注册和管理
/// - 工作流实例的创建和跟踪
/// - 工作流执行的控制和监控
/// - 工作流状态的查询和统计
pub struct WorkflowManager {
    /// 工作流定义存储
    /// 键为工作流ID，值为工作流定义
    definitions: Arc<RwLock<HashMap<Uuid, WorkflowDefinition>>>,
    
    /// 工作流实例存储
    /// 键为实例ID，值为工作流实例
    instances: Arc<RwLock<HashMap<Uuid, WorkflowInstance>>>,
    
    /// 工作流执行器
    /// 负责实际的工作流执行逻辑
    executor: Arc<dyn WorkflowExecutor>,
}

impl WorkflowManager {
    /// 创建新的工作流管理器
    /// 
    /// # 参数
    /// * `executor` - 工作流执行器的共享引用
    /// 
    /// # 返回值
    /// 返回新创建的工作流管理器实例
    pub fn new(executor: Arc<dyn WorkflowExecutor>) -> Self {
        Self {
            definitions: Arc::new(RwLock::new(HashMap::new())),
            instances: Arc::new(RwLock::new(HashMap::new())),
            executor,
        }
    }
    
    /// 注册工作流定义
    /// 
    /// 将工作流定义添加到管理器中，使其可用于创建实例。
    /// 
    /// # 参数
    /// * `definition` - 要注册的工作流定义
    /// 
    /// # 返回值
    /// * `Ok(())` - 注册成功
    /// * `Err(OrionError)` - 注册失败
    pub async fn register_workflow(&self, definition: WorkflowDefinition) -> Result<()> {
        let mut definitions = self.definitions.write().await;
        definitions.insert(definition.id, definition.clone());
        tracing::info!("工作流 '{}' 已注册", definition.name);
        Ok(())
    }
    
    /// 创建工作流实例
    /// 
    /// 基于已注册的工作流定义创建新的执行实例。
    /// 
    /// # 参数
    /// * `workflow_id` - 工作流定义的ID
    /// * `name` - 实例名称
    /// * `input_parameters` - 输入参数值
    /// 
    /// # 返回值
    /// * `Ok(Uuid)` - 创建成功，返回实例ID
    /// * `Err(OrionError)` - 创建失败（如工作流定义不存在）
    pub async fn create_instance(
        &self,
        workflow_id: Uuid,
        name: String,
        input_parameters: HashMap<String, serde_json::Value>,
    ) -> Result<Uuid> {
        let definitions = self.definitions.read().await;
        let _definition = definitions.get(&workflow_id)
            .ok_or_else(|| OrionError::Workflow(WorkflowError::definition_error(
                format!("工作流定义不存在: {}", workflow_id)
            )))?;
        
        let instance_id = Uuid::new_v4();
        let instance = WorkflowInstance {
            id: instance_id,
            workflow_id,
            name,
            status: WorkflowStatus::Pending,
            input_parameters,
            output_parameters: HashMap::new(),
            current_step_id: None,
            completed_steps: Vec::new(),
            failed_steps: Vec::new(),
            skipped_steps: Vec::new(),
            execution_context: ExecutionContext::default(),
            created_at: SystemTime::now(),
            started_at: None,
            completed_at: None,
            error: None,
        };
        
        let mut instances = self.instances.write().await;
        instances.insert(instance_id, instance);
        
        tracing::info!("工作流实例 '{}' 已创建", instance_id);
        Ok(instance_id)
    }
    
    /// 执行工作流实例
    /// 
    /// 启动指定工作流实例的执行。
    /// 
    /// # 参数
    /// * `instance_id` - 要执行的实例ID
    /// 
    /// # 返回值
    /// * `Ok(())` - 执行成功
    /// * `Err(OrionError)` - 执行失败（如实例不存在或定义不存在）
    pub async fn execute_instance(&self, instance_id: Uuid) -> Result<()> {
        let (mut instance, definition) = {
            let instances = self.instances.read().await;
            let definitions = self.definitions.read().await;
            
            let instance = instances.get(&instance_id)
                .ok_or_else(|| OrionError::Workflow(WorkflowError::execution_error(
                    format!("工作流实例不存在: {}", instance_id)
                )))?.clone();

            let definition = definitions.get(&instance.workflow_id)
                .ok_or_else(|| OrionError::Workflow(WorkflowError::definition_error(
                    format!("工作流定义不存在: {}", instance.workflow_id)
                )))?.clone();
            
            (instance, definition)
        };
        
        // 执行工作流
        let result = self.executor.execute_workflow(&mut instance, &definition).await;
        
        // 更新实例
        {
            let mut instances = self.instances.write().await;
            instances.insert(instance_id, instance);
        }
        
        result
    }
    
    /// 获取工作流实例
    /// 
    /// 根据实例ID获取工作流实例的详细信息。
    /// 
    /// # 参数
    /// * `instance_id` - 实例ID
    /// 
    /// # 返回值
    /// * `Ok(WorkflowInstance)` - 获取成功，返回实例信息
    /// * `Err(OrionError)` - 获取失败（如实例不存在）
    pub async fn get_instance(&self, instance_id: Uuid) -> Result<WorkflowInstance> {
        let instances = self.instances.read().await;
        instances.get(&instance_id)
            .cloned()
            .ok_or_else(|| OrionError::Workflow(WorkflowError::execution_error(
                format!("工作流实例不存在: {}", instance_id)
            )))
    }
    
    /// 列出所有工作流定义
    /// 
    /// 获取所有已注册的工作流定义列表。
    /// 
    /// # 返回值
    /// 返回工作流定义的向量
    pub async fn list_workflows(&self) -> Vec<WorkflowDefinition> {
        let definitions = self.definitions.read().await;
        definitions.values().cloned().collect()
    }
    
    /// 列出工作流实例
    /// 
    /// 获取工作流实例列表，可选择性地按工作流ID过滤。
    /// 
    /// # 参数
    /// * `workflow_id` - 可选的工作流ID过滤器
    /// 
    /// # 返回值
    /// 返回符合条件的工作流实例向量
    pub async fn list_instances(&self, workflow_id: Option<Uuid>) -> Vec<WorkflowInstance> {
        let instances = self.instances.read().await;
        instances.values()
            .filter(|instance| {
                workflow_id.map_or(true, |id| instance.workflow_id == id)
            })
            .cloned()
            .collect()
    }
    
    /// 删除工作流定义
    /// 
    /// 从管理器中移除指定的工作流定义。
    /// 注意：如果有活跃的实例正在使用此定义，删除可能会失败。
    /// 
    /// # 参数
    /// * `workflow_id` - 要删除的工作流定义ID
    /// 
    /// # 返回值
    /// * `Ok(())` - 删除成功
    /// * `Err(OrionError)` - 删除失败
    pub async fn delete_workflow(&self, workflow_id: Uuid) -> Result<()> {
        let mut definitions = self.definitions.write().await;
        if definitions.remove(&workflow_id).is_some() {
            tracing::info!("工作流定义 {} 已删除", workflow_id);
            Ok(())
        } else {
            Err(OrionError::Workflow(WorkflowError::definition_error(
                format!("工作流定义不存在: {}", workflow_id)
            )))
        }
    }
    
    /// 删除工作流实例
    /// 
    /// 从管理器中移除指定的工作流实例。
    /// 注意：只能删除已完成、失败或取消的实例。
    /// 
    /// # 参数
    /// * `instance_id` - 要删除的实例ID
    /// 
    /// # 返回值
    /// * `Ok(())` - 删除成功
    /// * `Err(OrionError)` - 删除失败
    pub async fn delete_instance(&self, instance_id: Uuid) -> Result<()> {
        let mut instances = self.instances.write().await;
        if let Some(instance) = instances.get(&instance_id) {
            // 检查实例状态，只允许删除非活跃状态的实例
            match instance.status {
                WorkflowStatus::Running | WorkflowStatus::Paused => {
                    return Err(OrionError::Workflow(WorkflowError::invalid_state(
                        format!("{:?}", instance.status), "非活跃状态"
                    )));
                }
                _ => {}
            }
        }
        
        if instances.remove(&instance_id).is_some() {
            tracing::info!("工作流实例 {} 已删除", instance_id);
            Ok(())
        } else {
            Err(OrionError::Workflow(WorkflowError::execution_error(
                format!("工作流实例不存在: {}", instance_id)
            )))
        }
    }
}
