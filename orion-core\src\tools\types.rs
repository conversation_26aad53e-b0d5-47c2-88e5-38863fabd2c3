//! # 工具系统核心类型定义
//!
//! 定义工具系统中使用的所有数据结构和枚举类型，包括工具参数、
//! 工具定义、执行请求、执行结果等核心类型。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::SystemTime;
use uuid::Uuid;

/// 工具参数定义
/// 
/// 描述工具的单个参数，包括名称、类型、描述、是否必需、默认值和约束条件。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolParameter {
    /// 参数名称
    pub name: String,
    /// 参数类型
    pub param_type: ParameterType,
    /// 参数描述
    pub description: String,
    /// 是否必需
    pub required: bool,
    /// 默认值
    pub default_value: Option<serde_json::Value>,
    /// 参数约束
    pub constraints: Option<ParameterConstraints>,
}

/// 参数类型枚举
/// 
/// 定义工具参数支持的所有数据类型，包括基础类型和复合类型。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ParameterType {
    /// 字符串类型
    String,
    /// 整数类型
    Integer,
    /// 浮点数类型
    Float,
    /// 布尔值类型
    Boolean,
    /// 数组类型（包含元素类型）
    Array(Box<ParameterType>),
    /// 对象类型
    Object,
    /// 文件路径类型
    FilePath,
    /// URL 类型
    Url,
    /// JSON 类型
    Json,
}

/// 参数约束条件
/// 
/// 定义参数的各种约束条件，用于参数验证。
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ParameterConstraints {
    /// 最小值（适用于数字类型）
    pub min_value: Option<f64>,
    /// 最大值（适用于数字类型）
    pub max_value: Option<f64>,
    /// 最小长度（适用于字符串和数组类型）
    pub min_length: Option<usize>,
    /// 最大长度（适用于字符串和数组类型）
    pub max_length: Option<usize>,
    /// 正则表达式模式（适用于字符串类型）
    pub pattern: Option<String>,
    /// 枚举值列表（限制参数只能取特定值）
    pub enum_values: Option<Vec<serde_json::Value>>,
}

/// 工具定义
/// 
/// 完整描述一个工具的元数据，包括名称、版本、参数、返回类型等。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolDefinition {
    /// 工具名称（唯一标识符）
    pub name: String,
    /// 工具版本
    pub version: String,
    /// 工具描述
    pub description: String,
    /// 工具分类（如 filesystem、network、data 等）
    pub category: String,
    /// 参数定义列表
    pub parameters: Vec<ToolParameter>,
    /// 返回值类型
    pub return_type: ParameterType,
    /// 是否需要沙箱环境执行
    pub requires_sandbox: bool,
    /// 工具标签（用于搜索和分类）
    pub tags: Vec<String>,
    /// 使用示例
    pub examples: Vec<ToolExample>,
}

/// 工具使用示例
/// 
/// 展示工具的典型使用方法，包括输入参数和期望输出。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolExample {
    /// 示例描述
    pub description: String,
    /// 输入参数
    pub input: HashMap<String, serde_json::Value>,
    /// 期望输出
    pub expected_output: serde_json::Value,
}

/// 工具执行请求
/// 
/// 包含执行工具所需的所有信息，包括工具名称、参数和执行上下文。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolRequest {
    /// 请求唯一标识符
    pub id: Uuid,
    /// 要执行的工具名称
    pub tool_name: String,
    /// 执行参数
    pub parameters: HashMap<String, serde_json::Value>,
    /// 执行上下文
    pub context: ToolContext,
    /// 请求创建时间戳
    pub timestamp: SystemTime,
}

/// 工具执行上下文
/// 
/// 提供工具执行时的环境信息和配置。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolContext {
    /// 工作目录
    pub working_directory: Option<PathBuf>,
    /// 环境变量
    pub environment: HashMap<String, String>,
    /// 会话标识符
    pub session_id: Option<String>,
    /// 用户标识符
    pub user_id: Option<String>,
    /// 超时时间（秒）
    pub timeout_seconds: Option<u64>,
}

impl Default for ToolContext {
    /// 创建默认的工具执行上下文
    /// 
    /// 默认超时时间为 30 秒，其他字段为空。
    fn default() -> Self {
        Self {
            working_directory: None,
            environment: HashMap::new(),
            session_id: None,
            user_id: None,
            timeout_seconds: Some(30),
        }
    }
}

/// 工具执行结果
/// 
/// 包含工具执行的完整结果信息，包括成功状态、数据、错误信息和资源使用情况。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    /// 对应的请求标识符
    pub request_id: Uuid,
    /// 执行是否成功
    pub success: bool,
    /// 结果数据
    pub data: serde_json::Value,
    /// 错误信息（如果执行失败）
    pub error: Option<String>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 资源使用统计
    pub resources_used: ResourceUsage,
    /// 结果生成时间戳
    pub timestamp: SystemTime,
}

/// 资源使用统计
/// 
/// 记录工具执行过程中的资源消耗情况。
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ResourceUsage {
    /// CPU 时间（毫秒）
    pub cpu_time_ms: u64,
    /// 内存使用峰值（字节）
    pub peak_memory_bytes: u64,
    /// 磁盘读取字节数
    pub disk_read_bytes: u64,
    /// 磁盘写入字节数
    pub disk_write_bytes: u64,
    /// 网络发送字节数
    pub network_sent_bytes: u64,
    /// 网络接收字节数
    pub network_received_bytes: u64,
}

impl ToolParameter {
    /// 创建新的工具参数
    /// 
    /// # 参数
    /// 
    /// * `name` - 参数名称
    /// * `param_type` - 参数类型
    /// * `description` - 参数描述
    /// * `required` - 是否必需
    pub fn new(
        name: String,
        param_type: ParameterType,
        description: String,
        required: bool,
    ) -> Self {
        Self {
            name,
            param_type,
            description,
            required,
            default_value: None,
            constraints: None,
        }
    }
    
    /// 设置默认值
    pub fn with_default_value(mut self, value: serde_json::Value) -> Self {
        self.default_value = Some(value);
        self
    }
    
    /// 设置约束条件
    pub fn with_constraints(mut self, constraints: ParameterConstraints) -> Self {
        self.constraints = Some(constraints);
        self
    }
}

impl ToolRequest {
    /// 创建新的工具执行请求
    /// 
    /// # 参数
    /// 
    /// * `tool_name` - 工具名称
    /// * `parameters` - 执行参数
    pub fn new(
        tool_name: String,
        parameters: HashMap<String, serde_json::Value>,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            tool_name,
            parameters,
            context: ToolContext::default(),
            timestamp: SystemTime::now(),
        }
    }
    
    /// 设置执行上下文
    pub fn with_context(mut self, context: ToolContext) -> Self {
        self.context = context;
        self
    }
}

impl ToolResult {
    /// 创建成功的执行结果
    /// 
    /// # 参数
    /// 
    /// * `request_id` - 请求标识符
    /// * `data` - 结果数据
    /// * `execution_time_ms` - 执行时间
    pub fn success(
        request_id: Uuid,
        data: serde_json::Value,
        execution_time_ms: u64,
    ) -> Self {
        Self {
            request_id,
            success: true,
            data,
            error: None,
            execution_time_ms,
            resources_used: ResourceUsage::default(),
            timestamp: SystemTime::now(),
        }
    }
    
    /// 创建失败的执行结果
    /// 
    /// # 参数
    /// 
    /// * `request_id` - 请求标识符
    /// * `error` - 错误信息
    /// * `execution_time_ms` - 执行时间
    pub fn failure(
        request_id: Uuid,
        error: String,
        execution_time_ms: u64,
    ) -> Self {
        Self {
            request_id,
            success: false,
            data: serde_json::Value::Null,
            error: Some(error),
            execution_time_ms,
            resources_used: ResourceUsage::default(),
            timestamp: SystemTime::now(),
        }
    }
    
    /// 设置资源使用统计
    pub fn with_resources(mut self, resources: ResourceUsage) -> Self {
        self.resources_used = resources;
        self
    }
}
