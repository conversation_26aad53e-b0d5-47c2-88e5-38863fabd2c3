//! # 工作流验证功能
//!
//! 实现工作流定义的验证功能。

use crate::error::{CliError, Result};
use crate::commands::workflow::{types::ValidateWorkflow, utils::*};

impl ValidateWorkflow {
    /// 执行验证工作流
    pub async fn execute(&self) -> Result<()> {
        let _workflow_manager = create_workflow_manager(&self.config).await?;
        
        println!("🔍 验证工作流文件: {}", self.file.display());
        
        // 读取工作流文件
        let content = tokio::fs::read_to_string(&self.file).await
            .map_err(|e| CliError::IoError {
                error: format!("读取工作流文件失败: {}", e),
            })?;
        
        // 解析工作流定义
        let workflow_def: orion_core::workflow::WorkflowDefinition = 
            parse_file_content(&self.file, &content).await?;
        
        println!("✅ 工作流文件格式正确");
        
        if self.verbose {
            println!();
            println!("📋 工作流信息:");
            println!("  名称: {}", workflow_def.name);
            println!("  版本: {}", workflow_def.version);
            println!("  描述: {}", if workflow_def.description.is_empty() { "无" } else { &workflow_def.description });
            println!("  步骤数: {}", workflow_def.steps.len());
            println!("  输入参数: {}", workflow_def.input_parameters.len());
            println!("  输出参数: {}", workflow_def.output_parameters.len());
            
            if !workflow_def.steps.is_empty() {
                println!();
                println!("🔧 步骤详情:");
                for (i, step) in workflow_def.steps.iter().enumerate() {
                    println!("  {}. {} ({})", 
                        i + 1, 
                        step.name,
                        format!("{:?}", step.step_type).split('(').next().unwrap_or("Unknown")
                    );
                    if !step.description.is_empty() {
                        println!("     描述: {}", step.description);
                    }
                }
            }
        }
        
        // 执行基本验证
        self.validate_workflow_structure(&workflow_def)?;
        
        simulate_processing_delay(300).await;
        
        println!("✅ 工作流验证通过");
        Ok(())
    }
    
    /// 验证工作流结构
    fn validate_workflow_structure(&self, workflow_def: &orion_core::workflow::WorkflowDefinition) -> Result<()> {
        // 验证基本信息
        if workflow_def.name.is_empty() {
            return Err(CliError::InvalidArgument {
                error: "工作流名称不能为空".to_string(),
            });
        }
        
        if workflow_def.steps.is_empty() {
            return Err(CliError::InvalidArgument {
                error: "工作流必须至少包含一个步骤".to_string(),
            });
        }
        
        // 验证初始步骤ID存在
        let initial_step_exists = workflow_def.steps.iter()
            .any(|step| step.id == workflow_def.initial_step_id);
        
        if !initial_step_exists {
            return Err(CliError::InvalidArgument {
                error: "初始步骤ID不存在于步骤列表中".to_string(),
            });
        }
        
        // 验证步骤名称唯一性
        let mut step_names = std::collections::HashSet::new();
        for step in &workflow_def.steps {
            if !step_names.insert(&step.name) {
                return Err(CliError::InvalidArgument {
                    error: format!("步骤名称重复: {}", step.name),
                });
            }
        }
        
        if self.verbose {
            println!("  ✓ 工作流名称有效");
            println!("  ✓ 步骤数量: {}", workflow_def.steps.len());
            println!("  ✓ 初始步骤ID有效");
            println!("  ✓ 步骤名称唯一");
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_validate_workflow() {
        let cmd = ValidateWorkflow {
            config: PathBuf::from("test.toml"),
            file: PathBuf::from("workflow.yaml"),
            verbose: false,
        };

        assert_eq!(cmd.file, PathBuf::from("workflow.yaml"));
        assert!(!cmd.verbose);
    }
}