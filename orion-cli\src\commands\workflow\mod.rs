//! # 工作流命令模块
//!
//! 实现 Orion 工作流的管理功能，重构为模块化结构。

// 声明子模块
pub mod types;
pub mod utils;
pub mod list;
pub mod create;
pub mod execute;
pub mod lifecycle;
pub mod management;
pub mod io;
pub mod validation;
pub mod templates;

// 重新导出主要类型
pub use types::*;

use crate::error::Result;

impl WorkflowCommand {
    /// 执行工作流命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            WorkflowAction::List(cmd) => cmd.execute().await,
            WorkflowAction::Create(cmd) => cmd.execute().await,
            WorkflowAction::Run(cmd) => cmd.execute().await,
            WorkflowAction::Show(cmd) => cmd.execute().await,
            WorkflowAction::Stop(cmd) => cmd.execute().await,
            WorkflowAction::Pause(cmd) => cmd.execute().await,
            WorkflowAction::Resume(cmd) => cmd.execute().await,
            WorkflowAction::Delete(cmd) => cmd.execute().await,
            WorkflowAction::Import(cmd) => cmd.execute().await,
            WorkflowAction::Export(cmd) => cmd.execute().await,
            WorkflowAction::Validate(cmd) => cmd.execute().await,
            WorkflowAction::Template(cmd) => cmd.execute().await,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_workflow_command_creation() {
        let list_cmd = ListWorkflows {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            status: None,
            instances: false,
            limit: None,
        };

        let workflow_cmd = WorkflowCommand {
            action: WorkflowAction::List(list_cmd),
        };

        // 测试基本结构
        match &workflow_cmd.action {
            WorkflowAction::List(cmd) => {
                assert_eq!(cmd.format, "table");
                assert!(!cmd.instances);
            }
            _ => panic!("Expected List action"),
        }
    }

    #[tokio::test]
    async fn test_create_workflow_command() {
        let create_cmd = CreateWorkflow {
            config: PathBuf::from("test.toml"),
            name: "测试工作流".to_string(),
            description: Some("测试描述".to_string()),
            file: None,
            interactive: false,
            template: Some("simple".to_string()),
        };

        assert_eq!(create_cmd.name, "测试工作流");
        assert_eq!(create_cmd.template, Some("simple".to_string()));
    }

    #[tokio::test]
    async fn test_run_workflow_command() {
        let run_cmd = RunWorkflow {
            config: PathBuf::from("test.toml"),
            workflow: "test-workflow".to_string(),
            input: Some(r#"{"key": "value"}"#.to_string()),
            input_file: None,
            async_run: false,
            monitor: true,
            format: "text".to_string(),
            timeout: Some(300),
        };

        assert_eq!(run_cmd.workflow, "test-workflow");
        assert!(run_cmd.monitor);
        assert_eq!(run_cmd.timeout, Some(300));
    }

    #[test]
    fn test_workflow_action_variants() {
        // 测试所有 WorkflowAction 变体都存在
        let actions = vec![
            "List", "Create", "Run", "Show", "Stop", "Pause", 
            "Resume", "Delete", "Import", "Export", "Validate", "Template"
        ];

        // 这个测试确保我们没有遗漏任何命令类型
        assert_eq!(actions.len(), 12);
    }

    #[test]
    fn test_template_workflow_command() {
        let template_cmd = TemplateWorkflow {
            template_type: "simple".to_string(),
            output: None,
            name: Some("测试模板".to_string()),
        };

        assert_eq!(template_cmd.template_type, "simple");
        assert_eq!(template_cmd.name, Some("测试模板".to_string()));
    }
}