# Orion 开发环境配置脚本

# 添加到 ~/.bashrc 或 ~/.zshrc

# Orion 项目路径（请修改为你的实际路径）
export ORION_ROOT="E:/Orion"

# 开发阶段的 orion 命令
orion() {
    local cache_dir="$ORION_ROOT/.dev-cache"
    local binary_path="$ORION_ROOT/target/debug/orion"
    local last_build_file="$cache_dir/last_build_hash"
    
    # 创建缓存目录
    mkdir -p "$cache_dir"
    
    # 计算源码哈希
    local current_hash=$(find "$ORION_ROOT/orion-cli/src" "$ORION_ROOT/orion-core/src" \
                        -name "*.rs" -exec cat {} \; | sha256sum | cut -d' ' -f1)
    
    # 检查是否需要重新编译
    local last_hash=""
    if [ -f "$last_build_file" ]; then
        last_hash=$(cat "$last_build_file")
    fi
    
    if [ ! -f "$binary_path" ] || [ "$current_hash" != "$last_hash" ]; then
        echo "🔨 代码已更新，正在重新编译..."
        cd "$ORION_ROOT"
        if cargo build --bin orion; then
            echo "$current_hash" > "$last_build_file"
            echo "✅ 编译完成"
        else
            echo "❌ 编译失败"
            return 1
        fi
    fi
    
    # 执行命令
    "$binary_path" "$@"
}

# 快速导航到 Orion 项目
alias cdo="cd $ORION_ROOT"

# 开发服务器
alias orion-dev="$ORION_ROOT/scripts/dev-server.sh"

# 清理缓存
alias orion-clean="rm -rf $ORION_ROOT/.dev-cache $ORION_ROOT/target"

echo "🚀 Orion 开发环境已配置"
echo "💡 使用 'orion' 命令运行（自动编译）"
echo "💡 使用 'orion-dev' 启动热重载服务器"
echo "💡 使用 'cdo' 快速进入项目目录"