//! # 消息处理器模块
//!
//! 本模块定义了消息处理器的接口和一些常用的处理器实现。
//! 消息处理器是消息系统的核心组件，负责处理接收到的消息并生成响应。
//! 通过实现 MessageHandler trait，可以创建自定义的消息处理逻辑。

use crate::error::Result;
use super::types::{Message, MessagePayload, MessagePriority};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

/// 消息处理器 trait
/// 
/// 定义了消息处理器的标准接口，所有消息处理器都必须实现此 trait。
/// 处理器可以是有状态的，支持异步处理，并且可以返回响应消息。
#[async_trait::async_trait]
pub trait MessageHandler: Send + Sync {
    /// 处理接收到的消息
    /// 
    /// 这是消息处理器的核心方法，负责处理传入的消息。
    /// 处理器可以选择返回响应消息，也可以不返回任何响应。
    /// 
    /// # 参数
    /// 
    /// * `message` - 需要处理的消息
    /// 
    /// # 返回值
    /// 
    /// * `Ok(Some(message))` - 处理成功并返回响应消息
    /// * `Ok(None)` - 处理成功但无响应消息
    /// * `Err(error)` - 处理失败
    async fn handle_message(&self, message: Message) -> Result<Option<Message>>;
    
    /// 获取处理器 ID
    /// 
    /// 返回处理器的唯一标识符，用于日志记录和调试
    /// 
    /// # 返回值
    /// 
    /// 返回处理器的字符串标识符
    fn handler_id(&self) -> String;
    
    /// 检查处理器是否可以处理指定类型的消息
    /// 
    /// 默认实现返回 true，表示可以处理所有类型的消息。
    /// 子类可以重写此方法来实现消息类型过滤。
    /// 
    /// # 参数
    /// 
    /// * `message` - 要检查的消息
    /// 
    /// # 返回值
    /// 
    /// 如果可以处理返回 true，否则返回 false
    fn can_handle(&self, _message: &Message) -> bool {
        true
    }
    
    /// 获取处理器的优先级
    /// 
    /// 用于确定多个处理器的执行顺序，数值越大优先级越高。
    /// 默认优先级为 0。
    /// 
    /// # 返回值
    /// 
    /// 返回处理器的优先级数值
    fn priority(&self) -> i32 {
        0
    }
}

/// 回声处理器
/// 
/// 简单的测试处理器，将接收到的文本消息原样返回。
/// 主要用于测试和调试目的。
pub struct EchoHandler {
    id: String,
}

impl EchoHandler {
    /// 创建新的回声处理器
    /// 
    /// # 参数
    /// 
    /// * `id` - 处理器标识符
    /// 
    /// # 返回值
    /// 
    /// 返回新的回声处理器实例
    pub fn new(id: String) -> Self {
        Self { id }
    }
}

#[async_trait::async_trait]
impl MessageHandler for EchoHandler {
    async fn handle_message(&self, message: Message) -> Result<Option<Message>> {
        // 只处理文本消息
        if let MessagePayload::Text(text) = &message.payload {
            let response = Message::new(
                message.to.clone(), // 交换发送者和接收者
                message.from.clone(),
                MessagePayload::Text(format!("Echo: {}", text)),
            );
            
            tracing::debug!("回声处理器 {} 处理消息: {}", self.id, text);
            Ok(Some(response))
        } else {
            Ok(None)
        }
    }
    
    fn handler_id(&self) -> String {
        self.id.clone()
    }
    
    fn can_handle(&self, message: &Message) -> bool {
        matches!(message.payload, MessagePayload::Text(_))
    }
}

/// 日志处理器
/// 
/// 记录所有接收到的消息到日志系统，不返回响应。
/// 用于消息审计和调试。
pub struct LoggingHandler {
    id: String,
    log_level: String,
}

impl LoggingHandler {
    /// 创建新的日志处理器
    /// 
    /// # 参数
    /// 
    /// * `id` - 处理器标识符
    /// * `log_level` - 日志级别（"debug", "info", "warn", "error"）
    /// 
    /// # 返回值
    /// 
    /// 返回新的日志处理器实例
    pub fn new(id: String, log_level: String) -> Self {
        Self { id, log_level }
    }
}

#[async_trait::async_trait]
impl MessageHandler for LoggingHandler {
    async fn handle_message(&self, message: Message) -> Result<Option<Message>> {
        let log_msg = format!(
            "消息处理 [{}]: {} -> {}, 优先级: {:?}, ID: {}",
            self.log_level,
            message.from,
            message.to,
            message.priority,
            message.id
        );
        
        match self.log_level.as_str() {
            "debug" => tracing::debug!("{}", log_msg),
            "info" => tracing::info!("{}", log_msg),
            "warn" => tracing::warn!("{}", log_msg),
            "error" => tracing::error!("{}", log_msg),
            _ => tracing::info!("{}", log_msg),
        }
        
        Ok(None) // 日志处理器不返回响应
    }
    
    fn handler_id(&self) -> String {
        self.id.clone()
    }
    
    fn priority(&self) -> i32 {
        -1 // 低优先级，确保在其他处理器之后执行
    }
}

/// 路由处理器
/// 
/// 根据消息内容或元数据将消息路由到不同的子处理器。
/// 支持基于规则的消息分发。
pub struct RoutingHandler {
    id: String,
    routes: Arc<RwLock<HashMap<String, Box<dyn MessageHandler>>>>,
    default_handler: Option<Box<dyn MessageHandler>>,
}

impl RoutingHandler {
    /// 创建新的路由处理器
    /// 
    /// # 参数
    /// 
    /// * `id` - 处理器标识符
    /// 
    /// # 返回值
    /// 
    /// 返回新的路由处理器实例
    pub fn new(id: String) -> Self {
        Self {
            id,
            routes: Arc::new(RwLock::new(HashMap::new())),
            default_handler: None,
        }
    }
    
    /// 添加路由规则
    /// 
    /// # 参数
    /// 
    /// * `route_key` - 路由键（通常是消息类型或目标）
    /// * `handler` - 对应的处理器
    pub async fn add_route(&self, route_key: String, handler: Box<dyn MessageHandler>) {
        let mut routes = self.routes.write().await;
        routes.insert(route_key, handler);
    }
    
    /// 设置默认处理器
    /// 
    /// 当没有匹配的路由规则时使用的处理器
    /// 
    /// # 参数
    /// 
    /// * `handler` - 默认处理器
    pub fn set_default_handler(&mut self, handler: Box<dyn MessageHandler>) {
        self.default_handler = Some(handler);
    }
    
    /// 确定消息的路由键
    /// 
    /// 根据消息内容确定应该使用哪个路由键
    /// 
    /// # 参数
    /// 
    /// * `message` - 要路由的消息
    /// 
    /// # 返回值
    /// 
    /// 返回路由键，如果无法确定则返回 None
    fn determine_route_key(&self, message: &Message) -> Option<String> {
        // 优先使用元数据中的路由信息
        if let Some(route) = message.metadata.get("route") {
            return Some(route.clone());
        }
        
        // 根据消息载荷类型确定路由
        match &message.payload {
            MessagePayload::TaskRequest { .. } => Some("task".to_string()),
            MessagePayload::TaskResponse { .. } => Some("task_response".to_string()),
            MessagePayload::WorkflowControl { .. } => Some("workflow".to_string()),
            MessagePayload::SteeringCommand { .. } => Some("steering".to_string()),
            MessagePayload::StatusUpdate { .. } => Some("status".to_string()),
            MessagePayload::LogMessage { .. } => Some("log".to_string()),
            MessagePayload::Text(_) => Some("text".to_string()),
            MessagePayload::Custom { message_type, .. } => Some(message_type.clone()),
        }
    }
}

#[async_trait::async_trait]
impl MessageHandler for RoutingHandler {
    async fn handle_message(&self, message: Message) -> Result<Option<Message>> {
        let route_key = self.determine_route_key(&message);
        
        if let Some(key) = route_key {
            let routes = self.routes.read().await;
            if let Some(handler) = routes.get(&key) {
                tracing::debug!("路由处理器 {} 将消息路由到: {}", self.id, key);
                return handler.handle_message(message).await;
            }
        }
        
        // 使用默认处理器
        if let Some(ref default_handler) = self.default_handler {
            tracing::debug!("路由处理器 {} 使用默认处理器", self.id);
            default_handler.handle_message(message).await
        } else {
            tracing::warn!("路由处理器 {} 无法路由消息，且无默认处理器", self.id);
            Ok(None)
        }
    }
    
    fn handler_id(&self) -> String {
        self.id.clone()
    }
    
    fn priority(&self) -> i32 {
        10 // 高优先级，确保路由处理器优先执行
    }
}

/// 过滤处理器
/// 
/// 根据指定条件过滤消息，只处理符合条件的消息。
/// 可以基于优先级、发送者、消息类型等进行过滤。
pub struct FilterHandler {
    id: String,
    inner_handler: Box<dyn MessageHandler>,
    min_priority: Option<MessagePriority>,
    allowed_senders: Option<Vec<String>>,
}

impl FilterHandler {
    /// 创建新的过滤处理器
    /// 
    /// # 参数
    /// 
    /// * `id` - 处理器标识符
    /// * `inner_handler` - 内部处理器
    /// 
    /// # 返回值
    /// 
    /// 返回新的过滤处理器实例
    pub fn new(id: String, inner_handler: Box<dyn MessageHandler>) -> Self {
        Self {
            id,
            inner_handler,
            min_priority: None,
            allowed_senders: None,
        }
    }
    
    /// 设置最小优先级过滤
    /// 
    /// # 参数
    /// 
    /// * `priority` - 最小优先级
    pub fn with_min_priority(mut self, priority: MessagePriority) -> Self {
        self.min_priority = Some(priority);
        self
    }
    
    /// 设置允许的发送者列表
    /// 
    /// # 参数
    /// 
    /// * `senders` - 允许的发送者 ID 列表
    pub fn with_allowed_senders(mut self, senders: Vec<String>) -> Self {
        self.allowed_senders = Some(senders);
        self
    }
    
    /// 检查消息是否通过过滤条件
    /// 
    /// # 参数
    /// 
    /// * `message` - 要检查的消息
    /// 
    /// # 返回值
    /// 
    /// 如果消息通过过滤返回 true，否则返回 false
    fn passes_filter(&self, message: &Message) -> bool {
        // 检查优先级过滤
        if let Some(min_priority) = self.min_priority {
            if message.priority < min_priority {
                return false;
            }
        }
        
        // 检查发送者过滤
        if let Some(ref allowed_senders) = self.allowed_senders {
            if !allowed_senders.contains(&message.from) {
                return false;
            }
        }
        
        true
    }
}

#[async_trait::async_trait]
impl MessageHandler for FilterHandler {
    async fn handle_message(&self, message: Message) -> Result<Option<Message>> {
        if self.passes_filter(&message) {
            tracing::debug!("过滤处理器 {} 允许消息通过", self.id);
            self.inner_handler.handle_message(message).await
        } else {
            tracing::debug!("过滤处理器 {} 拒绝消息", self.id);
            Ok(None)
        }
    }
    
    fn handler_id(&self) -> String {
        self.id.clone()
    }
    
    fn can_handle(&self, message: &Message) -> bool {
        self.passes_filter(message) && self.inner_handler.can_handle(message)
    }
    
    fn priority(&self) -> i32 {
        self.inner_handler.priority()
    }
}
