//! # Agent 列出功能
//!
//! 实现列出 Agent 的相关功能。

use crate::error::Result;
use crate::commands::agent::{types::ListAgents, utils::*};
use orion_core::agent::AgentState;

impl ListAgents {
    /// 执行列出 Agent
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        // 获取 Agent 列表（当前使用模拟数据）
        let mut agents = get_mock_agents();
        
        // 根据状态过滤
        if let Some(status_filter) = &self.status {
            agents.retain(|agent| {
                match status_filter.as_str() {
                    "idle" => matches!(agent.state, AgentState::Idle),
                    "busy" => matches!(
                        agent.state,
                        AgentState::Thinking | AgentState::ExecutingTool | AgentState::ExecutingWorkflow
                    ),
                    "paused" => matches!(agent.state, AgentState::Paused),
                    "stopped" => matches!(agent.state, AgentState::Stopped),
                    "error" => matches!(agent.state, AgentState::Error),
                    _ => true,
                }
            });
        }
        
        self.print_agents(&agents)?;
        
        Ok(())
    }
    
    /// 打印 Agent 列表
    fn print_agents(&self, agents: &[AgentInfo]) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(agents, &self.format)?;
            }
            "table" | _ => {
                if agents.is_empty() {
                    println!("没有找到 Agent");
                    return Ok(());
                }
                
                if self.verbose {
                    self.print_verbose_agents(agents);
                } else {
                    self.print_table_agents(agents);
                }
                
                if self.stats {
                    self.print_stats(agents);
                }
            }
        }
        
        Ok(())
    }
    
    /// 打印详细模式
    fn print_verbose_agents(&self, agents: &[AgentInfo]) {
        for agent in agents {
            println!("🤖 {}", agent.name);
            println!("   描述: {}", agent.description);
            println!("   状态: {}", format_agent_state(&agent.state));
            println!("   模型: {}", agent.model);
            println!("   运行时间: {}", format_duration(&agent.uptime));
            println!("   完成任务: {} 个", agent.tasks_completed);
            println!("   内存使用: {:.1} MB", agent.memory_usage);
            println!();
        }
    }
    
    /// 打印表格模式
    fn print_table_agents(&self, agents: &[AgentInfo]) {
        println!("{:<15} {:<10} {:<15} {:<30}", "名称", "状态", "模型", "描述");
        println!("{}", "-".repeat(75));
        
        for agent in agents {
            println!(
                "{:<15} {:<10} {:<15} {:<30}",
                agent.name,
                format_agent_state(&agent.state),
                agent.model,
                truncate_string(&agent.description, 27)
            );
        }
    }
    
    /// 打印统计信息
    fn print_stats(&self, agents: &[AgentInfo]) {
        println!();
        println!("📊 统计信息:");
        let total = agents.len();
        let running = agents
            .iter()
            .filter(|a| {
                matches!(
                    a.state,
                    AgentState::ExecutingTool | AgentState::Thinking | AgentState::ExecutingWorkflow
                )
            })
            .count();
        let idle = agents
            .iter()
            .filter(|a| matches!(a.state, AgentState::Idle))
            .count();
        let total_tasks: u64 = agents.iter().map(|a| a.tasks_completed).sum();
        let total_memory: f64 = agents.iter().map(|a| a.memory_usage).sum();
        
        println!("  总数: {} 个", total);
        println!("  运行中: {} 个", running);
        println!("  空闲: {} 个", idle);
        println!("  总任务数: {} 个", total_tasks);
        println!("  总内存使用: {:.1} MB", total_memory);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_list_agents_basic() {
        let cmd = ListAgents {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            verbose: false,
            status: None,
            stats: false,
        };

        // 测试基本参数
        assert_eq!(cmd.format, "table");
        assert!(!cmd.verbose);
        assert!(!cmd.stats);
    }

    #[test]
    fn test_print_table_agents() {
        let cmd = ListAgents {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            verbose: false,
            status: None,
            stats: false,
        };
        
        let agents = get_mock_agents();
        
        // 这个测试主要确保函数不会 panic
        cmd.print_table_agents(&agents);
    }

    #[test]
    fn test_print_verbose_agents() {
        let cmd = ListAgents {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            verbose: true,
            status: None,
            stats: false,
        };
        
        let agents = get_mock_agents();
        
        // 这个测试主要确保函数不会 panic
        cmd.print_verbose_agents(&agents);
    }

    #[test]
    fn test_print_stats() {
        let cmd = ListAgents {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            verbose: false,
            status: None,
            stats: true,
        };
        
        let agents = get_mock_agents();
        
        // 这个测试主要确保函数不会 panic
        cmd.print_stats(&agents);
    }
}