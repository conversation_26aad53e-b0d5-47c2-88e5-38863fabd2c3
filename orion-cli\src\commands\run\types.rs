//! # 运行命令类型定义
//!
//! 定义运行模块中使用的命令结构和类型。

use clap::Args;
use orion_core::{
    context::ContextManager,
    llm::LlmEngine,
    message::MessageBus,
    tools::ToolRegistry,
    workflow::WorkflowManager,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;

/// 运行命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct RunCommand {
    /// 配置文件路径
    #[arg(short, long, value_name = "FILE")]
    pub config: Option<String>,
    
    /// Agent 名称
    #[arg(short, long, default_value = "orion-agent")]
    pub name: String,
    
    /// 是否启用交互模式
    #[arg(short, long, default_value_t = true)]
    pub interactive: bool,
    
    /// 是否启用详细输出
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 日志级别
    #[arg(long, default_value = "info")]
    pub log_level: String,
    
    /// 是否启用沙箱
    #[arg(long, default_value_t = true)]
    pub sandbox: bool,
    
    /// 最大并发任务数
    #[arg(long, default_value_t = 10)]
    pub max_concurrent_tasks: usize,
    
    /// 工作目录
    #[arg(short, long)]
    pub workdir: Option<String>,
    
    /// 执行单个命令后退出
    #[arg(long)]
    pub command: Option<String>,
    
    /// 从文件读取命令
    #[arg(long)]
    pub file: Option<String>,
    
    /// 输出格式
    #[arg(long, default_value = "text", value_parser = ["text", "json", "yaml"])]
    pub output_format: String,

    /// 是否启用流式响应（默认启用）
    #[arg(long, default_value_t = true)]
    pub stream: bool,

    /// 禁用流式响应
    #[arg(long = "no-stream", action = clap::ArgAction::SetTrue)]
    pub no_stream: bool,

    /// 打字机效果速度（毫秒/字符，0表示无延迟）
    #[arg(long, default_value = "30")]
    pub typing_speed: u64,
}

/// Agent 组件
#[allow(dead_code)]
pub struct AgentComponents {
    #[allow(dead_code)]
    pub message_bus: Arc<MessageBus>,
    #[allow(dead_code)]
    pub llm_engine: Arc<LlmEngine>,
    #[allow(dead_code)]
    pub tool_registry: Arc<ToolRegistry>,
    #[allow(dead_code)]
    pub context_manager: Arc<ContextManager>,
    #[allow(dead_code)]
    pub workflow_manager: Arc<WorkflowManager>,
}

/// 运行模式
#[derive(Debug, Clone)]
pub enum RunMode {
    /// 单命令模式
    SingleCommand(String),
    /// 文件批处理模式
    FileExecution(String),
    /// 交互模式
    Interactive,
    /// 守护进程模式
    Daemon,
}

impl RunCommand {
    /// 确定运行模式
    pub fn determine_run_mode(&self) -> RunMode {
        if let Some(command) = &self.command {
            RunMode::SingleCommand(command.clone())
        } else if let Some(file_path) = &self.file {
            RunMode::FileExecution(file_path.clone())
        } else if self.interactive {
            RunMode::Interactive
        } else {
            RunMode::Daemon
        }
    }

    /// 检查是否启用流式模式
    pub fn is_stream_enabled(&self) -> bool {
        self.stream && !self.no_stream
    }
}