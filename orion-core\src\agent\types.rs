//! # Agent 类型定义模块
//!
//! 定义Agent系统中使用的所有核心类型、枚举和结构体，
//! 包括状态、任务、响应、优先级等类型定义。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use uuid::Uuid;

/// Agent 状态枚举
/// 
/// 定义Agent在运行过程中的各种状态，用于状态管理和监控
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AgentState {
    /// 空闲状态 - Agent等待新任务
    Idle,
    /// 思考中 - Agent正在分析和规划任务
    Thinking,
    /// 执行工具 - Agent正在调用外部工具
    ExecutingTool,
    /// 等待用户输入 - Agent需要用户提供更多信息
    WaitingForInput,
    /// 执行工作流 - Agent正在执行复杂的工作流程
    ExecutingWorkflow,
    /// 错误状态 - Agent遇到错误需要处理
    Error,
    /// 暂停状态 - Agent被用户暂停
    Paused,
    /// 停止状态 - Agent已停止运行
    Stopped,
}

/// 任务类型枚举
/// 
/// 定义Agent可以处理的不同类型的任务
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TaskType {
    /// 问答任务 - 回答用户问题
    Question,
    /// 代码生成 - 生成代码片段或完整程序
    CodeGeneration,
    /// 文档分析 - 分析和处理文档内容
    DocumentAnalysis,
    /// 数据处理 - 处理和分析数据
    DataProcessing,
    /// 工作流执行 - 执行预定义的工作流
    WorkflowExecution,
    /// 工具调用 - 调用特定的工具或服务
    ToolInvocation,
    /// 自定义任务 - 用户定义的特殊任务类型
    Custom(String),
}

/// 任务优先级枚举
/// 
/// 定义任务的优先级，用于任务调度和资源分配
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    /// 低优先级 - 可以延后处理的任务
    Low,
    /// 普通优先级 - 标准优先级任务
    Normal,
    /// 高优先级 - 需要优先处理的任务
    High,
    /// 紧急优先级 - 需要立即处理的任务
    Urgent,
}

/// 响应类型枚举
/// 
/// 定义Agent响应的不同类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResponseType {
    /// 文本响应 - 纯文本回复
    Text,
    /// 代码响应 - 包含代码的回复
    Code,
    /// 文件响应 - 生成或处理文件的回复
    File,
    /// 工具调用响应 - 工具执行结果
    ToolCall,
    /// 工作流响应 - 工作流执行结果
    Workflow,
    /// 错误响应 - 错误信息
    Error,
}

/// 任务请求结构体
/// 
/// 包含用户提交给Agent的任务的所有信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskRequest {
    /// 任务唯一标识符
    pub id: Uuid,
    /// 任务内容描述
    pub content: String,
    /// 任务类型
    pub task_type: TaskType,
    /// 会话ID，用于关联对话上下文
    pub session_id: Option<String>,
    /// 用户ID，用于用户管理和权限控制
    pub user_id: Option<String>,
    /// 任务优先级
    pub priority: TaskPriority,
    /// 任务参数，包含任务执行所需的额外参数
    pub parameters: HashMap<String, serde_json::Value>,
    /// 上下文信息，包含任务相关的背景信息
    pub context: HashMap<String, serde_json::Value>,
    /// 任务创建时间
    pub created_at: SystemTime,
    /// 任务截止时间（可选）
    pub deadline: Option<SystemTime>,
}

/// 任务响应结构体
/// 
/// 包含Agent处理任务后返回的所有信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskResponse {
    /// 对应的任务ID
    pub task_id: Uuid,
    /// 响应内容
    pub content: String,
    /// 响应类型
    pub response_type: ResponseType,
    /// 任务是否成功完成
    pub success: bool,
    /// 错误信息（如果有）
    pub error: Option<String>,
    /// 工具调用结果列表
    pub tool_results: Vec<crate::tools::ToolResult>,
    /// 生成的文件路径列表
    pub generated_files: Vec<String>,
    /// 元数据，包含额外的响应信息
    pub metadata: HashMap<String, serde_json::Value>,
    /// 处理时间（毫秒）
    pub processing_time_ms: u64,
    /// 令牌使用统计
    pub token_usage: Option<crate::llm::TokenUsage>,
    /// 响应创建时间
    pub created_at: SystemTime,
}

/// 思考步骤类型枚举
/// 
/// 定义Agent思考过程中的不同步骤类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ThinkingStepType {
    /// 问题分析 - 分析用户问题和需求
    ProblemAnalysis,
    /// 策略规划 - 制定解决方案策略
    StrategyPlanning,
    /// 工具选择 - 选择合适的工具
    ToolSelection,
    /// 参数准备 - 准备工具调用参数
    ParameterPreparation,
    /// 结果评估 - 评估执行结果
    ResultEvaluation,
    /// 错误分析 - 分析错误原因
    ErrorAnalysis,
    /// 决策制定 - 做出最终决策
    DecisionMaking,
}

/// 思考步骤结构体
/// 
/// 记录Agent思考过程中的单个步骤
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThinkingStep {
    /// 步骤唯一标识符
    pub id: Uuid,
    /// 步骤类型
    pub step_type: ThinkingStepType,
    /// 步骤内容描述
    pub content: String,
    /// 置信度（0.0-1.0）
    pub confidence: f32,
    /// 步骤时间戳
    pub timestamp: SystemTime,
}

/// 思考过程结构体
/// 
/// 记录Agent完整的思考过程
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ThinkingProcess {
    /// 思考步骤列表
    pub steps: Vec<ThinkingStep>,
    /// 思考开始时间
    pub started_at: SystemTime,
    /// 思考完成时间（可选）
    pub completed_at: Option<SystemTime>,
    /// 总思考时间（毫秒）
    pub total_time_ms: u64,
}

/// Agent统计信息结构体
/// 
/// 记录Agent的运行统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentStats {
    /// 处理的任务总数
    pub total_tasks: u64,
    /// 成功的任务数
    pub successful_tasks: u64,
    /// 失败的任务数
    pub failed_tasks: u64,
    /// 工具调用总数
    pub total_tool_calls: u64,
    /// 成功的工具调用数
    pub successful_tool_calls: u64,
    /// 总处理时间（毫秒）
    pub total_processing_time_ms: u64,
    /// 平均处理时间（毫秒）
    pub average_processing_time_ms: u64,
    /// 总令牌使用数
    pub total_tokens_used: u64,
    /// Agent启动时间
    pub started_at: SystemTime,
    /// 最后活动时间
    pub last_activity_at: SystemTime,
}

impl Default for AgentStats {
    /// 创建默认的Agent统计信息
    fn default() -> Self {
        let now = SystemTime::now();
        Self {
            total_tasks: 0,
            successful_tasks: 0,
            failed_tasks: 0,
            total_tool_calls: 0,
            successful_tool_calls: 0,
            total_processing_time_ms: 0,
            average_processing_time_ms: 0,
            total_tokens_used: 0,
            started_at: now,
            last_activity_at: now,
        }
    }
}
