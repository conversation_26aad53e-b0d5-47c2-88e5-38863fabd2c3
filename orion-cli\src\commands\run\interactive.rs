//! # 交互模式实现
//!
//! 实现Agent的交互式命令行界面。

use crate::error::{CliError, Result};
use crate::commands::run::{types::RunCommand, utils};
use crate::ui::{
    UIManager,
    components::{Footer, StatusIndicator, LoadingIndicator},
    constants::{status_indicators, help_text},
    utils::{supports_color, is_tty},
};
use orion_core::agent::Agent;
use uuid::Uuid;
use crossterm::{
    execute,
    terminal::{Clear, ClearType},
    cursor::MoveTo,
};
use std::io::stdout;

impl RunCommand {
    /// 运行交互模式
    pub async fn run_interactive_mode(&mut self, agent: &Agent) -> Result<()> {
        // 初始化 UI 管理器
        let mut ui_manager = UIManager::new()
            .map_err(|e| CliError::InitializationError { 
                error: format!("初始化 UI 失败: {}", e) 
            })?;
            
        ui_manager.init_terminal()
            .map_err(|e| CliError::InitializationError { 
                error: format!("初始化终端失败: {}", e) 
            })?;

        // 显示欢迎信息
        self.show_welcome_message(&ui_manager)?;
        
        let mut rl = utils::create_readline_editor().await?;
        let history_file = utils::get_history_file_path();
        
        // 加载历史记录
        if let Some(ref history_path) = history_file {
            let _ = rl.load_history(history_path);
        }
        
        let session_id = Uuid::new_v4().to_string();
        let mut ctrl_c_count = 0;
        let mut last_ctrl_c_time = std::time::Instant::now();
        
        loop {
            let prompt = self.create_prompt(&session_id)?;
            
            match rl.readline(&prompt) {
                Ok(line) => {
                    // 重置 Ctrl+C 计数器
                    ctrl_c_count = 0;
                    
                    let input = line.trim();
                    
                    if input.is_empty() {
                        continue;
                    }
                    
                    let _ = rl.add_history_entry(input);
                    
                    // 处理内置命令
                    if let Some(result) = self.handle_builtin_command(input, &mut ui_manager).await? {
                        if result {
                            break; // 退出命令
                        }
                        continue;
                    }
                    
                    // 显示加载指示器
                    let mut loader = LoadingIndicator::new(format!("处理请求: {}", input));
                    loader.start()
                        .map_err(|e| CliError::IoError { 
                            error: format!("启动加载指示器失败: {}", e) 
                        })?;
                    
                    // 处理用户输入
                    match self.process_user_input(input, agent, &mut loader).await {
                        Ok(response) => {
                            // 在非流式模式下才清除加载指示器，流式模式下已经在 process_user_input 中处理
                            if !self.is_stream_enabled() {
                                loader.stop_and_clear()
                                    .map_err(|e| CliError::IoError { 
                                        error: format!("清除加载指示器失败: {}", e) 
                                    })?;
                            }
                            
                            StatusIndicator::success("请求处理完成")
                                .map_err(|e| CliError::IoError { 
                                    error: format!("显示成功状态失败: {}", e) 
                                })?;
                            
                            // 只有在非流式模式或者响应不为空时才显示
                            if !response.is_empty() {
                                println!("{}", response);
                            }
                        }
                        Err(e) => {
                            loader.stop_and_clear()
                                .map_err(|e| CliError::IoError { 
                                    error: format!("清除加载指示器失败: {}", e) 
                                })?;
                            
                            StatusIndicator::error(&format!("处理失败: {}", e))
                                .map_err(|e| CliError::IoError { 
                                    error: format!("显示错误状态失败: {}", e) 
                                })?;
                        }
                    }
                }
                Err(rustyline::error::ReadlineError::Interrupted) => {
                    let now = std::time::Instant::now();
                    if now.duration_since(last_ctrl_c_time).as_secs() < 2 {
                        ctrl_c_count += 1;
                    } else {
                        ctrl_c_count = 1;
                    }
                    last_ctrl_c_time = now;
                    
                    if ctrl_c_count >= 2 {
                        StatusIndicator::info("强制退出...")
                            .map_err(|e| CliError::IoError { 
                                error: format!("显示信息状态失败: {}", e) 
                            })?;
                        break;
                    } else {
                        StatusIndicator::warning("再次按 Ctrl+C 强制退出")
                            .map_err(|e| CliError::IoError { 
                                error: format!("显示警告状态失败: {}", e) 
                            })?;
                    }
                }
                Err(rustyline::error::ReadlineError::Eof) => {
                    StatusIndicator::info("收到 EOF，退出...")
                        .map_err(|e| CliError::IoError { 
                            error: format!("显示信息状态失败: {}", e) 
                        })?;
                    break;
                }
                Err(err) => {
                    StatusIndicator::error(&format!("输入错误: {}", err))
                        .map_err(|e| CliError::IoError { 
                            error: format!("显示错误状态失败: {}", e) 
                        })?;
                }
            }
        }
        
        // 保存历史记录
        if let Some(ref history_path) = history_file {
            let _ = rl.save_history(history_path);
        }
        
        // 显示退出信息
        self.show_goodbye_message(&ui_manager)?;
        
        // 清理终端
        ui_manager.cleanup_terminal()
            .map_err(|e| CliError::SystemError { 
                error: format!("清理终端失败: {}", e) 
            })?;
        
        Ok(())
    }
    
    /// 显示欢迎信息
    fn show_welcome_message(&self, ui_manager: &UIManager) -> Result<()> {
        let (width, _) = ui_manager.get_terminal_size();
        
        StatusIndicator::success("Orion Agent 交互模式启动")
            .map_err(|e| CliError::IoError { 
                error: format!("显示欢迎信息失败: {}", e) 
            })?;
        
        // 显示流式模式提示
        if self.is_stream_enabled() {
            self.print_stream_mode_info();
        }
        
        // 显示底部帮助信息
        let footer = Footer::new(width);
        footer.render(Some("准备就绪"))
            .map_err(|e| CliError::IoError { 
                error: format!("渲染底部信息失败: {}", e) 
            })?;
        
        Ok(())
    }
    
    /// 显示告别信息
    fn show_goodbye_message(&self, _ui_manager: &UIManager) -> Result<()> {
        
        execute!(
            stdout(),
            Clear(ClearType::All),
            MoveTo(0, 0)
        ).map_err(|e| CliError::IoError { 
            error: format!("清屏失败: {}", e) 
        })?;
        
        StatusIndicator::info(&format!("{} 感谢使用 Orion Agent 系统！", status_indicators::WAVE))
            .map_err(|e| CliError::IoError { 
                error: format!("显示告别信息失败: {}", e) 
            })?;
        
        println!("🔗 项目主页: https://github.com/orion-ai/orion");
        println!("📧 问题反馈: https://github.com/orion-ai/orion/issues");
        println!();
        
        Ok(())
    }
    
    /// 创建提示符
    fn create_prompt(&self, session_id: &str) -> Result<String> {
        if supports_color() && is_tty() {
            Ok(format!(
                "{}orion{}[{}{}{}]{}> ",
                "\x1b[36m",      // cyan
                "\x1b[0m",       // reset
                "\x1b[33m",      // yellow
                &session_id[..8],
                "\x1b[0m",       // reset
                "\x1b[32m"       // green
            ))
        } else {
            Ok(format!("orion[{}]> ", &session_id[..8]))
        }
    }
    
    /// 处理内置命令
    async fn handle_builtin_command(&mut self, input: &str, ui_manager: &mut UIManager) -> Result<Option<bool>> {
        match input.to_lowercase().trim() {
            "help" | "?" => {
                self.show_help()?;
                Ok(Some(false))
            }
            "exit" | "quit" | "q" => {
                StatusIndicator::info("正在退出...")
                    .map_err(|e| CliError::IoError { 
                        error: format!("显示退出信息失败: {}", e) 
                    })?;
                Ok(Some(true))
            }
            "clear" | "cls" => {
                ui_manager.clear_screen()
                    .map_err(|e| CliError::IoError { 
                        error: format!("清屏失败: {}", e) 
                    })?;
                Ok(Some(false))
            }
            "version" | "ver" => {
                self.show_version()?;
                Ok(Some(false))
            }
            "status" => {
                self.show_status()?;
                Ok(Some(false))
            }
            "theme" => {
                self.show_theme_info(ui_manager)?;
                Ok(Some(false))
            }
            _ => Ok(None)
        }
    }
    
    /// 显示帮助信息
    fn show_help(&self) -> Result<()> {
        StatusIndicator::info("显示帮助信息")
            .map_err(|e| CliError::IoError { 
                error: format!("显示帮助标题失败: {}", e) 
            })?;
        
        println!("{}", help_text::FULL_HELP);
        Ok(())
    }
    
    /// 显示版本信息
    fn show_version(&self) -> Result<()> {
        StatusIndicator::info(&format!("Orion CLI v{}", env!("CARGO_PKG_VERSION")))
            .map_err(|e| CliError::IoError { 
                error: format!("显示版本信息失败: {}", e) 
            })?;
        
        println!("构建时间: {}", env!("CARGO_PKG_VERSION"));
        println!("Rust 版本: {}", env!("CARGO_PKG_RUST_VERSION"));
        Ok(())
    }
    
    /// 显示状态信息
    fn show_status(&self) -> Result<()> {
        StatusIndicator::info("系统状态")
            .map_err(|e| CliError::IoError { 
                error: format!("显示状态标题失败: {}", e) 
            })?;
        
        println!("• 流式模式: {}", if self.is_stream_enabled() { "启用" } else { "禁用" });
        println!("• 最大并发任务: {}", self.max_concurrent_tasks);
        println!("• 沙盒模式: {}", if self.sandbox { "启用" } else { "禁用" });
        println!("• 详细输出: {}", if self.verbose { "启用" } else { "禁用" });
        Ok(())
    }
    
    /// 显示主题信息
    fn show_theme_info(&self, ui_manager: &UIManager) -> Result<()> {
        StatusIndicator::info("当前主题信息")
            .map_err(|e| CliError::IoError { 
                error: format!("显示主题标题失败: {}", e) 
            })?;
        
        let theme_manager = ui_manager.theme_manager();
        let active_theme = theme_manager.get_active_theme();
        
        println!("• 主题名称: {}", active_theme.name);
        println!("• 主题类型: {:?}", active_theme.theme_type);
        println!("• 支持渐变: {}", if active_theme.gradient_colors.is_some() { "是" } else { "否" });
        
        println!("\n可用主题:");
        print!("{}", theme_manager.list_themes());
        
        Ok(())
    }
    
    /// 显示流式模式信息
    fn print_stream_mode_info(&self) {
        if self.stream {
            println!("🌊 流式模式已启用 - 响应将实时显示");
            if self.typing_speed > 0 {
                println!("⏱️  模拟打字速度: {} 字符/秒", self.typing_speed);
            }
        } else {
            println!("📋 批量模式 - 等待完整响应后显示");
        }
    }
    
    /// 处理用户输入
    async fn process_user_input(&self, input: &str, agent: &Agent, loader: &mut LoadingIndicator) -> Result<String> {
        use crate::commands::run::utils;
        
        // 创建任务请求
        let task_request = utils::create_task(input.to_string()).await;
        
        if self.is_stream_enabled() {
            // 流式模式：使用 process_task_stream
            match agent.process_task_stream(task_request).await {
                Ok(mut receiver) => {
                    let mut full_response = String::new();
                    let mut first_chunk = true;
                    
                    // 接收流式响应
                    while let Some(chunk_result) = receiver.recv().await {
                        match chunk_result {
                            Ok(chunk) => {
                                // 收到第一个chunk时清除加载指示器
                                if first_chunk {
                                    loader.stop_and_clear()
                                        .map_err(|e| CliError::IoError { 
                                            error: format!("清除加载指示器失败: {}", e) 
                                        })?;
                                    first_chunk = false;
                                }
                                
                                if self.typing_speed > 0 {
                                    // 使用打字机效果逐字符显示
                                    for ch in chunk.delta.chars() {
                                        print!("{}", ch);
                                        use std::io::Write;
                                        let _ = std::io::stdout().flush();
                                        
                                        // 根据字符类型调整延迟
                                        let delay = match ch {
                                            '!' | '？' | '。' => self.typing_speed * 3,
                                            '，' | '、' => self.typing_speed * 2,
                                            ' ' => self.typing_speed / 2,
                                            _ => self.typing_speed,
                                        };
                                        
                                        if delay > 0 {
                                            tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
                                        }
                                    }
                                } else {
                                    // 直接显示
                                    print!("{}", chunk.delta);
                                    use std::io::Write;
                                    let _ = std::io::stdout().flush();
                                }
                                full_response.push_str(&chunk.delta);
                            }
                            Err(e) => {
                                return Err(CliError::ExecutionError {
                                    error: format!("流式响应错误: {}", e),
                                });
                            }
                        }
                    }
                    
                    println!(); // 换行
                    // 在流式模式下返回空字符串，避免重复显示
                    Ok(String::new())
                }
                Err(e) => {
                    Err(CliError::ExecutionError {
                        error: format!("启动流式处理失败: {}", e),
                    })
                }
            }
        } else {
            // 非流式模式：使用 process_task
            match agent.process_task(task_request).await {
                Ok(response) => Ok(response.content),
                Err(e) => {
                    Err(CliError::ExecutionError {
                        error: format!("Agent 处理失败: {}", e),
                    })
                }
            }
        }
    }
}