//! # 命令执行功能
//!
//! 实现单个命令和文件批处理的执行功能。

use crate::error::{CliError, Result};
use crate::commands::run::{types::RunCommand, utils::*};
use orion_core::agent::Agent;

impl RunCommand {
    /// 执行单个命令
    pub async fn execute_single_command(&self, agent: &Agent, command: &str) -> Result<()> {
        let task = create_task(command.to_string()).await;
        let response = agent.process_task(task).await
            .map_err(|e| CliError::ExecutionError {
                error: format!("执行命令失败: {}", e),
            })?;
        
        self.print_response(&response)?;
        
        if !response.success {
            std::process::exit(1);
        }
        
        Ok(())
    }
    
    /// 从文件执行命令
    pub async fn execute_from_file(&self, agent: &Agent, file_path: &str) -> Result<()> {
        let lines = read_commands_from_file(file_path).await?;
        
        for (i, line) in lines.iter().enumerate() {
            println!("\n[{}/{}] 执行: {}", i + 1, lines.len(), line);
            
            let task = create_task(line.clone()).await;
            let response = agent.process_task(task).await
                .map_err(|e| CliError::ExecutionError {
                    error: format!("执行命令失败: {}", e),
                })?;
            
            self.print_response(&response)?;
            
            if !response.success {
                eprintln!("命令执行失败，停止执行");
                std::process::exit(1);
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_execution_config() {
        let cmd = RunCommand {
            config: None,
            name: "test-agent".to_string(),
            interactive: false,
            verbose: true,
            log_level: "debug".to_string(),
            sandbox: false,
            max_concurrent_tasks: 8,
            workdir: Some("/tmp".to_string()),
            command: Some("test command".to_string()),
            file: None,
            output_format: "json".to_string(),
            stream: false,
            typing_speed: 0,
        };
        
        assert_eq!(cmd.output_format, "json");
        assert!(cmd.verbose);
        assert!(!cmd.sandbox);
        assert_eq!(cmd.workdir, Some("/tmp".to_string()));
    }
}