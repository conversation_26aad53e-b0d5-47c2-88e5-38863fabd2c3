# Claude Code 复杂度评估机制

## 概述

Claude Code 通过 "关键词模式匹配 + 多维度加权"，实现任务复杂度的实时分级，为智能资源分配和任务调度提供决策依据。

## 核心流程与函数

| 函数 | 功能 | 作用 |
|------|------|------|
| WN5 | 提取用户消息文本 | 为复杂度分析准备"原材料" |
| FN5 | 初步分类复杂度 | 打基础标签（如高/中/基础） |
| XN5 | 正则匹配预定义模式库（YN5） | 精准映射到具体等级 |

## 复杂度等级与关键词

系统定义 4 级任务复杂度，通过关键词锚定：

| 等级 | 分值 | 典型关键词 | 思考深度要求 |
|------|------|------------|-------------|
| HIGHEST | 31999 分 | think harder、think intensely | 深度/高强度思考 |
| MIDDLE | 10000 分 | analyze、examine | 中等分析/审视 |
| BASIC | 4000 分 | search、find | 基础搜索/查找 |
| NONE | 0 分 | —— | 无特殊复杂度要求 |

## 匹配策略

### 1. 优先级匹配

从高到低依次匹配等级（先查 HIGHEST → 再 MIDDLE → 再 BASIC → 最后 NONE），匹配即停止，确保"高复杂度指令优先识别"。

### 2. 词边界控制

通过 `needsWordBoundary` 参数，决定是否要求完整词匹配（如区分 analyze 和 analyzes），避免歧义。

### 3. 综合加权

除关键词匹配，还结合：

- **指令长度、嵌套层级**
- **涉及工具数量**（多工具协同 → 复杂度高）
- **用户历史交互中的复杂度偏好**

最终通过"加权计算"输出等级。

## 流程图

```mermaid
flowchart TB
    A[用户输入] --> B[WN5 提取文本]
    B --> C[FN5 初步分类]
    C --> D[XN5 正则匹配 YN5 模式库]
    D --> E{优先级匹配<br/>(HIGHEST→MIDDLE→BASIC→NONE)}
    E -->|匹配到| F[输出对应等级+分值]
    E -->|未匹配| G[降级到下一级]
    H[指令长度/工具数/历史偏好] --> I[综合加权调整]
    F --> I
    I --> J[最终复杂度等级]
```

## 应用场景

### 高复杂度任务 (HIGHEST)
- 架构设计和重构
- 复杂算法实现
- 多系统集成
- 深度代码分析

### 中等复杂度任务 (MIDDLE)
- 功能模块开发
- 代码审查和优化
- 配置管理
- 问题诊断

### 基础复杂度任务 (BASIC)
- 文件搜索和查看
- 简单代码修改
- 状态查询
- 基础操作

## 核心价值

通过复杂度评估机制，Claude Code 实现：

- **智能分层响应**: 高复杂度任务分配更多资源（如多 Agent 并行），基础任务快速处理
- **贴合用户意图**: 关键词精准捕捉"思考深度要求"，让工具调用、流程决策更贴合需求
- **可扩展维护**: 通过 YN5 模式库、加权策略，支持动态调整复杂度判定规则

## 技术特点

- **实时评估**: 毫秒级复杂度判定
- **动态调整**: 支持运行时策略更新
- **历史学习**: 基于用户行为优化评估准确性
- **多维度分析**: 综合考虑多个因素，提高判定精度

复杂度评估是 Claude Code 实现"任务智能调度"的前提，让系统既能高效处理简单需求，又能深度应对复杂挑战。