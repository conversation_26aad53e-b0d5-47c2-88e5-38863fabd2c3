//! # Agent 命令工具函数
//!
//! 提供 Agent 命令模块共用的工具函数。

use crate::error::{CliError, Result};
use orion_core::{agent::AgentState, config::OrionConfig};
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::time::Duration;
use tokio::time::Duration as TokioDuration;

/// Agent 信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentInfo {
    pub name: String,
    pub description: String,
    pub state: AgentState,
    pub model: String,
    pub uptime: Duration,
    pub tasks_completed: u64,
    pub memory_usage: f64,
}

/// 加载配置文件
pub async fn load_config<P: AsRef<Path>>(config_path: P) -> Result<OrionConfig> {
    OrionConfig::from_file(config_path).map_err(|e| CliError::ConfigError {
        error: format!("加载配置失败: {}", e),
    })
}

/// 格式化 Agent 状态
pub fn format_agent_state(state: &AgentState) -> String {
    match state {
        AgentState::Idle => "空闲".to_string(),
        AgentState::Thinking => "思考中".to_string(),
        AgentState::ExecutingTool => "执行工具".to_string(),
        AgentState::WaitingForInput => "等待输入".to_string(),
        AgentState::ExecutingWorkflow => "执行工作流".to_string(),
        AgentState::Error => "错误".to_string(),
        AgentState::Paused => "暂停".to_string(),
        AgentState::Stopped => "停止".to_string(),
    }
}

/// 格式化持续时间
pub fn format_duration(duration: &Duration) -> String {
    let total_seconds = duration.as_secs();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;
    
    if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, seconds)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else {
        format!("{}s", seconds)
    }
}

/// 截断字符串
pub fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}

/// 获取模拟 Agent 列表（用于演示）
pub fn get_mock_agents() -> Vec<AgentInfo> {
    vec![
        AgentInfo {
            name: "assistant".to_string(),
            description: "通用助手 Agent".to_string(),
            state: AgentState::Idle,
            model: "gpt-4".to_string(),
            uptime: Duration::from_secs(3600),
            tasks_completed: 42,
            memory_usage: 128.5,
        },
        AgentInfo {
            name: "coder".to_string(),
            description: "代码生成 Agent".to_string(),
            state: AgentState::ExecutingTool,
            model: "claude-3".to_string(),
            uptime: Duration::from_secs(7200),
            tasks_completed: 18,
            memory_usage: 256.8,
        },
    ]
}

/// 打印输出（支持多种格式）
pub fn print_output<T: Serialize + ?Sized>(data: &T, format: &str) -> Result<()> {
    match format {
        "json" => {
            let json = serde_json::to_string_pretty(data).map_err(|e| CliError::SerializationError {
                error: format!("序列化失败: {}", e),
            })?;
            println!("{}", json);
        }
        "yaml" => {
            let yaml = serde_yaml::to_string(data).map_err(|e| CliError::SerializationError {
                error: format!("序列化失败: {}", e),
            })?;
            println!("{}", yaml);
        }
        _ => {
            // 默认格式由调用方处理
        }
    }
    Ok(())
}

/// 模拟延迟
pub async fn simulate_processing_delay(ms: u64) {
    tokio::time::sleep(TokioDuration::from_millis(ms)).await;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_agent_state() {
        assert_eq!(format_agent_state(&AgentState::Idle), "空闲");
        assert_eq!(format_agent_state(&AgentState::Thinking), "思考中");
        assert_eq!(format_agent_state(&AgentState::ExecutingTool), "执行工具");
    }

    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(&Duration::from_secs(30)), "30s");
        assert_eq!(format_duration(&Duration::from_secs(90)), "1m 30s");
        assert_eq!(format_duration(&Duration::from_secs(3661)), "1h 1m 1s");
    }

    #[test]
    fn test_truncate_string() {
        assert_eq!(truncate_string("hello", 10), "hello");
        assert_eq!(truncate_string("hello world", 8), "hello...");
        assert_eq!(truncate_string("hi", 5), "hi");
    }

    #[test]
    fn test_get_mock_agents() {
        let agents = get_mock_agents();
        assert_eq!(agents.len(), 2);
        assert_eq!(agents[0].name, "assistant");
        assert_eq!(agents[1].name, "coder");
    }
}