//! # 导入配置功能
//!
//! 实现配置文件的导入和合并功能。

use crate::error::{CliError, Result};
use crate::commands::config::types::ImportConfig;
use orion_core::config::OrionConfig;
use std::fs;

impl ImportConfig {
    /// 执行导入配置
    pub async fn execute(&self) -> Result<()> {
        // 检查输入文件是否存在
        if !self.input.exists() {
            return Err(CliError::ConfigError {
                error: format!("输入文件不存在: {}", self.input.display()),
            });
        }
        
        // 检查目标文件是否存在
        if self.config.exists() && !self.force && !self.merge {
            return Err(CliError::ConfigError {
                error: format!(
                    "目标配置文件已存在: {}，使用 --force 覆盖或 --merge 合并",
                    self.config.display()
                ),
            });
        }
        
        // 读取输入文件
        let input_content = fs::read_to_string(&self.input)
            .map_err(|e| CliError::IoError {
                error: format!("读取输入文件失败: {}", e),
            })?;
        
        // 根据文件扩展名解析配置
        let input_config = self.parse_config_content(&input_content)?;
        
        // 处理目标配置
        let final_config = if self.merge && self.config.exists() {
            // 合并配置
            let existing_config = OrionConfig::from_file(&self.config)
                .map_err(|e| CliError::ConfigError {
                    error: format!("加载现有配置失败: {}", e),
                })?;
            
            self.merge_configs(existing_config, input_config)?
        } else {
            // 直接使用输入配置
            input_config
        };
        
        // 创建目标目录（如果需要）
        if let Some(parent) = self.config.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| CliError::IoError {
                    error: format!("创建目录失败: {}", e),
                })?;
        }
        
        // 保存配置
        final_config.save_to_file(&self.config)
            .map_err(|e| CliError::ConfigError {
                error: format!("保存配置文件失败: {}", e),
            })?;
        
        println!("✅ 配置已导入到: {}", self.config.display());
        
        if self.merge {
            println!("🔄 配置已与现有配置合并");
        }
        
        Ok(())
    }
    
    /// 解析配置内容
    fn parse_config_content(&self, content: &str) -> Result<OrionConfig> {
        // 根据输入文件扩展名确定格式
        let format = self.input.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("toml");
        
        match format {
            "json" => {
                serde_json::from_str(content)
                    .map_err(|e| CliError::ConfigError {
                        error: format!("解析 JSON 配置失败: {}", e),
                    })
            }
            "yaml" | "yml" => {
                serde_yaml::from_str(content)
                    .map_err(|e| CliError::ConfigError {
                        error: format!("解析 YAML 配置失败: {}", e),
                    })
            }
            "toml" | _ => {
                toml::from_str(content)
                    .map_err(|e| CliError::ConfigError {
                        error: format!("解析 TOML 配置失败: {}", e),
                    })
            }
        }
    }
    
    /// 合并配置
    fn merge_configs(&self, mut existing: OrionConfig, input: OrionConfig) -> Result<OrionConfig> {
        // 合并各个配置节
        // 直接赋值，因为这些字段不是 Option 类型
        existing.general = input.general;
        existing.api = input.api;
        existing.agents = input.agents;
        existing.security = input.security;
        existing.workflows = input.workflows;
        existing.logging = input.logging;
        existing.tools = input.tools;

        Ok(existing)
    }
}