//! # 配置命令类型定义
//!
//! 定义配置模块中使用的所有命令结构体和枚举类型。

use clap::{Args, Subcommand};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 配置命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ConfigCommand {
    #[command(subcommand)]
    pub action: ConfigAction,
}

/// 配置操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum ConfigAction {
    /// 初始化配置文件
    Init(InitConfig),
    /// 显示当前配置
    Show(ShowConfig),
    /// 设置配置项
    Set(SetConfig),
    /// 获取配置项
    Get(GetConfig),
    /// 验证配置文件
    Validate(ValidateConfig),
    /// 重置配置为默认值
    Reset(ResetConfig),
    /// 导出配置
    Export(ExportConfig),
    /// 导入配置
    Import(ImportConfig),
}

/// 初始化配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct InitConfig {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub output: PathBuf,
    
    /// 是否覆盖已存在的配置文件
    #[arg(short, long)]
    pub force: bool,
    
    /// 配置模板类型
    #[arg(short, long, default_value = "default", value_parser = ["default", "minimal", "development", "production"])]
    pub template: String,
    
    /// 是否包含示例配置
    #[arg(long, default_value_t = true)]
    pub with_examples: bool,
}

/// 显示配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ShowConfig {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 输出格式
    #[arg(short, long, default_value = "toml", value_parser = ["toml", "json", "yaml"])]
    pub format: String,
    
    /// 是否显示敏感信息
    #[arg(long)]
    pub show_secrets: bool,
    
    /// 只显示指定的配置节
    #[arg(long)]
    pub section: Option<String>,
}

/// 设置配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct SetConfig {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 配置键（使用点号分隔，如 llm.default_model）
    pub key: String,
    
    /// 配置值
    pub value: String,
    
    /// 值的类型
    #[arg(short, long, default_value = "string", value_parser = ["string", "number", "boolean", "array"])]
    pub value_type: String,
}

/// 获取配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct GetConfig {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 配置键（使用点号分隔）
    pub key: String,
    
    /// 输出格式
    #[arg(short, long, default_value = "value", value_parser = ["value", "json", "yaml"])]
    pub format: String,
}

/// 验证配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ValidateConfig {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 是否显示详细验证信息
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 是否检查外部依赖
    #[arg(long, default_value_t = true)]
    pub check_dependencies: bool,
}

/// 重置配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ResetConfig {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 要重置的配置节（如果不指定则重置全部）
    #[arg(long)]
    pub section: Option<String>,
    
    /// 是否确认重置
    #[arg(short, long)]
    pub yes: bool,
}

/// 导出配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ExportConfig {
    /// 源配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 导出文件路径
    #[arg(short, long)]
    pub output: PathBuf,
    
    /// 导出格式
    #[arg(short, long, default_value = "toml", value_parser = ["toml", "json", "yaml"])]
    pub format: String,
    
    /// 是否包含敏感信息
    #[arg(long)]
    pub include_secrets: bool,
}

/// 导入配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ImportConfig {
    /// 目标配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 导入文件路径
    #[arg(short, long)]
    pub input: PathBuf,
    
    /// 是否合并配置（否则覆盖）
    #[arg(short, long, default_value_t = true)]
    pub merge: bool,
    
    /// 是否覆盖已存在的配置文件
    #[arg(long)]
    pub force: bool,
}