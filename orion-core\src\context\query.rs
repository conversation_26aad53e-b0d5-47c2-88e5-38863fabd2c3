//! # 上下文查询模块
//!
//! 提供上下文查询相关的数据结构和功能，包括查询条件、时间范围、
//! 查询构建器等。支持复杂的多条件查询和灵活的结果过滤。

use crate::context::types::{ContextEntryType, ImportanceLevel};
use serde::{Deserialize, Serialize};
use std::time::SystemTime;

/// 上下文查询条件
/// 
/// 定义查询上下文条目的各种条件，支持多维度的过滤和排序。
/// 所有条件都是可选的，未指定的条件将被忽略。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextQuery {
    /// 会话ID过滤，只返回指定会话的条目
    pub session_id: Option<String>,
    /// 查询文本，在条目内容中进行模糊匹配
    pub query_text: Option<String>,
    /// 条目类型过滤，只返回指定类型的条目
    pub entry_types: Option<Vec<ContextEntryType>>,
    /// 标签过滤，返回包含任一指定标签的条目
    pub tags: Option<Vec<String>>,
    /// 最小相关性评分，过滤低相关性的条目
    pub min_relevance: Option<f64>,
    /// 最小重要性级别，过滤低重要性的条目
    pub min_importance: Option<ImportanceLevel>,
    /// 时间范围过滤，只返回指定时间范围内的条目
    pub time_range: Option<TimeRange>,
    /// 最大结果数量，限制返回的条目数量
    pub limit: Option<usize>,
    /// 是否包含子条目，递归包含匹配条目的所有子条目
    pub include_children: bool,
}

impl Default for ContextQuery {
    fn default() -> Self {
        Self {
            session_id: None,
            query_text: None,
            entry_types: None,
            tags: None,
            min_relevance: None,
            min_importance: None,
            time_range: None,
            limit: None,
            include_children: false,
        }
    }
}

impl ContextQuery {
    /// 创建新的查询构建器
    /// 
    /// # 返回
    /// 
    /// 返回新的查询构建器实例
    pub fn builder() -> ContextQueryBuilder {
        ContextQueryBuilder::new()
    }

    /// 创建会话查询
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话ID
    /// 
    /// # 返回
    /// 
    /// 返回只查询指定会话的查询条件
    pub fn for_session(session_id: String) -> Self {
        Self {
            session_id: Some(session_id),
            ..Default::default()
        }
    }

    /// 创建文本搜索查询
    /// 
    /// # 参数
    /// 
    /// * `text` - 搜索文本
    /// 
    /// # 返回
    /// 
    /// 返回文本搜索查询条件
    pub fn search_text(text: String) -> Self {
        Self {
            query_text: Some(text),
            ..Default::default()
        }
    }

    /// 创建类型过滤查询
    /// 
    /// # 参数
    /// 
    /// * `entry_types` - 条目类型列表
    /// 
    /// # 返回
    /// 
    /// 返回类型过滤查询条件
    pub fn filter_types(entry_types: Vec<ContextEntryType>) -> Self {
        Self {
            entry_types: Some(entry_types),
            ..Default::default()
        }
    }

    /// 检查查询是否为空
    /// 
    /// # 返回
    /// 
    /// 如果所有查询条件都为空返回true
    pub fn is_empty(&self) -> bool {
        self.session_id.is_none()
            && self.query_text.is_none()
            && self.entry_types.is_none()
            && self.tags.is_none()
            && self.min_relevance.is_none()
            && self.min_importance.is_none()
            && self.time_range.is_none()
    }
}

/// 时间范围
/// 
/// 定义查询的时间范围，用于过滤指定时间段内的条目。
/// 支持开放式范围（只指定开始或结束时间）。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeRange {
    /// 开始时间（包含）
    pub start: SystemTime,
    /// 结束时间（包含）
    pub end: SystemTime,
}

impl TimeRange {
    /// 创建新的时间范围
    /// 
    /// # 参数
    /// 
    /// * `start` - 开始时间
    /// * `end` - 结束时间
    /// 
    /// # 返回
    /// 
    /// 返回新的时间范围实例
    pub fn new(start: SystemTime, end: SystemTime) -> Self {
        Self { start, end }
    }

    /// 创建从指定时间到现在的时间范围
    /// 
    /// # 参数
    /// 
    /// * `start` - 开始时间
    /// 
    /// # 返回
    /// 
    /// 返回从指定时间到现在的时间范围
    pub fn from_start(start: SystemTime) -> Self {
        Self {
            start,
            end: SystemTime::now(),
        }
    }

    /// 创建最近指定时长的时间范围
    /// 
    /// # 参数
    /// 
    /// * `duration` - 时长
    /// 
    /// # 返回
    /// 
    /// 返回最近指定时长的时间范围
    pub fn recent(duration: std::time::Duration) -> Self {
        let now = SystemTime::now();
        let start = now - duration;
        Self { start, end: now }
    }

    /// 检查时间是否在范围内
    /// 
    /// # 参数
    /// 
    /// * `time` - 要检查的时间
    /// 
    /// # 返回
    /// 
    /// 如果时间在范围内返回true
    pub fn contains(&self, time: SystemTime) -> bool {
        time >= self.start && time <= self.end
    }

    /// 获取时间范围的持续时间
    /// 
    /// # 返回
    /// 
    /// 返回时间范围的持续时间
    pub fn duration(&self) -> std::time::Duration {
        self.end.duration_since(self.start).unwrap_or_default()
    }
}

/// 查询构建器
/// 
/// 提供流式API来构建复杂的查询条件，使查询构建更加直观和灵活。
#[derive(Debug, Default)]
pub struct ContextQueryBuilder {
    query: ContextQuery,
}

impl ContextQueryBuilder {
    /// 创建新的查询构建器
    /// 
    /// # 返回
    /// 
    /// 返回新的查询构建器实例
    pub fn new() -> Self {
        Self {
            query: ContextQuery::default(),
        }
    }

    /// 设置会话ID过滤
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话ID
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn session_id(mut self, session_id: String) -> Self {
        self.query.session_id = Some(session_id);
        self
    }

    /// 设置查询文本
    /// 
    /// # 参数
    /// 
    /// * `text` - 查询文本
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn query_text(mut self, text: String) -> Self {
        self.query.query_text = Some(text);
        self
    }

    /// 设置条目类型过滤
    /// 
    /// # 参数
    /// 
    /// * `types` - 条目类型列表
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn entry_types(mut self, types: Vec<ContextEntryType>) -> Self {
        self.query.entry_types = Some(types);
        self
    }

    /// 设置标签过滤
    /// 
    /// # 参数
    /// 
    /// * `tags` - 标签列表
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn tags(mut self, tags: Vec<String>) -> Self {
        self.query.tags = Some(tags);
        self
    }

    /// 设置最小相关性评分
    /// 
    /// # 参数
    /// 
    /// * `score` - 最小相关性评分
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn min_relevance(mut self, score: f64) -> Self {
        self.query.min_relevance = Some(score);
        self
    }

    /// 设置最小重要性级别
    /// 
    /// # 参数
    /// 
    /// * `importance` - 最小重要性级别
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn min_importance(mut self, importance: ImportanceLevel) -> Self {
        self.query.min_importance = Some(importance);
        self
    }

    /// 设置时间范围
    /// 
    /// # 参数
    /// 
    /// * `range` - 时间范围
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn time_range(mut self, range: TimeRange) -> Self {
        self.query.time_range = Some(range);
        self
    }

    /// 设置结果数量限制
    /// 
    /// # 参数
    /// 
    /// * `limit` - 最大结果数量
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn limit(mut self, limit: usize) -> Self {
        self.query.limit = Some(limit);
        self
    }

    /// 设置是否包含子条目
    /// 
    /// # 参数
    /// 
    /// * `include` - 是否包含子条目
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn include_children(mut self, include: bool) -> Self {
        self.query.include_children = include;
        self
    }

    /// 构建查询条件
    /// 
    /// # 返回
    /// 
    /// 返回构建的查询条件
    pub fn build(self) -> ContextQuery {
        self.query
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::context::types::ContextEntryType;
    use std::time::Duration;

    #[test]
    fn test_context_query_builder() {
        let query = ContextQuery::builder()
            .session_id("test_session".to_string())
            .query_text("测试".to_string())
            .entry_types(vec![ContextEntryType::UserInput])
            .min_relevance(0.5)
            .limit(10)
            .include_children(true)
            .build();

        assert_eq!(query.session_id, Some("test_session".to_string()));
        assert_eq!(query.query_text, Some("测试".to_string()));
        assert_eq!(query.entry_types, Some(vec![ContextEntryType::UserInput]));
        assert_eq!(query.min_relevance, Some(0.5));
        assert_eq!(query.limit, Some(10));
        assert!(query.include_children);
    }

    #[test]
    fn test_time_range() {
        let now = SystemTime::now();
        let start = now - Duration::from_secs(3600); // 1小时前
        let range = TimeRange::new(start, now);

        assert!(range.contains(now - Duration::from_secs(1800))); // 30分钟前
        assert!(!range.contains(now - Duration::from_secs(7200))); // 2小时前
        assert!(range.duration() <= Duration::from_secs(3600));
    }

    #[test]
    fn test_recent_time_range() {
        let range = TimeRange::recent(Duration::from_secs(3600));
        let now = SystemTime::now();
        
        assert!(range.contains(now - Duration::from_secs(1800))); // 30分钟前
        assert!(!range.contains(now - Duration::from_secs(7200))); // 2小时前
    }

    #[test]
    fn test_query_shortcuts() {
        let session_query = ContextQuery::for_session("test".to_string());
        assert_eq!(session_query.session_id, Some("test".to_string()));

        let text_query = ContextQuery::search_text("搜索".to_string());
        assert_eq!(text_query.query_text, Some("搜索".to_string()));

        let type_query = ContextQuery::filter_types(vec![ContextEntryType::UserInput]);
        assert_eq!(type_query.entry_types, Some(vec![ContextEntryType::UserInput]));
    }
}
