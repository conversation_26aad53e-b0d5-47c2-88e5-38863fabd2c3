//! # 错误系统通用类型模块
//!
//! 定义错误处理系统中的通用类型和枚举。
//! 包含错误级别、错误分类、错误码、上下文信息等通用定义。

use std::collections::HashMap;
use std::fmt;
use std::time::{Duration, SystemTime};
use serde::{Deserialize, Serialize};

/// 错误严重程度级别
/// 
/// 用于对错误进行分级，帮助系统和用户了解错误的严重程度。
/// 从低到高分为四个级别：Low -> Medium -> High -> Critical
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 低级别 - 不影响核心功能
    /// 
    /// 例如：日志记录失败、非关键功能异常等
    Low,
    /// 中级别 - 影响部分功能  
    /// 
    /// 例如：某个工具执行失败、部分API调用异常等
    Medium,
    /// 高级别 - 影响核心功能
    /// 
    /// 例如：配置错误、消息队列异常等
    High,
    /// 严重级别 - 系统无法继续运行
    /// 
    /// 例如：初始化失败、核心组件崩溃等
    Critical,
}

impl ErrorSeverity {
    /// 获取错误级别的数值表示
    pub fn as_number(&self) -> u8 {
        match self {
            ErrorSeverity::Low => 1,
            ErrorSeverity::Medium => 2,
            ErrorSeverity::High => 3,
            ErrorSeverity::Critical => 4,
        }
    }

    /// 获取错误级别的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            ErrorSeverity::Low => "低",
            ErrorSeverity::Medium => "中",
            ErrorSeverity::High => "高",
            ErrorSeverity::Critical => "严重",
        }
    }

    /// 从数值创建错误级别
    pub fn from_number(num: u8) -> Option<Self> {
        match num {
            1 => Some(ErrorSeverity::Low),
            2 => Some(ErrorSeverity::Medium),
            3 => Some(ErrorSeverity::High),
            4 => Some(ErrorSeverity::Critical),
            _ => None,
        }
    }
}

impl fmt::Display for ErrorSeverity {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

/// 错误分类枚举
/// 
/// 将错误按照功能领域进行分类，便于错误统计和分析。
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ErrorCategory {
    /// 系统核心错误
    Core,
    /// 配置相关错误
    Configuration,
    /// 网络通信错误
    Network,
    /// LLM相关错误
    LanguageModel,
    /// 工作流执行错误
    Workflow,
    /// 消息队列错误
    MessageBus,
    /// 安全沙箱错误
    Security,
    /// 工具系统错误
    Tools,
    /// 上下文管理错误
    Context,
    /// Agent相关错误
    Agent,
    /// 数据库错误
    Database,
    /// 文件系统错误
    FileSystem,
    /// 序列化错误
    Serialization,
    /// 时间相关错误
    Time,
    /// 其他未分类错误
    Other,
}

impl ErrorCategory {
    /// 获取错误分类的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            ErrorCategory::Core => "核心",
            ErrorCategory::Configuration => "配置",
            ErrorCategory::Network => "网络",
            ErrorCategory::LanguageModel => "语言模型",
            ErrorCategory::Workflow => "工作流",
            ErrorCategory::MessageBus => "消息队列",
            ErrorCategory::Security => "安全",
            ErrorCategory::Tools => "工具",
            ErrorCategory::Context => "上下文",
            ErrorCategory::Agent => "代理",
            ErrorCategory::Database => "数据库",
            ErrorCategory::FileSystem => "文件系统",
            ErrorCategory::Serialization => "序列化",
            ErrorCategory::Time => "时间",
            ErrorCategory::Other => "其他",
        }
    }
}

impl fmt::Display for ErrorCategory {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

/// 错误码定义
/// 
/// 为每种错误类型分配唯一的错误码，便于错误识别和文档查询。
/// 错误码格式：CATEOGRY_SPECIFIC_CODE (例如：CORE_001, NET_404)
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ErrorCode {
    /// 分类前缀
    pub category: String,
    /// 具体错误编号
    pub code: u32,
}

impl ErrorCode {
    /// 创建新的错误码
    pub fn new(category: impl Into<String>, code: u32) -> Self {
        Self {
            category: category.into(),
            code,
        }
    }

    /// 获取完整的错误码字符串
    pub fn full_code(&self) -> String {
        format!("{}_{:03}", self.category, self.code)
    }
}

impl fmt::Display for ErrorCode {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.full_code())
    }
}

/// 常用错误码定义
impl ErrorCode {
    /// 创建核心错误码
    pub fn core_init_failed() -> Self { Self::new("CORE", 1) }
    pub fn core_internal_error() -> Self { Self::new("CORE", 2) }
    
    /// 创建配置错误码
    pub fn cfg_file_not_found() -> Self { Self::new("CFG", 1) }
    pub fn cfg_parse_error() -> Self { Self::new("CFG", 2) }
    pub fn cfg_validation_error() -> Self { Self::new("CFG", 3) }
    
    /// 创建网络错误码
    pub fn net_connection_timeout() -> Self { Self::new("NET", 1) }
    pub fn net_request_failed() -> Self { Self::new("NET", 2) }
    pub fn net_http_error() -> Self { Self::new("NET", 3) }
    
    /// 创建LLM错误码
    pub fn llm_api_call_failed() -> Self { Self::new("LLM", 1) }
    pub fn llm_unsupported_model() -> Self { Self::new("LLM", 2) }
    pub fn llm_engine_error() -> Self { Self::new("LLM", 3) }
    
    /// 创建工作流错误码
    pub fn wf_execution_failed() -> Self { Self::new("WF", 1) }
    pub fn wf_step_failed() -> Self { Self::new("WF", 2) }
    pub fn wf_invalid_state() -> Self { Self::new("WF", 3) }
}

/// 错误上下文信息
/// 
/// 提供错误发生时的额外上下文信息，帮助调试和问题定位。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// 错误发生的时间戳
    pub timestamp: SystemTime,
    /// 错误发生的组件或模块
    pub component: String,
    /// 操作名称
    pub operation: Option<String>,
    /// 用户ID（如果有）
    pub user_id: Option<String>,
    /// 会话ID（如果有）
    pub session_id: Option<String>,
    /// 请求ID（如果有）
    pub request_id: Option<String>,
    /// 额外的键值对信息
    pub metadata: HashMap<String, String>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    pub fn new(component: impl Into<String>) -> Self {
        Self {
            timestamp: SystemTime::now(),
            component: component.into(),
            operation: None,
            user_id: None,
            session_id: None,
            request_id: None,
            metadata: HashMap::new(),
        }
    }

    /// 设置操作名称
    pub fn with_operation(mut self, operation: impl Into<String>) -> Self {
        self.operation = Some(operation.into());
        self
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: impl Into<String>) -> Self {
        self.user_id = Some(user_id.into());
        self
    }

    /// 设置会话ID
    pub fn with_session_id(mut self, session_id: impl Into<String>) -> Self {
        self.session_id = Some(session_id.into());
        self
    }

    /// 设置请求ID
    pub fn with_request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }

    /// 添加元数据
    pub fn with_metadata(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.metadata.insert(key.into(), value.into());
        self
    }

    /// 添加多个元数据
    pub fn with_metadata_map(mut self, metadata: HashMap<String, String>) -> Self {
        self.metadata.extend(metadata);
        self
    }
}

impl Default for ErrorContext {
    fn default() -> Self {
        Self::new("unknown")
    }
}

/// 错误恢复策略
/// 
/// 定义错误发生时系统可以采取的恢复策略。
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum RecoveryStrategy {
    /// 不采取任何恢复措施
    None,
    /// 重试操作
    Retry {
        /// 最大重试次数
        max_attempts: u32,
        /// 重试间隔
        interval: Duration,
        /// 是否使用指数退避
        exponential_backoff: bool,
    },
    /// 降级服务
    Fallback {
        /// 降级策略描述
        strategy: String,
    },
    /// 回滚操作
    Rollback {
        /// 回滚点标识
        checkpoint: String,
    },
    /// 忽略错误继续执行
    IgnoreAndContinue,
    /// 停止当前操作
    Stop,
    /// 重启组件
    Restart {
        /// 重启的组件名称
        component: String,
    },
}

impl RecoveryStrategy {
    /// 创建重试策略
    pub fn retry(max_attempts: u32, interval: Duration) -> Self {
        Self::Retry {
            max_attempts,
            interval,
            exponential_backoff: false,
        }
    }

    /// 创建带指数退避的重试策略
    pub fn retry_with_backoff(max_attempts: u32, interval: Duration) -> Self {
        Self::Retry {
            max_attempts,
            interval,
            exponential_backoff: true,
        }
    }

    /// 创建降级策略
    pub fn fallback(strategy: impl Into<String>) -> Self {
        Self::Fallback {
            strategy: strategy.into(),
        }
    }

    /// 创建回滚策略
    pub fn rollback(checkpoint: impl Into<String>) -> Self {
        Self::Rollback {
            checkpoint: checkpoint.into(),
        }
    }

    /// 创建重启策略
    pub fn restart(component: impl Into<String>) -> Self {
        Self::Restart {
            component: component.into(),
        }
    }
}

/// 错误统计信息
/// 
/// 用于统计和分析错误发生的情况。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorStats {
    /// 按分类统计的错误次数
    pub by_category: HashMap<ErrorCategory, u64>,
    /// 按严重程度统计的错误次数
    pub by_severity: HashMap<ErrorSeverity, u64>,
    /// 按组件统计的错误次数
    pub by_component: HashMap<String, u64>,
    /// 总错误次数
    pub total_count: u64,
    /// 统计开始时间
    pub start_time: SystemTime,
    /// 最后更新时间
    pub last_updated: SystemTime,
}

impl Default for ErrorStats {
    fn default() -> Self {
        Self::new()
    }
}

impl ErrorStats {
    /// 创建新的错误统计
    pub fn new() -> Self {
        let now = SystemTime::now();
        Self {
            by_category: HashMap::new(),
            by_severity: HashMap::new(),
            by_component: HashMap::new(),
            total_count: 0,
            start_time: now,
            last_updated: now,
        }
    }

    /// 记录一个错误
    pub fn record_error(
        &mut self,
        category: ErrorCategory,
        severity: ErrorSeverity,
        component: &str,
    ) {
        *self.by_category.entry(category).or_insert(0) += 1;
        *self.by_severity.entry(severity).or_insert(0) += 1;
        *self.by_component.entry(component.to_string()).or_insert(0) += 1;
        self.total_count += 1;
        self.last_updated = SystemTime::now();
    }

    /// 获取指定分类的错误次数
    pub fn get_category_count(&self, category: &ErrorCategory) -> u64 {
        self.by_category.get(category).copied().unwrap_or(0)
    }

    /// 获取指定严重程度的错误次数
    pub fn get_severity_count(&self, severity: &ErrorSeverity) -> u64 {
        self.by_severity.get(severity).copied().unwrap_or(0)
    }

    /// 获取指定组件的错误次数
    pub fn get_component_count(&self, component: &str) -> u64 {
        self.by_component.get(component).copied().unwrap_or(0)
    }

    /// 重置统计信息
    pub fn reset(&mut self) {
        self.by_category.clear();
        self.by_severity.clear();
        self.by_component.clear();
        self.total_count = 0;
        let now = SystemTime::now();
        self.start_time = now;
        self.last_updated = now;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_severity() {
        assert_eq!(ErrorSeverity::Low.as_number(), 1);
        assert_eq!(ErrorSeverity::Critical.as_number(), 4);
        assert!(ErrorSeverity::Low < ErrorSeverity::High);
        
        assert_eq!(ErrorSeverity::from_number(2), Some(ErrorSeverity::Medium));
        assert_eq!(ErrorSeverity::from_number(5), None);
    }

    #[test]
    fn test_error_category() {
        assert_eq!(ErrorCategory::Core.as_str(), "核心");
        assert_eq!(ErrorCategory::Network.as_str(), "网络");
        assert_eq!(format!("{}", ErrorCategory::Configuration), "配置");
    }

    #[test]
    fn test_error_code() {
        let code = ErrorCode::new("TEST", 42);
        assert_eq!(code.full_code(), "TEST_042");
        assert_eq!(format!("{}", code), "TEST_042");
    }

    #[test]
    fn test_error_context() {
        let context = ErrorContext::new("test-component")
            .with_operation("test_operation")
            .with_user_id("user123")
            .with_metadata("key1", "value1");

        assert_eq!(context.component, "test-component");
        assert_eq!(context.operation, Some("test_operation".to_string()));
        assert_eq!(context.user_id, Some("user123".to_string()));
        assert_eq!(context.metadata.get("key1"), Some(&"value1".to_string()));
    }

    #[test]
    fn test_recovery_strategy() {
        let retry = RecoveryStrategy::retry(3, Duration::from_secs(1));
        match retry {
            RecoveryStrategy::Retry { max_attempts, .. } => {
                assert_eq!(max_attempts, 3);
            }
            _ => panic!("Expected retry strategy"),
        }

        let fallback = RecoveryStrategy::fallback("use_cache");
        match fallback {
            RecoveryStrategy::Fallback { strategy } => {
                assert_eq!(strategy, "use_cache");
            }
            _ => panic!("Expected fallback strategy"),
        }
    }

    #[test]
    fn test_error_stats() {
        let mut stats = ErrorStats::new();
        assert_eq!(stats.total_count, 0);

        stats.record_error(ErrorCategory::Core, ErrorSeverity::High, "test-component");
        assert_eq!(stats.total_count, 1);
        assert_eq!(stats.get_category_count(&ErrorCategory::Core), 1);
        assert_eq!(stats.get_severity_count(&ErrorSeverity::High), 1);
        assert_eq!(stats.get_component_count("test-component"), 1);

        stats.reset();
        assert_eq!(stats.total_count, 0);
        assert_eq!(stats.get_category_count(&ErrorCategory::Core), 0);
    }
}