//! # 配置类型定义
//!
//! 定义 Orion 系统的配置结构体。

use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Orion 主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrionConfig {
    /// 通用配置
    pub general: GeneralConfig,
    /// API 配置
    pub api: ApiConfig,
    /// Agent 配置
    pub agents: AgentConfig,
    /// 工具配置
    pub tools: ToolsConfig,
    /// 工作流配置
    pub workflows: WorkflowsConfig,
    /// 日志配置
    pub logging: LoggingConfig,
    /// 安全配置
    pub security: SecurityConfig,
}

/// 通用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneralConfig {
    /// 日志级别
    pub log_level: String,
    /// 工作目录
    pub work_dir: PathBuf,
    /// 输出格式
    pub output_format: String,
    /// 是否启用颜色输出
    pub enable_color: bool,
    /// 是否启用性能分析
    pub enable_profiling: bool,
}

/// API 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiConfig {
    /// API 基础 URL
    pub base_url: String,
    /// API 密钥
    pub api_key: Option<String>,
    /// 请求超时时间（秒）
    pub timeout: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试间隔（秒）
    pub retry_interval: u64,
}

/// Agent 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfig {
    /// 默认模型
    pub default_model: String,
    /// 最大并发 Agent 数量
    pub max_concurrent: usize,
    /// 是否自动启动
    pub auto_start: bool,
    /// 默认超时时间（秒）
    pub default_timeout: u64,
    /// 最大上下文长度
    pub max_context_length: usize,
}

/// 工具配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolsConfig {
    /// 工具注册表 URL
    pub registry_url: String,
    /// 是否自动更新
    pub auto_update: bool,
    /// 缓存目录
    pub cache_dir: PathBuf,
    /// 工具执行超时时间（秒）
    pub execution_timeout: u64,
    /// 最大并发工具执行数
    pub max_concurrent_executions: usize,
}

/// 工作流配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowsConfig {
    /// 模板目录
    pub template_dir: PathBuf,
    /// 最大执行时间（秒）
    pub max_execution_time: u64,
    /// 是否自动保存
    pub auto_save: bool,
    /// 最大并发工作流数
    pub max_concurrent_workflows: usize,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志格式
    pub format: String,
    /// 日志文件路径
    pub file_path: Option<PathBuf>,
    /// 是否启用控制台输出
    pub enable_console: bool,
    /// 是否启用文件输出
    pub enable_file: bool,
    /// 日志轮转大小（MB）
    pub rotation_size: u64,
    /// 保留日志文件数量
    pub retention_count: u32,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 是否启用沙箱
    pub enable_sandbox: bool,
    /// 沙箱配置
    pub sandbox: SandboxConfig,
    /// 允许的域名列表
    pub allowed_domains: Vec<String>,
    /// 禁止的命令列表
    pub blocked_commands: Vec<String>,
}

/// 沙箱配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SandboxConfig {
    /// 内存限制（MB）
    pub memory_limit: u64,
    /// CPU 限制（百分比）
    pub cpu_limit: u32,
    /// 执行超时时间（秒）
    pub timeout: u64,
    /// 临时目录
    pub temp_dir: PathBuf,
}