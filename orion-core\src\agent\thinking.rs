//! # Agent 思考过程管理模块
//!
//! 提供Agent思考过程的记录、管理和分析功能，
//! 帮助追踪Agent的决策过程和提高透明度。

use crate::agent::types::{ThinkingProcess, ThinkingStep, ThinkingStepType};
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use uuid::Uuid;

/// 思考过程管理器
/// 
/// 负责管理Agent的思考过程，记录思考步骤和分析决策路径
pub struct ThinkingManager {
    /// 当前思考过程，使用RwLock保证线程安全
    current_process: Arc<RwLock<Option<ThinkingProcess>>>,
    /// Agent名称，用于日志记录
    agent_name: String,
}

impl ThinkingManager {
    /// 创建新的思考过程管理器
    /// 
    /// # 参数
    /// * `agent_name` - Agent名称，用于日志记录
    /// 
    /// # 返回
    /// 返回新创建的ThinkingManager实例
    pub fn new(agent_name: String) -> Self {
        Self {
            current_process: Arc::new(RwLock::new(None)),
            agent_name,
        }
    }

    /// 开始新的思考过程
    /// 
    /// 创建一个新的思考过程并开始记录思考步骤
    /// 
    /// # 返回
    /// 返回思考过程的唯一标识符
    pub async fn start_thinking(&self) -> Uuid {
        let process_id = Uuid::new_v4();
        let thinking_process = ThinkingProcess {
            steps: Vec::new(),
            started_at: SystemTime::now(),
            completed_at: None,
            total_time_ms: 0,
        };

        {
            let mut current_thinking = self.current_process.write().await;
            *current_thinking = Some(thinking_process);
        }

        tracing::debug!(
            "Agent '{}' 开始思考过程: {}",
            self.agent_name,
            process_id
        );

        process_id
    }

    /// 添加思考步骤
    /// 
    /// 向当前思考过程添加一个新的思考步骤
    /// 
    /// # 参数
    /// * `step_type` - 思考步骤类型
    /// * `content` - 思考内容描述
    /// * `confidence` - 置信度（0.0-1.0）
    /// 
    /// # 返回
    /// 返回添加的步骤ID，如果没有活跃的思考过程则返回None
    pub async fn add_step(
        &self,
        step_type: ThinkingStepType,
        content: String,
        confidence: f32,
    ) -> Option<Uuid> {
        let step_id = Uuid::new_v4();
        let step = ThinkingStep {
            id: step_id,
            step_type: step_type.clone(),
            content: content.clone(),
            confidence: confidence.clamp(0.0, 1.0),
            timestamp: SystemTime::now(),
        };

        let mut thinking_process = self.current_process.write().await;
        if let Some(ref mut process) = *thinking_process {
            process.steps.push(step);
            
            tracing::debug!(
                "Agent '{}' 添加思考步骤 [{:?}]: {} (置信度: {:.2})",
                self.agent_name,
                step_type,
                content,
                confidence
            );
            
            Some(step_id)
        } else {
            tracing::warn!(
                "Agent '{}' 尝试添加思考步骤但没有活跃的思考过程",
                self.agent_name
            );
            None
        }
    }

    /// 结束当前思考过程
    /// 
    /// 完成当前的思考过程并计算总思考时间
    /// 
    /// # 返回
    /// 返回完成的思考过程，如果没有活跃的思考过程则返回None
    pub async fn end_thinking(&self) -> Option<ThinkingProcess> {
        let mut thinking_process = self.current_process.write().await;
        if let Some(ref mut process) = *thinking_process {
            let now = SystemTime::now();
            process.completed_at = Some(now);
            
            // 计算总思考时间
            if let Ok(duration) = now.duration_since(process.started_at) {
                process.total_time_ms = duration.as_millis() as u64;
            }

            let completed_process = process.clone();
            *thinking_process = None;

            tracing::debug!(
                "Agent '{}' 完成思考过程，总时间: {}ms，步骤数: {}",
                self.agent_name,
                completed_process.total_time_ms,
                completed_process.steps.len()
            );

            Some(completed_process)
        } else {
            tracing::warn!(
                "Agent '{}' 尝试结束思考过程但没有活跃的思考过程",
                self.agent_name
            );
            None
        }
    }

    /// 获取当前思考过程
    /// 
    /// # 返回
    /// 返回当前思考过程的克隆，如果没有活跃的思考过程则返回None
    pub async fn get_current_process(&self) -> Option<ThinkingProcess> {
        self.current_process.read().await.clone()
    }

    /// 获取当前思考步骤数量
    /// 
    /// # 返回
    /// 返回当前思考过程中的步骤数量
    pub async fn get_step_count(&self) -> usize {
        if let Some(process) = self.current_process.read().await.as_ref() {
            process.steps.len()
        } else {
            0
        }
    }

    /// 获取最后一个思考步骤
    /// 
    /// # 返回
    /// 返回最后一个思考步骤的克隆，如果没有步骤则返回None
    pub async fn get_last_step(&self) -> Option<ThinkingStep> {
        if let Some(process) = self.current_process.read().await.as_ref() {
            process.steps.last().cloned()
        } else {
            None
        }
    }

    /// 获取特定类型的思考步骤
    /// 
    /// # 参数
    /// * `step_type` - 要查找的步骤类型
    /// 
    /// # 返回
    /// 返回匹配类型的所有思考步骤
    pub async fn get_steps_by_type(&self, step_type: ThinkingStepType) -> Vec<ThinkingStep> {
        if let Some(process) = self.current_process.read().await.as_ref() {
            process.steps.iter()
                .filter(|step| std::mem::discriminant(&step.step_type) == std::mem::discriminant(&step_type))
                .cloned()
                .collect()
        } else {
            Vec::new()
        }
    }

    /// 计算平均置信度
    /// 
    /// # 返回
    /// 返回当前思考过程中所有步骤的平均置信度
    pub async fn get_average_confidence(&self) -> f32 {
        if let Some(process) = self.current_process.read().await.as_ref() {
            if process.steps.is_empty() {
                0.0
            } else {
                let total_confidence: f32 = process.steps.iter()
                    .map(|step| step.confidence)
                    .sum();
                total_confidence / process.steps.len() as f32
            }
        } else {
            0.0
        }
    }

    /// 获取思考过程摘要
    /// 
    /// # 返回
    /// 返回当前思考过程的摘要信息
    pub async fn get_summary(&self) -> String {
        if let Some(process) = self.current_process.read().await.as_ref() {
            let step_count = process.steps.len();
            let avg_confidence = if step_count > 0 {
                let total: f32 = process.steps.iter().map(|s| s.confidence).sum();
                total / step_count as f32
            } else {
                0.0
            };

            let elapsed_time = if let Some(completed_at) = process.completed_at {
                completed_at.duration_since(process.started_at)
                    .map(|d| d.as_millis() as u64)
                    .unwrap_or(0)
            } else {
                SystemTime::now().duration_since(process.started_at)
                    .map(|d| d.as_millis() as u64)
                    .unwrap_or(0)
            };

            format!(
                "思考过程摘要: {} 个步骤，平均置信度 {:.2}，耗时 {}ms",
                step_count,
                avg_confidence,
                elapsed_time
            )
        } else {
            "无活跃的思考过程".to_string()
        }
    }

    /// 清除当前思考过程
    /// 
    /// 强制清除当前的思考过程，通常在出现错误时使用
    pub async fn clear_current_process(&self) {
        let mut thinking_process = self.current_process.write().await;
        *thinking_process = None;
        
        tracing::debug!(
            "Agent '{}' 清除了当前思考过程",
            self.agent_name
        );
    }

    /// 检查是否有活跃的思考过程
    /// 
    /// # 返回
    /// 如果有活跃的思考过程返回true，否则返回false
    pub async fn has_active_process(&self) -> bool {
        self.current_process.read().await.is_some()
    }

    /// 添加问题分析步骤
    /// 
    /// 便捷方法，用于添加问题分析类型的思考步骤
    /// 
    /// # 参数
    /// * `analysis` - 分析内容
    /// * `confidence` - 置信度
    pub async fn add_problem_analysis(&self, analysis: String, confidence: f32) -> Option<Uuid> {
        self.add_step(ThinkingStepType::ProblemAnalysis, analysis, confidence).await
    }

    /// 添加策略规划步骤
    /// 
    /// 便捷方法，用于添加策略规划类型的思考步骤
    /// 
    /// # 参数
    /// * `strategy` - 策略内容
    /// * `confidence` - 置信度
    pub async fn add_strategy_planning(&self, strategy: String, confidence: f32) -> Option<Uuid> {
        self.add_step(ThinkingStepType::StrategyPlanning, strategy, confidence).await
    }

    /// 添加工具选择步骤
    /// 
    /// 便捷方法，用于添加工具选择类型的思考步骤
    /// 
    /// # 参数
    /// * `tool_selection` - 工具选择说明
    /// * `confidence` - 置信度
    pub async fn add_tool_selection(&self, tool_selection: String, confidence: f32) -> Option<Uuid> {
        self.add_step(ThinkingStepType::ToolSelection, tool_selection, confidence).await
    }

    /// 添加结果评估步骤
    /// 
    /// 便捷方法，用于添加结果评估类型的思考步骤
    /// 
    /// # 参数
    /// * `evaluation` - 评估内容
    /// * `confidence` - 置信度
    pub async fn add_result_evaluation(&self, evaluation: String, confidence: f32) -> Option<Uuid> {
        self.add_step(ThinkingStepType::ResultEvaluation, evaluation, confidence).await
    }
}

impl Clone for ThinkingManager {
    /// 克隆思考过程管理器
    /// 
    /// 注意：克隆的实例共享相同的思考过程
    fn clone(&self) -> Self {
        Self {
            current_process: self.current_process.clone(),
            agent_name: self.agent_name.clone(),
        }
    }
}
