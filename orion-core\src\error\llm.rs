//! # LLM相关错误模块
//!
//! 包含所有与大语言模型(LLM)相关的错误类型。
//! 涵盖模型调用、API交互、token处理、上下文管理等LLM操作中的错误。

use thiserror::Error;

/// LLM相关错误枚举
/// 
/// 定义了与大语言模型交互中可能出现的各种错误情况。
/// 包括模型不支持、API调用失败、token限制等问题。
#[derive(Error, Debug, Clone)]
pub enum LlmError {
    /// 通用LLM引擎错误
    /// 
    /// 用于不属于其他特定类别的LLM相关错误。
    #[error("LLM引擎错误: {message}")]
    EngineError {
        /// 错误详细信息
        message: String,
    },

    /// LLM API调用失败错误
    /// 
    /// 当调用LLM服务API失败时抛出。
    #[error("LLM API调用失败: {provider}, 错误: {message}")]
    ApiCallFailed {
        /// 服务提供商名称
        provider: String,
        /// 错误信息
        message: String,
    },

    /// 不支持的模型错误
    /// 
    /// 当尝试使用不支持的模型时抛出。
    #[error("不支持的LLM模型: {model}")]
    UnsupportedModel {
        /// 模型名称
        model: String,
    },
}

impl LlmError {
    /// 创建通用引擎错误
    pub fn engine_error(message: impl Into<String>) -> Self {
        Self::EngineError {
            message: message.into(),
        }
    }

    /// 创建API调用失败错误
    pub fn api_call_failed(
        provider: impl Into<String>,
        message: impl Into<String>,
    ) -> Self {
        Self::ApiCallFailed {
            provider: provider.into(),
            message: message.into(),
        }
    }

    /// 创建不支持的模型错误
    pub fn unsupported_model(model: impl Into<String>) -> Self {
        Self::UnsupportedModel {
            model: model.into(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_llm_error_creation() {
        let error = LlmError::engine_error("测试LLM错误");
        assert!(error.to_string().contains("LLM引擎错误"));
        assert!(error.to_string().contains("测试LLM错误"));
    }

    #[test]
    fn test_unsupported_model() {
        let error = LlmError::unsupported_model("gpt-5");
        assert!(error.to_string().contains("不支持的LLM模型"));
        assert!(error.to_string().contains("gpt-5"));
    }

    #[test]
    fn test_api_call_failed() {
        let error = LlmError::api_call_failed("openai", "API密钥无效");
        assert!(error.to_string().contains("LLM API调用失败"));
        assert!(error.to_string().contains("openai"));
        assert!(error.to_string().contains("API密钥无效"));
    }
}
