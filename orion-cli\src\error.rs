//! # Orion CLI 错误处理模块
//!
//! 提供统一的错误类型定义和处理机制，支持不同类型的错误场景。

use thiserror::Error;

/// CLI 操作结果类型
pub type Result<T> = std::result::Result<T, CliError>;

/// CLI 错误类型枚举
#[derive(Debug, Error)]
pub enum CliError {
    /// 配置相关错误
    #[error("配置错误: {error}")]
    ConfigError {
        /// 错误详情
        error: String,
    },
    
    /// 无效参数错误
    #[error("无效参数: {error}")]
    InvalidArgument {
        /// 错误详情
        error: String,
    },
    
    /// I/O 操作错误
    #[error("I/O 错误: {error}")]
    IoError {
        /// 错误详情
        error: String,
    },
    
    /// 序列化/反序列化错误
    #[error("序列化错误: {error}")]
    SerializationError {
        /// 错误详情
        error: String,
    },
    
    /// 命令执行错误
    #[error("执行错误: {error}")]
    ExecutionError {
        /// 错误详情
        error: String,
    },
    
    /// 网络相关错误
    #[error("网络错误: {error}")]
    NetworkError {
        /// 错误详情
        error: String,
    },
    
    /// 认证错误
    #[error("认证错误: {error}")]
    #[allow(dead_code)]
    AuthenticationError {
        /// 错误详情
        error: String,
    },
    
    /// 权限错误
    #[error("权限错误: {error}")]
    PermissionError {
        /// 错误详情
        error: String,
    },
    
    /// 超时错误
    #[error("超时错误: {error}")]
    TimeoutError {
        /// 错误详情
        error: String,
    },
    
    /// 初始化错误
    #[error("初始化错误: {error}")]
    InitializationError {
        /// 错误详情
        error: String,
    },

    /// 系统错误
    #[error("系统错误: {error}")]
    SystemError {
        /// 错误详情
        error: String,
    },

    /// 其他错误
    #[error("其他错误: {error}")]
    Other {
        /// 错误详情
        error: String,
    },
}

#[allow(dead_code)]
impl CliError {
    /// 创建配置错误
    pub fn config_error<S: Into<String>>(error: S) -> Self {
        Self::ConfigError {
            error: error.into(),
        }
    }
    
    /// 创建无效参数错误
    pub fn invalid_argument<S: Into<String>>(error: S) -> Self {
        Self::InvalidArgument {
            error: error.into(),
        }
    }
    
    /// 创建 I/O 错误
    pub fn io_error<S: Into<String>>(error: S) -> Self {
        Self::IoError {
            error: error.into(),
        }
    }
    
    /// 创建序列化错误
    pub fn serialization_error<S: Into<String>>(error: S) -> Self {
        Self::SerializationError {
            error: error.into(),
        }
    }
    
    /// 创建执行错误
    pub fn execution_error<S: Into<String>>(error: S) -> Self {
        Self::ExecutionError {
            error: error.into(),
        }
    }
    
    /// 创建网络错误
    pub fn network_error<S: Into<String>>(error: S) -> Self {
        Self::NetworkError {
            error: error.into(),
        }
    }
    
    /// 创建认证错误
    pub fn authentication_error<S: Into<String>>(error: S) -> Self {
        Self::AuthenticationError {
            error: error.into(),
        }
    }
    
    /// 创建权限错误
    pub fn permission_error<S: Into<String>>(error: S) -> Self {
        Self::PermissionError {
            error: error.into(),
        }
    }
    
    /// 创建超时错误
    pub fn timeout_error<S: Into<String>>(error: S) -> Self {
        Self::TimeoutError {
            error: error.into(),
        }
    }
    
    /// 创建其他错误
    pub fn other<S: Into<String>>(error: S) -> Self {
        Self::Other {
            error: error.into(),
        }
    }
    
    /// 获取错误代码
    pub fn error_code(&self) -> u32 {
        match self {
            Self::ConfigError { .. } => 2,
            Self::InvalidArgument { .. } => 3,
            Self::IoError { .. } => 4,
            Self::SerializationError { .. } => 5,
            Self::ExecutionError { .. } => 6,
            Self::NetworkError { .. } => 7,
            Self::AuthenticationError { .. } => 8,
            Self::PermissionError { .. } => 9,
            Self::TimeoutError { .. } => 10,
            Self::InitializationError { .. } => 11,
            Self::SystemError { .. } => 12,
            Self::Other { .. } => 1,
        }
    }
    
    /// 检查是否为可重试错误
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            Self::NetworkError { .. } | Self::TimeoutError { .. }
        )
    }
    
    /// 检查是否为用户错误（非系统错误）
    pub fn is_user_error(&self) -> bool {
        matches!(
            self,
            Self::ConfigError { .. }
                | Self::InvalidArgument { .. }
                | Self::AuthenticationError { .. }
                | Self::PermissionError { .. }
        )
    }
    
    /// 获取错误类型名称
    pub fn error_type(&self) -> &'static str {
        match self {
            Self::ConfigError { .. } => "配置错误",
            Self::InvalidArgument { .. } => "参数错误",
            Self::IoError { .. } => "I/O错误",
            Self::SerializationError { .. } => "序列化错误",
            Self::ExecutionError { .. } => "执行错误",
            Self::NetworkError { .. } => "网络错误",
            Self::AuthenticationError { .. } => "认证错误",
            Self::PermissionError { .. } => "权限错误",
            Self::TimeoutError { .. } => "超时错误",
            Self::InitializationError { .. } => "初始化错误",
            Self::SystemError { .. } => "系统错误",
            Self::Other { .. } => "其他错误",
        }
    }
}

// 标准库错误类型转换
impl From<std::io::Error> for CliError {
    fn from(error: std::io::Error) -> Self {
        Self::IoError {
            error: error.to_string(),
        }
    }
}

impl From<serde_json::Error> for CliError {
    fn from(error: serde_json::Error) -> Self {
        Self::SerializationError {
            error: error.to_string(),
        }
    }
}

impl From<serde_yaml::Error> for CliError {
    fn from(error: serde_yaml::Error) -> Self {
        Self::SerializationError {
            error: error.to_string(),
        }
    }
}

impl From<toml::de::Error> for CliError {
    fn from(error: toml::de::Error) -> Self {
        Self::SerializationError {
            error: error.to_string(),
        }
    }
}

impl From<toml::ser::Error> for CliError {
    fn from(error: toml::ser::Error) -> Self {
        Self::SerializationError {
            error: error.to_string(),
        }
    }
}

impl From<reqwest::Error> for CliError {
    fn from(error: reqwest::Error) -> Self {
        if error.is_timeout() {
            Self::TimeoutError {
                error: error.to_string(),
            }
        } else if error.is_connect() || error.is_request() {
            Self::NetworkError {
                error: error.to_string(),
            }
        } else {
            Self::Other {
                error: error.to_string(),
            }
        }
    }
}

// Orion Core 错误类型转换
impl From<orion_core::error::OrionError> for CliError {
    fn from(error: orion_core::error::OrionError) -> Self {
        match error {
            orion_core::error::OrionError::Config(config_error) => Self::ConfigError { error: config_error.to_string() },
            orion_core::error::OrionError::SerializationError(error) => Self::SerializationError { error },
            orion_core::error::OrionError::Network(network_error) => Self::NetworkError { error: network_error.to_string() },
            orion_core::error::OrionError::Core(orion_core::error::CoreError::InitializationError { message }) => Self::InitializationError { error: message },
            orion_core::error::OrionError::FileSystemError(error) => Self::IoError { error },
            orion_core::error::OrionError::DatabaseError(error) => Self::ExecutionError { error },
            orion_core::error::OrionError::Llm(llm_error) => Self::ExecutionError { error: llm_error.to_string() },
            orion_core::error::OrionError::ToolError(error) => Self::ExecutionError { error },
            orion_core::error::OrionError::Workflow(workflow_error) => Self::ExecutionError { error: workflow_error.to_string() },
            orion_core::error::OrionError::AgentError(error) => Self::ExecutionError { error },
            orion_core::error::OrionError::ContextError(error) => Self::ExecutionError { error },
            orion_core::error::OrionError::SecurityError(error) => Self::PermissionError { error },
            orion_core::error::OrionError::MessageBusError(error) => Self::SystemError { error },
            orion_core::error::OrionError::TimeError(error) => Self::TimeoutError { error },
            orion_core::error::OrionError::SystemTimeError(error) => Self::TimeoutError { error },
            orion_core::error::OrionError::Core(orion_core::error::CoreError::InternalError { message }) => Self::SystemError { error: message },
            _ => Self::Other { error: error.to_string() },
        }
    }
}

impl From<rustyline::error::ReadlineError> for CliError {
    fn from(error: rustyline::error::ReadlineError) -> Self {
        Self::IoError {
            error: format!("readline error: {}", error),
        }
    }
}

/// 错误上下文扩展 trait
#[allow(dead_code)]
pub trait ErrorContext<T> {
    /// 添加配置错误上下文
    fn with_config_context<S: Into<String>>(self, context: S) -> Result<T>;
    
    /// 添加执行错误上下文
    fn with_execution_context<S: Into<String>>(self, context: S) -> Result<T>;
    
    /// 添加 I/O 错误上下文
    fn with_io_context<S: Into<String>>(self, context: S) -> Result<T>;
}

impl<T, E> ErrorContext<T> for std::result::Result<T, E>
where
    E: std::error::Error + Send + Sync + 'static,
{
    fn with_config_context<S: Into<String>>(self, context: S) -> Result<T> {
        self.map_err(|e| CliError::ConfigError {
            error: format!("{}: {}", context.into(), e),
        })
    }
    
    fn with_execution_context<S: Into<String>>(self, context: S) -> Result<T> {
        self.map_err(|e| CliError::ExecutionError {
            error: format!("{}: {}", context.into(), e),
        })
    }
    
    fn with_io_context<S: Into<String>>(self, context: S) -> Result<T> {
        self.map_err(|e| CliError::IoError {
            error: format!("{}: {}", context.into(), e),
        })
    }
}

/// 错误报告器
#[allow(dead_code)]
pub struct ErrorReporter {
    /// 是否显示详细信息
    verbose: bool,
    /// 是否使用颜色
    use_color: bool,
}

#[allow(dead_code)]
impl ErrorReporter {
    /// 创建新的错误报告器
    pub fn new(verbose: bool, use_color: bool) -> Self {
        Self { verbose, use_color }
    }
    
    /// 报告错误
    pub fn report(&self, error: &CliError) {
        let error_symbol = if self.use_color {
            "\x1b[31m❌\x1b[0m"
        } else {
            "❌"
        };
        
        let error_type = if self.use_color {
            format!("\x1b[31m{}\x1b[0m", error.error_type())
        } else {
            error.error_type().to_string()
        };
        
        eprintln!("{} {}: {}", error_symbol, error_type, error);
        
        if self.verbose {
            eprintln!("错误代码: {}", error.error_code());
            eprintln!("可重试: {}", if error.is_retryable() { "是" } else { "否" });
            eprintln!("用户错误: {}", if error.is_user_error() { "是" } else { "否" });
        }
        
        // 提供建议
        self.provide_suggestion(error);
    }
    
    /// 提供错误解决建议
    fn provide_suggestion(&self, error: &CliError) {
        let suggestion = match error {
            CliError::ConfigError { .. } => {
                "💡 建议: 检查配置文件格式，或使用 'orion config init' 重新初始化配置"
            }
            CliError::InvalidArgument { .. } => {
                "💡 建议: 检查命令参数，使用 'orion --help' 查看帮助信息"
            }
            CliError::IoError { .. } => {
                "💡 建议: 检查文件路径和权限，确保有足够的磁盘空间"
            }
            CliError::NetworkError { .. } => {
                "💡 建议: 检查网络连接，确认服务器地址正确"
            }
            CliError::AuthenticationError { .. } => {
                "💡 建议: 检查认证凭据，确认 API 密钥或令牌有效"
            }
            CliError::PermissionError { .. } => {
                "💡 建议: 检查文件权限，或以管理员身份运行"
            }
            CliError::TimeoutError { .. } => {
                "💡 建议: 检查网络连接，或增加超时时间设置"
            }
            _ => "💡 建议: 查看日志获取更多详细信息",
        };
        
        if self.use_color {
            eprintln!("\x1b[33m{}\x1b[0m", suggestion);
        } else {
            eprintln!("{}", suggestion);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_error_creation() {
        let error = CliError::config_error("测试配置错误");
        assert!(matches!(error, CliError::ConfigError { .. }));
        assert_eq!(error.error_code(), 2);
        assert!(error.is_user_error());
        assert!(!error.is_retryable());
    }
    
    #[test]
    fn test_error_types() {
        let config_error = CliError::config_error("配置错误");
        let network_error = CliError::network_error("网络错误");
        let timeout_error = CliError::timeout_error("超时错误");
        
        assert_eq!(config_error.error_type(), "配置错误");
        assert_eq!(network_error.error_type(), "网络错误");
        assert_eq!(timeout_error.error_type(), "超时错误");
        
        assert!(config_error.is_user_error());
        assert!(!network_error.is_user_error());
        
        assert!(!config_error.is_retryable());
        assert!(network_error.is_retryable());
        assert!(timeout_error.is_retryable());
    }
    
    #[test]
    fn test_error_conversion() {
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "文件未找到");
        let cli_error: CliError = io_error.into();
        assert!(matches!(cli_error, CliError::IoError { .. }));
    }
    
    #[test]
    fn test_error_context() {
        let result: std::result::Result<(), std::io::Error> = Err(std::io::Error::new(
            std::io::ErrorKind::PermissionDenied,
            "权限被拒绝",
        ));
        
        let cli_result = result.with_io_context("读取配置文件时");
        assert!(cli_result.is_err());
        
        if let Err(error) = cli_result {
            assert!(matches!(error, CliError::IoError { .. }));
            assert!(error.to_string().contains("读取配置文件时"));
        }
    }
    
    #[test]
    fn test_error_reporter() {
        let reporter = ErrorReporter::new(true, false);
        let error = CliError::config_error("测试错误");
        
        // 这个测试主要验证不会 panic
        reporter.report(&error);
    }
}