//! # 设置配置功能
//!
//! 实现配置项的设置和修改功能。

use crate::error::{CliError, Result};
use crate::commands::config::types::SetConfig;
use orion_core::config::OrionConfig;

impl SetConfig {
    /// 执行设置配置
    pub async fn execute(&self) -> Result<()> {
        // 加载现有配置
        let mut config = if self.config.exists() {
            OrionConfig::from_file(&self.config)
                .map_err(|e| CliError::ConfigError {
                    error: format!("加载配置文件失败: {}", e),
                })?
        } else {
            OrionConfig::default()
        };
        
        // 解析配置键路径
        let keys: Vec<&str> = self.key.split('.').collect();
        if keys.is_empty() {
            return Err(CliError::ConfigError {
                error: "配置键不能为空".to_string(),
            });
        }
        
        // 解析值
        let parsed_value = self.parse_value()?;
        
        // 设置配置值
        self.set_config_value(&mut config, &keys, parsed_value)?;
        
        // 保存配置
        config.save_to_file(&self.config)
            .map_err(|e| CliError::ConfigError {
                error: format!("保存配置文件失败: {}", e),
            })?;
        
        println!("✅ 配置已更新: {} = {}", self.key, self.value);
        Ok(())
    }
    
    /// 解析配置值
    fn parse_value(&self) -> Result<toml::Value> {
        match self.value_type.as_str() {
            "boolean" => {
                let bool_val = self.value.parse::<bool>()
                    .map_err(|_| CliError::ConfigError {
                        error: format!("无效的布尔值: {}", self.value),
                    })?;
                Ok(toml::Value::Boolean(bool_val))
            }
            "number" => {
                if let Ok(int_val) = self.value.parse::<i64>() {
                    Ok(toml::Value::Integer(int_val))
                } else if let Ok(float_val) = self.value.parse::<f64>() {
                    Ok(toml::Value::Float(float_val))
                } else {
                    Err(CliError::ConfigError {
                        error: format!("无效的数字值: {}", self.value),
                    })
                }
            }
            "array" => {
                let array_val: Vec<&str> = self.value.split(',').map(|s| s.trim()).collect();
                let toml_array: Vec<toml::Value> = array_val.into_iter()
                    .map(|s| toml::Value::String(s.to_string()))
                    .collect();
                Ok(toml::Value::Array(toml_array))
            }
            "string" | _ => {
                Ok(toml::Value::String(self.value.clone()))
            }
        }
    }
    
    /// 设置配置值
    fn set_config_value(&self, config: &mut OrionConfig, keys: &[&str], value: toml::Value) -> Result<()> {
        // 将配置转换为 TOML 值进行操作
        let mut config_value = toml::Value::try_from(&*config)
            .map_err(|e| CliError::ConfigError {
                error: format!("配置序列化失败: {}", e),
            })?;

        // 递归设置值
        self.set_nested_value(&mut config_value, keys, value)?;

        // 将 TOML 值转换回配置结构
        *config = config_value.try_into()
            .map_err(|e| CliError::ConfigError {
                error: format!("配置反序列化失败: {:?}", e),
            })?;

        Ok(())
    }
    
    /// 递归设置嵌套值
    fn set_nested_value(&self, current: &mut toml::Value, keys: &[&str], value: toml::Value) -> Result<()> {
        if keys.is_empty() {
            return Ok(());
        }
        
        if keys.len() == 1 {
            // 最后一个键，设置值
            match current {
                toml::Value::Table(table) => {
                    table.insert(keys[0].to_string(), value);
                }
                _ => {
                    return Err(CliError::ConfigError {
                        error: format!("无法在非表类型中设置键: {}", keys[0]),
                    });
                }
            }
        } else {
            // 中间键，递归处理
            match current {
                toml::Value::Table(table) => {
                    let next_key = keys[0];
                    if !table.contains_key(next_key) {
                        table.insert(next_key.to_string(), toml::Value::Table(toml::Table::new()));
                    }
                    
                    if let Some(next_value) = table.get_mut(next_key) {
                        self.set_nested_value(next_value, &keys[1..], value)?;
                    }
                }
                _ => {
                    return Err(CliError::ConfigError {
                        error: format!("无法在非表类型中访问键: {}", keys[0]),
                    });
                }
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    
    #[test]
    fn test_parse_value() {
        let cmd = SetConfig {
            config: PathBuf::from("test.toml"),
            key: "test.key".to_string(),
            value: "true".to_string(),
            value_type: "boolean".to_string(),
        };
        
        let value = cmd.parse_value().unwrap();
        assert_eq!(value, toml::Value::Boolean(true));
    }
}