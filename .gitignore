# Rust编译产物
/target/
**/*.rs.bk
*.pdb

# Cargo锁文件（对于库项目通常忽略，但对于二进制项目保留）
# Cargo.lock

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 配置文件（可能包含敏感信息）
# orion.toml

# 工作空间目录
orion-workspace/

# 缓存目录
.cache/

# 测试覆盖率报告
tarpaulin-report.html
cov.info

# Rust分析器缓存
/target/rust-analyzer/

# 性能分析文件
*.prof
perf.data*

# 文档构建产物
/target/doc/

# 基准测试结果
/target/criterion/

# VSCode Counter
.VSCodeCounter/

# 项目规格和开发工具目录
.kiro/
.trae/

# 其他工具生成的文件
*.orig