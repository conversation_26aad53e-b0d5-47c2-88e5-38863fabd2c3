//! # 工具命令工具函数
//!
//! 提供工具模块共用的工具函数和辅助功能。

use crate::error::{CliError, Result};
use orion_core::{
    config::OrionConfig,
    tools::{ToolRegistry, ParameterType},
};
use std::path::Path;

/// 创建工具注册表
pub async fn create_tool_registry<P: AsRef<Path>>(config_path: P) -> Result<ToolRegistry> {
    let _config = OrionConfig::from_file(config_path)
        .map_err(|e| CliError::ConfigError {
            error: format!("加载配置失败: {}", e),
        })?;

    Ok(ToolRegistry::new())
}

/// 截断字符串
pub fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}

/// 格式化参数类型
pub fn format_parameter_type(param_type: &ParameterType) -> String {
    match param_type {
        ParameterType::String => "字符串".to_string(),
        ParameterType::Integer => "整数".to_string(),
        ParameterType::Float => "浮点数".to_string(),
        ParameterType::Boolean => "布尔值".to_string(),
        ParameterType::Array(_) => "数组".to_string(),
        ParameterType::Object => "对象".to_string(),
        ParameterType::FilePath => "文件路径".to_string(),
        ParameterType::Url => "URL".to_string(),
        ParameterType::Json => "JSON".to_string(),
    }
}

/// 打印输出（支持多种格式）
pub fn print_output<T: serde::Serialize + ?Sized>(data: &T, format: &str) -> Result<()> {
    match format {
        "json" => {
            let json = serde_json::to_string_pretty(data)
                .map_err(|e| CliError::SerializationError {
                    error: format!("序列化失败: {}", e),
                })?;
            println!("{}", json);
        }
        "yaml" => {
            let yaml = serde_yaml::to_string(data)
                .map_err(|e| CliError::SerializationError {
                    error: format!("序列化失败: {}", e),
                })?;
            println!("{}", yaml);
        }
        _ => {
            // 默认格式由调用方处理
        }
    }
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_truncate_string() {
        assert_eq!(truncate_string("hello", 10), "hello");
        assert_eq!(truncate_string("hello world", 8), "hello...");
        assert_eq!(truncate_string("hi", 5), "hi");
    }
    
    #[test]
    fn test_format_parameter_type() {
        assert_eq!(format_parameter_type(&ParameterType::String), "字符串");
        assert_eq!(format_parameter_type(&ParameterType::Integer), "整数");
        assert_eq!(format_parameter_type(&ParameterType::Boolean), "布尔值");
        assert_eq!(format_parameter_type(&ParameterType::FilePath), "文件路径");
    }
}