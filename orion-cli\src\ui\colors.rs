//! # 颜色系统
//!
//! 统一的颜色管理系统，支持主题切换。

use crossterm::style::Color;
use crate::ui::themes::{ThemeManager, ThemeType};
use std::sync::{LazyLock, Mutex};

/// 全局主题管理器
static GLOBAL_THEME_MANAGER: LazyLock<Mutex<ThemeManager>> = LazyLock::new(|| {
    Mutex::new(ThemeManager::new())
});

/// 初始化全局主题管理器
pub fn init_global_theme_manager() {
    // LazyLock 会在第一次访问时自动初始化
    let _ = &*GLOBAL_THEME_MANAGER;
}

/// 获取全局主题管理器
fn with_global_theme_manager<T>(f: impl FnOnce(&ThemeManager) -> T) -> T {
    let manager = GLOBAL_THEME_MANAGER.lock().unwrap();
    f(&*manager)
}

/// 颜色访问器
pub struct Colors;

impl Colors {
    /// 主要前景色
    pub fn foreground() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().foreground)
    }

    /// 背景色
    pub fn background() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().background)
    }

    /// 浅蓝色
    pub fn light_blue() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().light_blue)
    }

    /// 强调蓝色
    pub fn accent_blue() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().accent_blue)
    }

    /// 强调紫色
    pub fn accent_purple() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().accent_purple)
    }

    /// 强调青色
    pub fn accent_cyan() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().accent_cyan)
    }

    /// 强调绿色
    pub fn accent_green() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().accent_green)
    }

    /// 强调黄色
    pub fn accent_yellow() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().accent_yellow)
    }

    /// 强调红色
    pub fn accent_red() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().accent_red)
    }

    /// 差异添加色
    pub fn diff_added() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().diff_added)
    }

    /// 差异删除色
    pub fn diff_removed() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().diff_removed)
    }

    /// 注释色
    pub fn comment() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().comment)
    }

    /// 灰色
    pub fn gray() -> Color {
        with_global_theme_manager(|manager| manager.get_active_theme().gray)
    }

    /// 主题类型
    pub fn theme_type() -> ThemeType {
        with_global_theme_manager(|manager| manager.get_active_theme().theme_type)
    }

    /// 渐变色（如果支持）
    pub fn gradient_colors() -> Option<Vec<Color>> {
        with_global_theme_manager(|manager| manager.get_active_theme().gradient_colors.clone())
    }

    /// 成功色（绿色）
    pub fn success() -> Color {
        Self::accent_green()
    }

    /// 错误色（红色）
    pub fn error() -> Color {
        Self::accent_red()
    }

    /// 警告色（黄色）
    pub fn warning() -> Color {
        Self::accent_yellow()
    }

    /// 信息色（蓝色）
    pub fn info() -> Color {
        Self::accent_blue()
    }

    /// 边框色
    pub fn border() -> Color {
        Self::gray()
    }

    /// 高亮色
    pub fn highlight() -> Color {
        Self::accent_cyan()
    }

    /// 次要文本色
    pub fn secondary() -> Color {
        Self::comment()
    }

    /// 主要文本色
    pub fn primary() -> Color {
        Self::foreground()
    }

    /// 根据主题类型选择对比色
    pub fn contrast() -> Color {
        match Self::theme_type() {
            ThemeType::Dark => Color::White,
            ThemeType::Light => Color::Black,
        }
    }

    /// 获取状态颜色
    pub fn status_color(status: &str) -> Color {
        match status.to_lowercase().as_str() {
            "success" | "ok" | "done" | "completed" => Self::success(),
            "error" | "fail" | "failed" | "critical" => Self::error(),
            "warning" | "warn" | "caution" => Self::warning(),
            "info" | "information" | "note" => Self::info(),
            "pending" | "waiting" | "loading" => Self::accent_yellow(),
            "running" | "active" | "processing" => Self::accent_blue(),
            _ => Self::foreground(),
        }
    }

    /// 获取优先级颜色
    pub fn priority_color(priority: u8) -> Color {
        match priority {
            1 => Self::accent_red(),     // 最高优先级
            2 => Self::accent_yellow(),  // 高优先级
            3 => Self::accent_blue(),    // 中等优先级
            4 => Self::accent_green(),   // 低优先级
            _ => Self::gray(),           // 最低优先级
        }
    }
}

/// 颜色工具函数
pub mod color_utils {
    use super::*;

    /// 将 RGB 颜色转换为 CrossTerm Color
    pub fn rgb_to_color(r: u8, g: u8, b: u8) -> Color {
        Color::Rgb { r, g, b }
    }

    /// 创建渐变色数组
    pub fn create_gradient(start: Color, end: Color, steps: usize) -> Vec<Color> {
        if steps <= 1 {
            return vec![start];
        }

        let mut colors = Vec::with_capacity(steps);
        
        // 简化的渐变实现，实际项目中可以使用更复杂的算法
        for i in 0..steps {
            let factor = i as f32 / (steps - 1) as f32;
            
            match (start, end) {
                (Color::Rgb { r: r1, g: g1, b: b1 }, Color::Rgb { r: r2, g: g2, b: b2 }) => {
                    let r = (r1 as f32 + (r2 as f32 - r1 as f32) * factor) as u8;
                    let g = (g1 as f32 + (g2 as f32 - g1 as f32) * factor) as u8;
                    let b = (b1 as f32 + (b2 as f32 - b1 as f32) * factor) as u8;
                    colors.push(Color::Rgb { r, g, b });
                }
                _ => {
                    // 如果不是 RGB 颜色，交替使用起始和结束色
                    colors.push(if i < steps / 2 { start } else { end });
                }
            }
        }
        
        colors
    }

    /// 检查是否支持真彩色
    pub fn supports_truecolor() -> bool {
        std::env::var("COLORTERM")
            .map(|term| term.contains("truecolor") || term.contains("24bit"))
            .unwrap_or(false)
    }

    /// 检查是否支持 256 色
    pub fn supports_256_colors() -> bool {
        std::env::var("TERM")
            .map(|term| term.contains("256") || term.contains("xterm"))
            .unwrap_or(false) || supports_truecolor()
    }

    /// 检查是否应该禁用颜色
    pub fn should_disable_colors() -> bool {
        std::env::var("NO_COLOR").is_ok() || 
        std::env::var("TERM").map(|term| term == "dumb").unwrap_or(false)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_color_initialization() {
        init_global_theme_manager();
        
        // 测试基本颜色访问
        let _ = Colors::foreground();
        let _ = Colors::background();
        let _ = Colors::accent_blue();
    }

    #[test]
    fn test_status_colors() {
        init_global_theme_manager();
        
        assert_eq!(Colors::status_color("success"), Colors::success());
        assert_eq!(Colors::status_color("error"), Colors::error());
        assert_eq!(Colors::status_color("warning"), Colors::warning());
    }

    #[test]
    fn test_gradient_creation() {
        let gradient = color_utils::create_gradient(Color::Red, Color::Blue, 5);
        assert_eq!(gradient.len(), 5);
    }
}