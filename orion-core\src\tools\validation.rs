//! # 工具参数验证模块
//!
//! 提供工具参数的验证功能，包括类型检查、约束验证和自定义验证规则。

use crate::error::{OrionError, Result};
use super::types::{ToolParameter, ParameterType, ParameterConstraints};
use std::collections::HashMap;
use url::Url;

/// 参数验证器
/// 
/// 提供参数验证的核心功能，支持各种类型的参数验证和约束检查。
pub struct ParameterValidator;

impl ParameterValidator {
    /// 创建新的参数验证器
    pub fn new() -> Self {
        Self
    }
    
    /// 验证参数映射
    /// 
    /// 验证一组参数是否符合工具定义的要求。
    /// 
    /// # 参数
    /// 
    /// * `parameters` - 要验证的参数映射
    /// * `param_definitions` - 参数定义列表
    /// * `tool_name` - 工具名称（用于错误信息）
    pub fn validate_parameters(
        &self,
        parameters: &HashMap<String, serde_json::Value>,
        param_definitions: &[ToolParameter],
        tool_name: &str,
    ) -> Result<()> {
        // 检查必需参数
        for param in param_definitions {
            if param.required && !parameters.contains_key(&param.name) {
                return Err(OrionError::ToolError(format!(
                    "{}: 缺少必需参数: {}", 
                    tool_name, 
                    param.name
                )));
            }
        }
        
        // 验证每个参数
        for (name, value) in parameters {
            if let Some(param) = param_definitions.iter().find(|p| &p.name == name) {
                self.validate_parameter_value(param, value)?;
            } else {
                // 可选：警告未知参数
                tracing::warn!("{}: 未知参数: {}", tool_name, name);
            }
        }
        
        Ok(())
    }
    
    /// 验证单个参数值
    /// 
    /// 验证单个参数的类型和约束条件。
    /// 
    /// # 参数
    /// 
    /// * `param` - 参数定义
    /// * `value` - 参数值
    pub fn validate_parameter_value(
        &self,
        param: &ToolParameter,
        value: &serde_json::Value,
    ) -> Result<()> {
        // 首先验证基础类型
        self.validate_parameter_type(&param.param_type, value, &param.name)?;
        
        // 然后验证约束条件
        if let Some(constraints) = &param.constraints {
            self.validate_parameter_constraints(constraints, value, &param.name)?;
        }
        
        Ok(())
    }
    
    /// 验证参数类型
    /// 
    /// 检查参数值是否符合指定的类型。
    fn validate_parameter_type(
        &self,
        param_type: &ParameterType,
        value: &serde_json::Value,
        param_name: &str,
    ) -> Result<()> {
        match (param_type, value) {
            (ParameterType::String, serde_json::Value::String(_)) => Ok(()),
            (ParameterType::Integer, serde_json::Value::Number(n)) => {
                if n.is_i64() {
                    Ok(())
                } else {
                    Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不是有效的整数", 
                        param_name
                    )))
                }
            }
            (ParameterType::Float, serde_json::Value::Number(n)) => {
                if n.is_f64() {
                    Ok(())
                } else {
                    Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不是有效的浮点数", 
                        param_name
                    )))
                }
            }
            (ParameterType::Boolean, serde_json::Value::Bool(_)) => Ok(()),
            (ParameterType::Array(element_type), serde_json::Value::Array(arr)) => {
                // 验证数组中每个元素的类型
                for (index, element) in arr.iter().enumerate() {
                    let element_name = format!("{}[{}]", param_name, index);
                    self.validate_parameter_type(element_type, element, &element_name)?;
                }
                Ok(())
            }
            (ParameterType::Object, serde_json::Value::Object(_)) => Ok(()),
            (ParameterType::FilePath, serde_json::Value::String(path)) => {
                // 验证文件路径格式
                if path.is_empty() {
                    Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 文件路径不能为空", 
                        param_name
                    )))
                } else {
                    Ok(())
                }
            }
            (ParameterType::Url, serde_json::Value::String(url_str)) => {
                // 验证 URL 格式
                Url::parse(url_str).map_err(|_| {
                    OrionError::ToolError(format!(
                        "validation: 参数 {} 不是有效的 URL 格式",
                        param_name
                    ))
                })?;
                Ok(())
            }
            (ParameterType::Json, _) => {
                // JSON 类型可以是任何有效的 JSON 值
                Ok(())
            }
            _ => Err(OrionError::ToolError(format!(
                "validation: 参数 {} 类型不匹配，期望 {:?}，实际 {:?}", 
                param_name,
                param_type,
                value
            ))),
        }
    }
    
    /// 验证参数约束条件
    /// 
    /// 检查参数值是否满足约束条件。
    fn validate_parameter_constraints(
        &self,
        constraints: &ParameterConstraints,
        value: &serde_json::Value,
        param_name: &str,
    ) -> Result<()> {
        // 验证枚举值约束
        if let Some(enum_values) = &constraints.enum_values {
            if !enum_values.contains(value) {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 的值不在允许的枚举值中", 
                    param_name
                )));
            }
        }
        
        match value {
            serde_json::Value::String(s) => {
                self.validate_string_constraints(constraints, s, param_name)?;
            }
            serde_json::Value::Number(n) => {
                self.validate_number_constraints(constraints, n, param_name)?;
            }
            serde_json::Value::Array(arr) => {
                self.validate_array_constraints(constraints, arr, param_name)?;
            }
            _ => {
                // 其他类型暂不支持额外约束
            }
        }
        
        Ok(())
    }
    
    /// 验证字符串约束
    fn validate_string_constraints(
        &self,
        constraints: &ParameterConstraints,
        value: &str,
        param_name: &str,
    ) -> Result<()> {
        // 验证长度约束
        if let Some(min_len) = constraints.min_length {
            if value.len() < min_len {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 长度不能少于 {} 字符", 
                    param_name, 
                    min_len
                )));
            }
        }
        
        if let Some(max_len) = constraints.max_length {
            if value.len() > max_len {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 长度不能超过 {} 字符", 
                    param_name, 
                    max_len
                )));
            }
        }
        
        // 验证正则表达式模式
        if let Some(pattern) = &constraints.pattern {
            let regex = regex::Regex::new(pattern)
                .map_err(|e| OrionError::ToolError(format!(
                    "validation: 无效的正则表达式: {}", e
                )))?;
            if !regex.is_match(value) {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 不匹配模式: {}", 
                    param_name, 
                    pattern
                )));
            }
        }
        
        Ok(())
    }
    
    /// 验证数字约束
    fn validate_number_constraints(
        &self,
        constraints: &ParameterConstraints,
        value: &serde_json::Number,
        param_name: &str,
    ) -> Result<()> {
        if let Some(num_value) = value.as_f64() {
            // 验证数值范围约束
            if let Some(min_val) = constraints.min_value {
                if num_value < min_val {
                    return Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不能小于 {}", 
                        param_name, 
                        min_val
                    )));
                }
            }
            
            if let Some(max_val) = constraints.max_value {
                if num_value > max_val {
                    return Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不能大于 {}", 
                        param_name, 
                        max_val
                    )));
                }
            }
        }
        
        Ok(())
    }
    
    /// 验证数组约束
    fn validate_array_constraints(
        &self,
        constraints: &ParameterConstraints,
        value: &[serde_json::Value],
        param_name: &str,
    ) -> Result<()> {
        // 验证数组长度约束
        if let Some(min_len) = constraints.min_length {
            if value.len() < min_len {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 数组长度不能少于 {} 个元素", 
                    param_name, 
                    min_len
                )));
            }
        }
        
        if let Some(max_len) = constraints.max_length {
            if value.len() > max_len {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 数组长度不能超过 {} 个元素", 
                    param_name, 
                    max_len
                )));
            }
        }
        
        Ok(())
    }
}

impl Default for ParameterValidator {
    fn default() -> Self {
        Self::new()
    }
}

/// 验证工具参数的便捷函数
/// 
/// # 参数
/// 
/// * `parameters` - 要验证的参数映射
/// * `param_definitions` - 参数定义列表
/// * `tool_name` - 工具名称
pub fn validate_tool_parameters(
    parameters: &HashMap<String, serde_json::Value>,
    param_definitions: &[ToolParameter],
    tool_name: &str,
) -> Result<()> {
    let validator = ParameterValidator::new();
    validator.validate_parameters(parameters, param_definitions, tool_name)
}

/// 验证单个参数的便捷函数
/// 
/// # 参数
/// 
/// * `param` - 参数定义
/// * `value` - 参数值
pub fn validate_parameter(
    param: &ToolParameter,
    value: &serde_json::Value,
) -> Result<()> {
    let validator = ParameterValidator::new();
    validator.validate_parameter_value(param, value)
}
