@echo off
REM Orion 开发环境包装器 (Windows 版本)

setlocal enabledelayedexpansion

set ORION_ROOT=%~dp0..
set CACHE_DIR=%ORION_ROOT%\.dev-cache
set BINARY_PATH=%ORION_ROOT%\target\debug\orion.exe
set LAST_BUILD_FILE=%CACHE_DIR%\last_build_time

REM 创建缓存目录
if not exist "%CACHE_DIR%" mkdir "%CACHE_DIR%"

REM 检查是否需要重新编译
call :need_rebuild
if %errorlevel% equ 1 (
    call :build_project
    if !errorlevel! neq 0 exit /b 1
)

REM 执行 orion 命令
"%BINARY_PATH%" %*
exit /b %errorlevel%

:need_rebuild
REM 检查二进制文件是否存在
if not exist "%BINARY_PATH%" exit /b 1

REM 检查源码文件的最新修改时间
set newest_source=0
for /r "%ORION_ROOT%\orion-cli\src" %%f in (*.rs) do call :get_file_time "%%f"
for /r "%ORION_ROOT%\orion-core\src" %%f in (*.rs) do call :get_file_time "%%f"
call :get_file_time "%ORION_ROOT%\Cargo.toml"
call :get_file_time "%ORION_ROOT%\orion-cli\Cargo.toml"
call :get_file_time "%ORION_ROOT%\orion-core\Cargo.toml"

REM 读取上次构建时间
set last_build_time=0
if exist "%LAST_BUILD_FILE%" (
    set /p last_build_time=<"%LAST_BUILD_FILE%"
)

REM 比较时间
if %newest_source% gtr %last_build_time% exit /b 1
exit /b 0

:get_file_time
for %%i in ("%~1") do (
    set file_time=%%~ti
    set file_time=!file_time: =!
    set file_time=!file_time:/=!
    set file_time=!file_time::=!
    if !file_time! gtr !newest_source! set newest_source=!file_time!
)
exit /b 0

:build_project
echo 🔨 检测到代码变更，正在重新编译...
cd /d "%ORION_ROOT%"

cargo build --bin orion
if %errorlevel% equ 0 (
    echo %date%%time% > "%LAST_BUILD_FILE%"
    echo ✅ 编译完成
    exit /b 0
) else (
    echo ❌ 编译失败
    exit /b 1
)