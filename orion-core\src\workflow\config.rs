//! # 工作流配置模块
//!
//! 包含工作流和步骤的各种配置选项。
//! 定义了执行策略、资源限制、重试机制、错误处理等配置参数。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 工作流全局配置
/// 
/// 定义工作流执行的全局参数和策略。
/// 这些配置影响整个工作流的执行行为。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowConfig {
    /// 最大执行时间限制（秒）
    /// None 表示无时间限制
    pub max_execution_time_seconds: Option<u64>,
    
    /// 最大并发步骤数
    /// 控制同时执行的步骤数量，防止资源过度消耗
    pub max_concurrent_steps: Option<u32>,
    
    /// 是否启用状态持久化
    /// 启用后会将执行状态保存到存储中，支持断点续传
    pub enable_state_persistence: bool,
    
    /// 是否启用执行日志记录
    /// 启用后会详细记录每个步骤的执行过程
    pub enable_execution_logging: bool,
    
    /// 错误处理策略
    /// 定义遇到错误时的处理方式
    pub error_handling_strategy: ErrorHandlingStrategy,
    
    /// 变量作用域策略
    /// 定义变量的可见性和生命周期
    pub variable_scope: VariableScope,
}

impl Default for WorkflowConfig {
    /// 创建默认的工作流配置
    fn default() -> Self {
        Self {
            max_execution_time_seconds: Some(3600), // 默认1小时超时
            max_concurrent_steps: Some(10),         // 默认最多10个并发步骤
            enable_state_persistence: true,         // 默认启用状态持久化
            enable_execution_logging: true,         // 默认启用执行日志
            error_handling_strategy: ErrorHandlingStrategy::StopImmediately,
            variable_scope: VariableScope::Global,
        }
    }
}

/// 步骤配置
/// 
/// 定义单个步骤的执行配置参数。
/// 这些配置只影响特定步骤的执行行为。
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct StepConfig {
    /// 是否可跳过
    /// 当条件不满足时是否可以跳过此步骤
    pub skippable: bool,
    
    /// 是否支持并行执行
    /// 标记此步骤是否可以与其他步骤并行执行
    pub parallel: bool,
    
    /// 执行优先级
    /// 数值越大优先级越高，影响调度顺序
    pub priority: i32,
    
    /// 资源使用限制
    /// 定义此步骤可使用的最大资源量
    pub resource_limits: Option<ResourceLimits>,
    
    /// 自定义属性
    /// 存储步骤特定的配置参数
    pub custom_properties: HashMap<String, serde_json::Value>,
}

/// 资源限制配置
/// 
/// 定义步骤执行时的资源使用限制。
/// 用于防止单个步骤消耗过多系统资源。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResourceLimits {
    /// 最大内存使用量（字节）
    /// None 表示无内存限制
    pub max_memory_bytes: Option<u64>,
    
    /// 最大CPU使用率（0.0-1.0）
    /// 1.0 表示100%的CPU使用率
    pub max_cpu_usage: Option<f64>,
    
    /// 最大并发连接数
    /// 限制网络请求或数据库连接的数量
    pub max_concurrency: Option<u32>,
}

/// 重试配置
/// 
/// 定义步骤失败时的重试策略。
/// 支持多种退避算法和错误类型过滤。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// 最大重试次数
    /// 包括初始执行，总执行次数 = max_attempts
    pub max_attempts: u32,
    
    /// 基础重试间隔（秒）
    /// 实际间隔根据退避策略计算
    pub retry_interval_seconds: u64,
    
    /// 退避策略
    /// 定义重试间隔的计算方式
    pub backoff_strategy: BackoffStrategy,
    
    /// 可重试的错误类型列表
    /// 只有匹配的错误才会触发重试
    pub retryable_errors: Vec<String>,
}

/// 退避策略枚举
/// 
/// 定义重试时间间隔的计算策略。
/// 不同策略适用于不同的场景和错误类型。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackoffStrategy {
    /// 固定间隔
    /// 每次重试使用相同的时间间隔
    Fixed,
    
    /// 线性退避
    /// 重试间隔线性增长：interval * attempt
    Linear,
    
    /// 指数退避
    /// 重试间隔指数增长：interval * 2^(attempt-1)
    Exponential,
    
    /// 自定义退避
    /// 使用预定义的时间间隔序列
    Custom(Vec<u64>),
}

/// 错误处理策略枚举
///
/// 定义工作流遇到错误时的处理方式。
/// 不同策略适用于不同的业务场景。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ErrorHandlingStrategy {
    /// 立即停止执行
    /// 遇到任何错误立即终止整个工作流
    StopImmediately,
    
    /// 继续执行其他步骤
    /// 忽略错误，继续执行后续步骤
    ContinueOnError,
    
    /// 执行错误处理步骤
    /// 跳转到预定义的错误处理步骤
    ExecuteErrorHandler,
    
    /// 回滚到检查点
    /// 恢复到最近的有效检查点状态
    RollbackToCheckpoint,
}

/// 变量作用域枚举
///
/// 定义工作流变量的可见性范围。
/// 影响变量的生命周期和访问权限。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum VariableScope {
    /// 全局作用域
    /// 变量在整个工作流中可见和访问
    Global,
    
    /// 步骤作用域
    /// 变量只在当前步骤中可见
    Step,
    
    /// 分支作用域
    /// 变量在当前分支（条件或循环）中可见
    Branch,
}
