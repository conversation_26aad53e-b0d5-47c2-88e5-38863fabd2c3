//! # 获取配置功能
//!
//! 实现配置项的获取和查询功能。

use crate::error::{CliError, Result};
use crate::commands::config::types::GetConfig;
use orion_core::config::OrionConfig;

impl GetConfig {
    /// 执行获取配置
    pub async fn execute(&self) -> Result<()> {
        // 加载配置
        let config = OrionConfig::from_file(&self.config)
            .map_err(|e| CliError::ConfigError {
                error: format!("加载配置文件失败: {}", e),
            })?;
        
        // 将配置转换为 TOML 值
        let config_value = toml::Value::try_from(&config)
            .map_err(|e| CliError::ConfigError {
                error: format!("配置序列化失败: {}", e),
            })?;
        
        // 解析配置键路径
        let keys: Vec<&str> = self.key.split('.').collect();
        
        // 获取配置值
        let value = self.get_nested_value(&config_value, &keys)?;
        
        // 根据格式输出
        match self.format.as_str() {
            "json" => {
                let json_value = serde_json::to_value(&value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化值失败: {}", e),
                    })?;
                println!("{}", serde_json::to_string_pretty(&json_value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化 JSON 失败: {}", e),
                    })?);
            }
            "yaml" => {
                let yaml_value = serde_yaml::to_value(&value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化值失败: {}", e),
                    })?;
                println!("{}", serde_yaml::to_string(&yaml_value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化 YAML 失败: {}", e),
                    })?);
            }
            "value" | _ => {
                println!("{}", self.format_value(&value));
            }
        }
        
        Ok(())
    }
    
    /// 获取嵌套值
    fn get_nested_value<'a>(&self, current: &'a toml::Value, keys: &[&str]) -> Result<&'a toml::Value> {
        if keys.is_empty() {
            return Ok(current);
        }
        
        match current {
            toml::Value::Table(table) => {
                let key = keys[0];
                if let Some(next_value) = table.get(key) {
                    if keys.len() == 1 {
                        Ok(next_value)
                    } else {
                        self.get_nested_value(next_value, &keys[1..])
                    }
                } else {
                    Err(CliError::ConfigError {
                        error: format!("配置键不存在: {}", key),
                    })
                }
            }
            _ => {
                Err(CliError::ConfigError {
                    error: format!("无法在非表类型中访问键: {}", keys[0]),
                })
            }
        }
    }
    
    /// 格式化值
    fn format_value(&self, value: &toml::Value) -> String {
        match value {
            toml::Value::String(s) => s.clone(),
            toml::Value::Integer(i) => i.to_string(),
            toml::Value::Float(f) => f.to_string(),
            toml::Value::Boolean(b) => b.to_string(),
            toml::Value::Array(arr) => {
                let items: Vec<String> = arr.iter()
                    .map(|v| self.format_value(v))
                    .collect();
                format!("[{}]", items.join(", "))
            }
            toml::Value::Table(_) => "[Table]".to_string(),
            toml::Value::Datetime(dt) => dt.to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test] 
    fn test_format_value() {
        let cmd = GetConfig {
            config: std::path::PathBuf::from("test.toml"),
            key: "test".to_string(),
            format: "value".to_string(),
        };
        
        assert_eq!(cmd.format_value(&toml::Value::String("test".to_string())), "test");
        assert_eq!(cmd.format_value(&toml::Value::Integer(42)), "42");
        assert_eq!(cmd.format_value(&toml::Value::Boolean(true)), "true");
    }
}