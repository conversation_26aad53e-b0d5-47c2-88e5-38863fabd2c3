//! # 安全沙箱执行环境
//!
//! 提供隔离的代码执行环境，支持资源限制、超时控制和安全监控。
//! 实现跨平台的进程管理和资源监控功能。
//!
//! ## 模块结构
//!
//! - `types` - 核心类型定义
//! - `config` - 配置管理
//! - `executor` - 执行器
//! - `monitor` - 资源监控
//! - `security` - 安全控制
//! - `manager` - 核心管理器
//!
//! ## 使用示例
//!
//! ```rust,no_run
//! use orion_core::sandbox::{Sandbox, SandboxConfig, ScriptType};
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // 创建沙箱
//!     let sandbox = Sandbox::new();
//!
//!     // 执行命令（需要系统中有对应的程序）
//!     let result = sandbox.execute_command("echo", &["Hello, World!"], None).await?;
//!     println!("输出: {}", result.stdout);
//!
//!     // 执行脚本（需要系统中安装了 Python）
//!     let script = "print('Hello from Python!')";
//!     let result = sandbox.execute_script(script, ScriptType::Python, None).await?;
//!     println!("脚本输出: {}", result.stdout);
//!
//!     Ok(())
//! }
//! ```
//!
//! ## 基本类型使用示例
//!
//! ```rust
//! use orion_core::sandbox::{SandboxConfig, ScriptType, NetworkAccess, FilesystemAccess};
//! use std::collections::HashMap;
//! use std::path::PathBuf;
//!
//! // 创建沙箱配置
//! let config = SandboxConfig {
//!     timeout_seconds: 30,
//!     max_memory_mb: 512,
//!     max_cpu_percent: 80.0,
//!     working_directory: None,
//!     environment: HashMap::new(),
//!     network_access: NetworkAccess::Denied,
//!     filesystem_access: FilesystemAccess::ReadOnly(vec![PathBuf::from("/tmp")]),
//!     allow_subprocesses: false,
//!     cleanup_temp_files: true,
//! };
//!
//! // 验证配置
//! assert_eq!(config.timeout_seconds, 30);
//! assert_eq!(config.max_memory_mb, 512);
//! assert!(matches!(config.network_access, NetworkAccess::Denied));
//!
//! // 脚本类型
//! let script_types = vec![
//!     ScriptType::Python,
//!     ScriptType::JavaScript,
//!     ScriptType::Shell,
//!     ScriptType::PowerShell,
//! ];
//!
//! for script_type in script_types {
//!     println!("支持的脚本类型: {:?}", script_type);
//! }
//!
//! // 网络访问控制
//! let network_configs = vec![
//!     NetworkAccess::Denied,
//!     NetworkAccess::Full,
//!     NetworkAccess::Restricted(vec!["example.com".to_string()]),
//! ];
//!
//! for network_config in network_configs {
//!     println!("网络访问配置: {:?}", network_config);
//! }
//! ```

// 子模块声明
pub mod types;
pub mod config;
pub mod executor;
pub mod monitor;
pub mod security;
pub mod manager;

// 重新导出核心类型，保持向后兼容性
pub use types::{
    SandboxConfig, ExecutionResult, NetworkAccess, FilesystemAccess, 
    ScriptType, ExecutionContext, ResourceMonitor
};

// 重新导出配置相关
pub use config::{
    SandboxConfigBuilder, ConfigPresets, ConfigValidator
};

// 重新导出执行器
pub use executor::SandboxExecutor;

// 重新导出监控相关
pub use monitor::{
    ProcessMonitorResult, SystemResourceOverview
};

// 重新导出安全相关
pub use security::{
    SecurityManager, SecuritySummary
};

// 重新导出管理器
pub use manager::{
    Sandbox, SandboxManager, SandboxStats, ManagerStats
};

/// 沙箱系统构建器
/// 
/// 提供便捷的沙箱系统创建方式
pub struct SandboxSystemBuilder {
    config: Option<SandboxConfig>,
    name: Option<String>,
}

impl SandboxSystemBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: None,
            name: None,
        }
    }

    /// 设置配置
    pub fn with_config(mut self, config: SandboxConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// 使用预设配置
    pub fn with_preset(mut self, preset_name: &str) -> Self {
        if let Some(config) = ConfigPresets::get_preset(preset_name) {
            self.config = Some(config);
        }
        self
    }

    /// 设置名称
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 构建单个沙箱
    pub fn build_sandbox(self) -> Sandbox {
        match self.config {
            Some(config) => Sandbox::with_config(config),
            None => Sandbox::new(),
        }
    }

    /// 构建沙箱管理器并创建沙箱
    pub async fn build_manager(self) -> crate::error::Result<(SandboxManager, String)> {
        let manager = SandboxManager::new();
        let name = self.name.unwrap_or_else(|| "default".to_string());
        
        manager.create_sandbox(name.clone(), self.config).await?;
        manager.set_default_sandbox(name.clone()).await?;
        
        Ok((manager, name))
    }
}

impl Default for SandboxSystemBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷函数：创建默认沙箱
pub fn create_default_sandbox() -> Sandbox {
    Sandbox::new()
}

/// 便捷函数：创建带配置的沙箱
pub fn create_sandbox_with_config(config: SandboxConfig) -> Sandbox {
    Sandbox::with_config(config)
}

/// 便捷函数：创建沙箱管理器
pub fn create_sandbox_manager() -> SandboxManager {
    SandboxManager::new()
}

/// 便捷函数：创建严格安全配置的沙箱
pub fn create_secure_sandbox() -> Sandbox {
    Sandbox::with_config(ConfigPresets::strict_security())
}

/// 便捷函数：创建开发环境配置的沙箱
pub fn create_development_sandbox() -> Sandbox {
    Sandbox::with_config(ConfigPresets::development())
}

/// 便捷函数：创建生产环境配置的沙箱
pub fn create_production_sandbox() -> Sandbox {
    Sandbox::with_config(ConfigPresets::production())
}

/// 便捷函数：创建测试环境配置的沙箱
pub fn create_testing_sandbox() -> Sandbox {
    Sandbox::with_config(ConfigPresets::testing())
}

/// 沙箱系统信息
/// 
/// 提供整个沙箱系统的概览信息
#[derive(Debug, Clone)]
pub struct SandboxSystemInfo {
    /// 版本信息
    pub version: String,
    /// 支持的脚本类型
    pub supported_script_types: Vec<ScriptType>,
    /// 可用的预设配置
    pub available_presets: Vec<String>,
    /// 系统资源概览
    pub system_overview: Option<SystemResourceOverview>,
}

impl SandboxSystemInfo {
    /// 获取系统信息
    pub async fn get() -> Self {
        Self {
            version: env!("CARGO_PKG_VERSION").to_string(),
            supported_script_types: vec![
                ScriptType::Python,
                ScriptType::JavaScript,
                ScriptType::Shell,
                ScriptType::PowerShell,
            ],
            available_presets: ConfigPresets::list_presets().into_iter().map(|s| s.to_string()).collect(),
            system_overview: Some(monitor::ResourceMonitor::get_system_overview().await),
        }
    }
}

/// 沙箱系统工具函数
pub mod utils {
    use super::*;
    use crate::error::Result;

    /// 验证脚本安全性
    pub fn validate_script_security(script_content: &str, script_type: ScriptType) -> Result<()> {
        let security_manager = SecurityManager::new();
        security_manager.validate_script_security(script_content, script_type)
    }

    /// 检查命令是否安全
    pub fn is_command_safe(command: &str, config: &SandboxConfig) -> bool {
        let security_manager = SecurityManager::new();
        security_manager.is_command_allowed(command, config)
    }

    /// 验证配置安全性
    pub fn validate_config_security(config: &SandboxConfig) -> std::result::Result<(), Vec<String>> {
        ConfigValidator::validate_security(config)
    }

    /// 获取推荐配置
    pub fn get_recommended_config_for_use_case(use_case: &str) -> Option<SandboxConfig> {
        match use_case.to_lowercase().as_str() {
            "development" | "dev" => Some(ConfigPresets::development()),
            "production" | "prod" => Some(ConfigPresets::production()),
            "testing" | "test" => Some(ConfigPresets::testing()),
            "security" | "secure" => Some(ConfigPresets::strict_security()),
            "performance" | "perf" => Some(ConfigPresets::high_performance()),
            "lightweight" | "light" => Some(ConfigPresets::lightweight()),
            _ => None,
        }
    }

    /// 格式化执行结果
    pub fn format_execution_result(result: &ExecutionResult) -> String {
        format!(
            "执行ID: {}\n状态: {}\n执行时间: {}ms\n内存使用: {}MB\nCPU使用: {:.1}%\n标准输出:\n{}\n标准错误:\n{}",
            result.id,
            result.status_description(),
            result.execution_time_ms,
            result.peak_memory_mb,
            result.cpu_usage_percent,
            result.stdout,
            result.stderr
        )
    }

    /// 格式化统计信息
    pub fn format_sandbox_stats(stats: &SandboxStats) -> String {
        format!(
            "总执行次数: {}\n成功执行: {}\n失败执行: {}\n成功率: {:.1}%\n平均执行时间: {:.1}ms\n总脚本执行: {}\n脚本成功率: {:.1}%",
            stats.total_executions,
            stats.successful_executions,
            stats.failed_executions,
            stats.success_rate(),
            stats.average_execution_time_ms(),
            stats.total_script_executions,
            stats.script_success_rate()
        )
    }
}

// 常量定义
pub mod constants {
    /// 默认超时时间（秒）
    pub const DEFAULT_TIMEOUT_SECONDS: u64 = 30;
    
    /// 默认内存限制（MB）
    pub const DEFAULT_MEMORY_LIMIT_MB: u64 = 512;
    
    /// 默认 CPU 限制（百分比）
    pub const DEFAULT_CPU_LIMIT_PERCENT: f32 = 80.0;
    
    /// 最大执行历史记录数
    pub const MAX_EXECUTION_HISTORY: usize = 1000;
    
    /// 历史记录清理批次大小
    pub const HISTORY_CLEANUP_BATCH_SIZE: usize = 100;
    
    /// 资源监控间隔（毫秒）
    pub const RESOURCE_MONITOR_INTERVAL_MS: u64 = 100;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_module_exports() {
        // 测试类型导出
        let _config = SandboxConfig::default();
        let _result = ExecutionResult::new(uuid::Uuid::new_v4());
        
        // 测试枚举导出
        let _script_type = ScriptType::Python;
        let _network_access = NetworkAccess::Denied;
        let _filesystem_access = FilesystemAccess::Restricted(vec![]);
    }

    #[tokio::test]
    async fn test_builder() {
        let builder = SandboxSystemBuilder::new()
            .with_preset("testing")
            .with_name("test_sandbox".to_string());

        let _sandbox = builder.build_sandbox();
        // 验证沙箱创建成功
        assert!(true); // 简单验证，实际应该检查配置
    }

    #[tokio::test]
    async fn test_convenience_functions() {
        let _sandbox1 = create_default_sandbox();
        let _sandbox2 = create_secure_sandbox();
        let _sandbox3 = create_development_sandbox();
        let _manager = create_sandbox_manager();
    }

    #[tokio::test]
    async fn test_system_info() {
        let info = SandboxSystemInfo::get().await;
        assert!(!info.version.is_empty());
        assert!(!info.supported_script_types.is_empty());
        assert!(!info.available_presets.is_empty());
    }

    #[test]
    fn test_utils() {
        let config = ConfigPresets::testing();
        
        // 测试命令安全检查
        assert!(utils::is_command_safe("echo", &config));
        assert!(!utils::is_command_safe("rm", &config));
        
        // 测试推荐配置
        assert!(utils::get_recommended_config_for_use_case("development").is_some());
        assert!(utils::get_recommended_config_for_use_case("unknown").is_none());
    }
}
