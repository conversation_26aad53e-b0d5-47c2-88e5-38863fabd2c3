//! # 工具系统核心 Trait 定义
//!
//! 定义工具系统的核心接口，包括 Tool trait 和相关的行为定义。

use crate::error::{OrionError, Result};
use super::types::{ToolDefinition, ToolRequest, ToolResult, ToolParameter};
use async_trait::async_trait;
use std::collections::HashMap;
use url::Url;

/// 工具接口定义
/// 
/// 所有工具都必须实现此 trait，提供工具的基本信息和执行能力。
/// 
/// # 示例
/// 
/// ```rust
/// use orion_core::tools::{Tool, ToolDefinition, ToolRequest, ToolResult};
/// use orion_core::error::Result;
/// use async_trait::async_trait;
///
/// struct MyTool;
///
/// #[async_trait]
/// impl Tool for MyTool {
///     fn definition(&self) -> ToolDefinition {
///         // 返回工具定义
///         todo!()
///     }
///
///     async fn execute(&self, request: ToolRequest) -> Result<ToolResult> {
///         // 执行工具逻辑
///         todo!()
///     }
/// }
/// ```
#[async_trait]
pub trait Tool: Send + Sync {
    /// 获取工具定义
    /// 
    /// 返回工具的完整定义信息，包括名称、版本、参数、返回类型等。
    /// 这些信息用于工具注册、参数验证和文档生成。
    fn definition(&self) -> ToolDefinition;
    
    /// 验证工具参数
    /// 
    /// 在执行工具之前验证传入的参数是否符合要求。
    /// 默认实现会检查必需参数和基本类型验证。
    /// 
    /// # 参数
    /// 
    /// * `parameters` - 要验证的参数映射
    /// 
    /// # 返回值
    /// 
    /// 如果参数有效返回 `Ok(())`，否则返回包含错误信息的 `Err`。
    fn validate_parameters(&self, parameters: &HashMap<String, serde_json::Value>) -> Result<()> {
        let definition = self.definition();
        
        // 检查必需参数是否存在
        for param in &definition.parameters {
            if param.required && !parameters.contains_key(&param.name) {
                return Err(OrionError::ToolError(format!(
                    "{}: 缺少必需参数: {}", 
                    definition.name, 
                    param.name
                )));
            }
        }
        
        // 验证每个参数的类型和约束
        for (name, value) in parameters {
            if let Some(param) = definition.parameters.iter().find(|p| &p.name == name) {
                self.validate_parameter_value(param, value)?;
            }
        }
        
        Ok(())
    }
    
    /// 验证单个参数值
    /// 
    /// 验证单个参数的类型和约束条件。子类可以重写此方法来实现
    /// 自定义的参数验证逻辑。
    /// 
    /// # 参数
    /// 
    /// * `param` - 参数定义
    /// * `value` - 参数值
    /// 
    /// # 返回值
    /// 
    /// 如果参数值有效返回 `Ok(())`，否则返回包含错误信息的 `Err`。
    fn validate_parameter_value(&self, param: &ToolParameter, value: &serde_json::Value) -> Result<()> {
        use super::types::ParameterType;
        
        // 基础类型检查
        match (&param.param_type, value) {
            (ParameterType::String, serde_json::Value::String(s)) => {
                if let Some(constraints) = &param.constraints {
                    // 检查字符串长度约束
                    if let Some(min_len) = constraints.min_length {
                        if s.len() < min_len {
                            return Err(OrionError::ToolError(format!(
                                "validation: 参数 {} 长度不能少于 {} 字符", 
                                param.name, 
                                min_len
                            )));
                        }
                    }
                    if let Some(max_len) = constraints.max_length {
                        if s.len() > max_len {
                            return Err(OrionError::ToolError(format!(
                                "validation: 参数 {} 长度不能超过 {} 字符", 
                                param.name, 
                                max_len
                            )));
                        }
                    }
                    
                    // 检查正则表达式模式
                    if let Some(pattern) = &constraints.pattern {
                        let regex = regex::Regex::new(pattern)
                            .map_err(|e| OrionError::ToolError(format!(
                                "validation: 无效的正则表达式: {}", e
                            )))?;
                        if !regex.is_match(s) {
                            return Err(OrionError::ToolError(format!(
                                "validation: 参数 {} 不匹配模式: {}", 
                                param.name, 
                                pattern
                            )));
                        }
                    }
                    
                    // 检查枚举值约束
                    if let Some(enum_values) = &constraints.enum_values {
                        if !enum_values.contains(value) {
                            return Err(OrionError::ToolError(format!(
                                "validation: 参数 {} 的值不在允许的枚举值中", 
                                param.name
                            )));
                        }
                    }
                }
            }
            (ParameterType::Integer, serde_json::Value::Number(n)) => {
                if let Some(i) = n.as_i64() {
                    if let Some(constraints) = &param.constraints {
                        // 检查数值范围约束
                        if let Some(min_val) = constraints.min_value {
                            if (i as f64) < min_val {
                                return Err(OrionError::ToolError(format!(
                                    "validation: 参数 {} 不能小于 {}", 
                                    param.name, 
                                    min_val
                                )));
                            }
                        }
                        if let Some(max_val) = constraints.max_value {
                            if (i as f64) > max_val {
                                return Err(OrionError::ToolError(format!(
                                    "validation: 参数 {} 不能大于 {}", 
                                    param.name, 
                                    max_val
                                )));
                            }
                        }
                        
                        // 检查枚举值约束
                        if let Some(enum_values) = &constraints.enum_values {
                            if !enum_values.contains(value) {
                                return Err(OrionError::ToolError(format!(
                                    "validation: 参数 {} 的值不在允许的枚举值中", 
                                    param.name
                                )));
                            }
                        }
                    }
                } else {
                    return Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不是有效的整数", 
                        param.name
                    )));
                }
            }
            (ParameterType::Float, serde_json::Value::Number(n)) => {
                if let Some(f) = n.as_f64() {
                    if let Some(constraints) = &param.constraints {
                        // 检查数值范围约束
                        if let Some(min_val) = constraints.min_value {
                            if f < min_val {
                                return Err(OrionError::ToolError(format!(
                                    "validation: 参数 {} 不能小于 {}", 
                                    param.name, 
                                    min_val
                                )));
                            }
                        }
                        if let Some(max_val) = constraints.max_value {
                            if f > max_val {
                                return Err(OrionError::ToolError(format!(
                                    "validation: 参数 {} 不能大于 {}", 
                                    param.name, 
                                    max_val
                                )));
                            }
                        }
                    }
                } else {
                    return Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不是有效的浮点数", 
                        param.name
                    )));
                }
            }
            (ParameterType::Boolean, serde_json::Value::Bool(_)) => {
                // 布尔值类型检查通过
            }
            (ParameterType::Array(_), serde_json::Value::Array(arr)) => {
                if let Some(constraints) = &param.constraints {
                    // 检查数组长度约束
                    if let Some(min_len) = constraints.min_length {
                        if arr.len() < min_len {
                            return Err(OrionError::ToolError(format!(
                                "validation: 参数 {} 数组长度不能少于 {} 个元素", 
                                param.name, 
                                min_len
                            )));
                        }
                    }
                    if let Some(max_len) = constraints.max_length {
                        if arr.len() > max_len {
                            return Err(OrionError::ToolError(format!(
                                "validation: 参数 {} 数组长度不能超过 {} 个元素", 
                                param.name, 
                                max_len
                            )));
                        }
                    }
                }
            }
            (ParameterType::Object, serde_json::Value::Object(_)) => {
                // 对象类型检查通过
            }
            (ParameterType::FilePath, serde_json::Value::String(_)) => {
                // 文件路径类型检查通过（作为字符串处理）
            }
            (ParameterType::Url, serde_json::Value::String(url_str)) => {
                // 验证 URL 格式
                if Url::parse(url_str).is_err() {
                    return Err(OrionError::ToolError(format!(
                        "validation: 参数 {} 不是有效的 URL 格式",
                        param.name
                    )));
                }
            }
            (ParameterType::Json, _) => {
                // JSON 类型可以是任何有效的 JSON 值
            }
            _ => {
                return Err(OrionError::ToolError(format!(
                    "validation: 参数 {} 类型不匹配，期望 {:?}，实际 {:?}", 
                    param.name,
                    param.param_type,
                    value
                )));
            }
        }
        
        Ok(())
    }
    
    /// 执行工具
    /// 
    /// 这是工具的核心方法，执行具体的工具逻辑。
    /// 
    /// # 参数
    /// 
    /// * `request` - 工具执行请求，包含参数和上下文信息
    /// 
    /// # 返回值
    /// 
    /// 返回工具执行结果，包含成功状态、数据和资源使用情况。
    async fn execute(&self, request: ToolRequest) -> Result<ToolResult>;
}

/// 工具构建器 trait
/// 
/// 为工具提供构建器模式的支持，允许灵活配置工具实例。
pub trait ToolBuilder {
    /// 构建器生成的工具类型
    type Tool: Tool;
    
    /// 构建工具实例
    fn build(self) -> Result<Self::Tool>;
}

/// 工具工厂 trait
/// 
/// 提供工具的工厂方法，支持动态创建工具实例。
pub trait ToolFactory {
    /// 创建工具实例
    /// 
    /// # 参数
    /// 
    /// * `tool_name` - 工具名称
    /// * `config` - 工具配置
    fn create_tool(&self, tool_name: &str, config: Option<serde_json::Value>) -> Result<Box<dyn Tool>>;
    
    /// 获取支持的工具列表
    fn supported_tools(&self) -> Vec<String>;
}
