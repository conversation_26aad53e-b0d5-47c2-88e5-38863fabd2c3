//! # Agent 状态管理模块
//!
//! 提供Agent状态管理功能，包括状态转换、暂停、恢复、停止等操作。
//! 确保状态转换的安全性和一致性。

use crate::agent::types::AgentState;
use crate::error::{OrionError, Result};
use std::sync::Arc;
use tokio::sync::RwLock;

/// Agent状态管理器
/// 
/// 负责管理Agent的状态转换和状态相关操作
pub struct StateManager {
    /// 当前状态，使用RwLock保证线程安全
    current_state: Arc<RwLock<AgentState>>,
    /// Agent名称，用于日志记录
    agent_name: String,
}

impl StateManager {
    /// 创建新的状态管理器
    /// 
    /// # 参数
    /// * `agent_name` - Agent名称，用于日志记录
    /// 
    /// # 返回
    /// 返回新创建的StateManager实例
    pub fn new(agent_name: String) -> Self {
        Self {
            current_state: Arc::new(RwLock::new(AgentState::Idle)),
            agent_name,
        }
    }

    /// 获取当前状态
    /// 
    /// # 返回
    /// 返回当前Agent状态的克隆
    pub async fn get_state(&self) -> AgentState {
        self.current_state.read().await.clone()
    }

    /// 设置新状态
    /// 
    /// # 参数
    /// * `new_state` - 要设置的新状态
    /// 
    /// # 返回
    /// 如果状态转换成功返回Ok(())，否则返回错误
    pub async fn set_state(&self, new_state: AgentState) -> Result<()> {
        let current = self.get_state().await;
        
        // 验证状态转换的合法性
        if !self.is_valid_transition(&current, &new_state) {
            return Err(OrionError::AgentError(
                format!("无效的状态转换: {:?} -> {:?}", current, new_state)
            ));
        }

        // 执行状态转换
        {
            let mut state = self.current_state.write().await;
            *state = new_state.clone();
        }

        tracing::debug!(
            "Agent '{}' 状态已更改: {:?} -> {:?}",
            self.agent_name,
            current,
            new_state
        );

        Ok(())
    }

    /// 暂停Agent
    /// 
    /// 将Agent状态设置为暂停，只有在特定状态下才能暂停
    /// 
    /// # 返回
    /// 如果暂停成功返回Ok(())，否则返回错误
    pub async fn pause(&self) -> Result<()> {
        let current_state = self.get_state().await;
        
        match current_state {
            AgentState::Idle | AgentState::Thinking | AgentState::WaitingForInput => {
                self.set_state(AgentState::Paused).await?;
                tracing::info!("Agent '{}' 已暂停", self.agent_name);
                Ok(())
            }
            AgentState::Paused => {
                tracing::warn!("Agent '{}' 已经处于暂停状态", self.agent_name);
                Ok(())
            }
            _ => {
                Err(OrionError::AgentError(
                    format!("无法在当前状态下暂停Agent: {:?}", current_state)
                ))
            }
        }
    }

    /// 恢复Agent
    /// 
    /// 从暂停状态恢复到空闲状态
    /// 
    /// # 返回
    /// 如果恢复成功返回Ok(())，否则返回错误
    pub async fn resume(&self) -> Result<()> {
        let current_state = self.get_state().await;
        
        match current_state {
            AgentState::Paused => {
                self.set_state(AgentState::Idle).await?;
                tracing::info!("Agent '{}' 已恢复", self.agent_name);
                Ok(())
            }
            AgentState::Idle => {
                tracing::warn!("Agent '{}' 已经处于运行状态", self.agent_name);
                Ok(())
            }
            _ => {
                Err(OrionError::AgentError(
                    format!("只能从暂停状态恢复Agent，当前状态: {:?}", current_state)
                ))
            }
        }
    }

    /// 停止Agent
    /// 
    /// 将Agent状态设置为停止，这是一个终止状态
    /// 
    /// # 返回
    /// 如果停止成功返回Ok(())，否则返回错误
    pub async fn stop(&self) -> Result<()> {
        let current_state = self.get_state().await;
        
        if current_state == AgentState::Stopped {
            tracing::warn!("Agent '{}' 已经处于停止状态", self.agent_name);
            return Ok(());
        }

        self.set_state(AgentState::Stopped).await?;
        tracing::info!("Agent '{}' 已停止", self.agent_name);
        Ok(())
    }

    /// 检查Agent是否处于活跃状态
    /// 
    /// # 返回
    /// 如果Agent可以处理任务返回true，否则返回false
    pub async fn is_active(&self) -> bool {
        let state = self.get_state().await;
        matches!(
            state,
            AgentState::Idle 
            | AgentState::Thinking 
            | AgentState::ExecutingTool 
            | AgentState::ExecutingWorkflow
            | AgentState::WaitingForInput
        )
    }

    /// 检查Agent是否可以接受新任务
    /// 
    /// # 返回
    /// 如果Agent可以接受新任务返回true，否则返回false
    pub async fn can_accept_task(&self) -> bool {
        let state = self.get_state().await;
        matches!(state, AgentState::Idle)
    }

    /// 检查Agent是否处于错误状态
    /// 
    /// # 返回
    /// 如果Agent处于错误状态返回true，否则返回false
    pub async fn is_error(&self) -> bool {
        let state = self.get_state().await;
        matches!(state, AgentState::Error)
    }

    /// 从错误状态恢复
    /// 
    /// 将Agent从错误状态恢复到空闲状态
    /// 
    /// # 返回
    /// 如果恢复成功返回Ok(())，否则返回错误
    pub async fn recover_from_error(&self) -> Result<()> {
        let current_state = self.get_state().await;
        
        if current_state == AgentState::Error {
            self.set_state(AgentState::Idle).await?;
            tracing::info!("Agent '{}' 已从错误状态恢复", self.agent_name);
            Ok(())
        } else {
            Err(OrionError::AgentError(
                format!("Agent不在错误状态，当前状态: {:?}", current_state)
            ))
        }
    }

    /// 设置错误状态
    /// 
    /// 当Agent遇到无法处理的错误时调用
    /// 
    /// # 返回
    /// 如果设置成功返回Ok(())，否则返回错误
    pub async fn set_error(&self) -> Result<()> {
        self.set_state(AgentState::Error).await?;
        tracing::error!("Agent '{}' 进入错误状态", self.agent_name);
        Ok(())
    }

    /// 验证状态转换是否合法
    /// 
    /// # 参数
    /// * `from` - 当前状态
    /// * `to` - 目标状态
    /// 
    /// # 返回
    /// 如果转换合法返回true，否则返回false
    fn is_valid_transition(&self, from: &AgentState, to: &AgentState) -> bool {
        use AgentState::*;
        
        match (from, to) {
            // 从任何状态都可以转换到停止状态
            (_, Stopped) => true,
            
            // 从任何状态都可以转换到错误状态
            (_, Error) => true,
            
            // 空闲状态的转换
            (Idle, Thinking | Paused | WaitingForInput) => true,
            
            // 思考状态的转换
            (Thinking, Idle | ExecutingTool | ExecutingWorkflow | WaitingForInput | Paused) => true,
            
            // 执行工具状态的转换
            (ExecutingTool, Idle | Thinking | WaitingForInput) => true,
            
            // 执行工作流状态的转换
            (ExecutingWorkflow, Idle | Thinking | WaitingForInput) => true,
            
            // 等待用户输入状态的转换
            (WaitingForInput, Idle | Thinking | Paused) => true,
            
            // 暂停状态的转换
            (Paused, Idle) => true,
            
            // 错误状态的转换
            (Error, Idle) => true,
            
            // 停止状态不能转换到其他状态
            (Stopped, _) => false,
            
            // 其他转换都是无效的
            _ => false,
        }
    }

    /// 获取状态描述
    /// 
    /// # 返回
    /// 返回当前状态的中文描述
    pub async fn get_state_description(&self) -> String {
        let state = self.get_state().await;
        match state {
            AgentState::Idle => "空闲 - 等待新任务".to_string(),
            AgentState::Thinking => "思考中 - 正在分析任务".to_string(),
            AgentState::ExecutingTool => "执行工具 - 正在调用外部工具".to_string(),
            AgentState::WaitingForInput => "等待输入 - 需要用户提供更多信息".to_string(),
            AgentState::ExecutingWorkflow => "执行工作流 - 正在执行复杂流程".to_string(),
            AgentState::Error => "错误状态 - 需要处理错误".to_string(),
            AgentState::Paused => "已暂停 - 用户暂停了Agent".to_string(),
            AgentState::Stopped => "已停止 - Agent已停止运行".to_string(),
        }
    }
}

impl Clone for StateManager {
    /// 克隆状态管理器
    /// 
    /// 注意：克隆的实例共享相同的状态
    fn clone(&self) -> Self {
        Self {
            current_state: self.current_state.clone(),
            agent_name: self.agent_name.clone(),
        }
    }
}
