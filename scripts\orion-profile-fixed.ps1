# Orion PowerShell Profile Configuration
# Add this to your PowerShell Profile

$ORION_ROOT = "E:\Orion"

# Orion development command
function orion {
    param(
        [Parameter(ValueFromRemainingArguments = $true)]
        [string[]]$Arguments
    )
    
    $scriptPath = Join-Path $ORION_ROOT "scripts\orion-dev.bat"
    & $scriptPath @Arguments
}

# Quick navigation to Orion project
function cdo {
    Set-Location $ORION_ROOT
}

# Clean cache function
function orion-clean {
    $cacheDir = Join-Path $ORION_ROOT ".dev-cache"
    $targetDir = Join-Path $ORION_ROOT "target"
    
    if (Test-Path $cacheDir) { Remove-Item -Recurse -Force $cacheDir }
    if (Test-Path $targetDir) { Remove-Item -Recurse -Force $targetDir }
    
    Write-Host "Cache cleaned successfully" -ForegroundColor Green
}

Write-Host "Orion development environment loaded" -ForegroundColor Green
Write-Host "Use 'orion' command to run (auto-compile)" -ForegroundColor Cyan
Write-Host "Use 'cdo' to navigate to project directory" -ForegroundColor Cyan