#!/bin/bash

# Orion 开发环境包装器
# 自动检测代码变更并重新编译

ORION_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CACHE_DIR="$ORION_ROOT/.dev-cache"
BINARY_PATH="$ORION_ROOT/target/debug/orion"
LAST_BUILD_FILE="$CACHE_DIR/last_build_hash"

# 创建缓存目录
mkdir -p "$CACHE_DIR"

# 计算源码文件的哈希值
calculate_source_hash() {
    find "$ORION_ROOT/orion-cli/src" "$ORION_ROOT/orion-core/src" \
         "$ORION_ROOT/Cargo.toml" "$ORION_ROOT/orion-cli/Cargo.toml" "$ORION_ROOT/orion-core/Cargo.toml" \
         -type f -name "*.rs" -o -name "*.toml" | \
    xargs cat | sha256sum | cut -d' ' -f1
}

# 检查是否需要重新编译
need_rebuild() {
    if [ ! -f "$BINARY_PATH" ]; then
        return 0  # 二进制文件不存在，需要编译
    fi
    
    if [ ! -f "$LAST_BUILD_FILE" ]; then
        return 0  # 没有缓存记录，需要编译
    fi
    
    current_hash=$(calculate_source_hash)
    last_hash=$(cat "$LAST_BUILD_FILE" 2>/dev/null || echo "")
    
    if [ "$current_hash" != "$last_hash" ]; then
        return 0  # 源码有变更，需要编译
    fi
    
    return 1  # 不需要编译
}

# 编译项目
build_project() {
    echo "🔨 检测到代码变更，正在重新编译..."
    cd "$ORION_ROOT"
    
    if cargo build --bin orion; then
        current_hash=$(calculate_source_hash)
        echo "$current_hash" > "$LAST_BUILD_FILE"
        echo "✅ 编译完成"
        return 0
    else
        echo "❌ 编译失败"
        return 1
    fi
}

# 主逻辑
main() {
    if need_rebuild; then
        if ! build_project; then
            exit 1
        fi
    fi
    
    # 执行 orion 命令
    exec "$BINARY_PATH" "$@"
}

main "$@"