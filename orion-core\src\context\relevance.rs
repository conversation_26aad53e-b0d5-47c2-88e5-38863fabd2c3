//! # 相关性计算模块
//!
//! 提供智能的相关性评分计算功能，支持多种评分算法和动态调整机制。
//! 包括基于内容、类型、重要性、访问模式等多维度的相关性分析。

use crate::context::types::{ContextEntry, ContextEntryType, ImportanceLevel};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};

/// 相关性计算器
/// 
/// 负责计算和更新上下文条目的相关性评分。
/// 支持多种算法和配置选项。
pub struct RelevanceCalculator {
    /// 计算配置
    config: RelevanceConfig,
    /// 计算统计信息
    stats: RelevanceStats,
}

impl RelevanceCalculator {
    /// 创建新的相关性计算器
    /// 
    /// # 参数
    /// 
    /// * `config` - 相关性计算配置
    /// 
    /// # 返回
    /// 
    /// 返回新的相关性计算器实例
    pub fn new(config: RelevanceConfig) -> Self {
        Self {
            config,
            stats: RelevanceStats::new(),
        }
    }

    /// 使用默认配置创建相关性计算器
    /// 
    /// # 返回
    /// 
    /// 返回使用默认配置的相关性计算器实例
    pub fn with_default_config() -> Self {
        Self::new(RelevanceConfig::default())
    }

    /// 计算智能相关性评分
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// * `context_entries` - 其他上下文条目（用于上下文分析）
    /// 
    /// # 返回
    /// 
    /// 返回计算的相关性评分（0.0-1.0）
    pub fn calculate_smart_relevance(
        &mut self,
        entry: &ContextEntry,
        context_entries: Option<&HashMap<uuid::Uuid, ContextEntry>>,
    ) -> f64 {
        let start_time = SystemTime::now();
        let mut score = self.config.default_relevance_score;

        // 基于重要性级别调整
        score += self.calculate_importance_factor(entry);

        // 基于条目类型调整
        score += self.calculate_type_factor(entry);

        // 基于内容长度调整
        score += self.calculate_content_factor(entry);

        // 基于访问模式调整
        score += self.calculate_access_factor(entry);

        // 基于时间衰减调整
        score += self.calculate_time_factor(entry);

        // 如果提供了上下文，进行上下文相关性分析
        if let Some(context) = context_entries {
            score += self.calculate_context_factor(entry, context);
        }

        let final_score = score.clamp(0.0, 1.0);

        // 更新统计信息
        let calculation_time = SystemTime::now().duration_since(start_time).unwrap_or_default();
        self.stats.record_calculation(final_score, calculation_time);

        tracing::debug!(
            "计算条目 {} 的相关性评分: {:.3} (耗时: {:?})",
            entry.id,
            final_score,
            calculation_time
        );

        final_score
    }

    /// 批量更新相关性评分
    /// 
    /// # 参数
    /// 
    /// * `entries` - 条目映射
    /// 
    /// # 返回
    /// 
    /// 返回更新的评分映射
    pub fn batch_calculate_relevance(
        &mut self,
        entries: &HashMap<uuid::Uuid, ContextEntry>,
    ) -> HashMap<uuid::Uuid, f64> {
        let mut results = HashMap::new();

        for (id, entry) in entries.iter() {
            let score = self.calculate_smart_relevance(entry, Some(entries));
            results.insert(*id, score);
        }

        tracing::info!("批量计算了 {} 个条目的相关性评分", results.len());
        results
    }

    /// 计算重要性因子
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回重要性调整因子
    fn calculate_importance_factor(&self, entry: &ContextEntry) -> f64 {
        match entry.importance {
            ImportanceLevel::Critical => self.config.importance_weights.critical,
            ImportanceLevel::High => self.config.importance_weights.high,
            ImportanceLevel::Normal => self.config.importance_weights.normal,
            ImportanceLevel::Low => self.config.importance_weights.low,
        }
    }

    /// 计算类型因子
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回类型调整因子
    fn calculate_type_factor(&self, entry: &ContextEntry) -> f64 {
        match entry.entry_type {
            ContextEntryType::UserInput => self.config.type_weights.user_input,
            ContextEntryType::AgentResponse => self.config.type_weights.agent_response,
            ContextEntryType::Error => self.config.type_weights.error,
            ContextEntryType::ToolCall => self.config.type_weights.tool_call,
            ContextEntryType::ToolResult => self.config.type_weights.tool_result,
            ContextEntryType::SystemMessage => self.config.type_weights.system_message,
            ContextEntryType::StateUpdate => self.config.type_weights.state_update,
            ContextEntryType::DocumentFragment => self.config.type_weights.document_fragment,
            ContextEntryType::CodeFragment => self.config.type_weights.code_fragment,
            ContextEntryType::Configuration => self.config.type_weights.configuration,
            ContextEntryType::Custom(_) => self.config.type_weights.custom,
        }
    }

    /// 计算内容因子
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回内容长度调整因子
    fn calculate_content_factor(&self, entry: &ContextEntry) -> f64 {
        let length_factor = (entry.content.len() as f64 / self.config.content_length_baseline).min(1.0);
        length_factor * self.config.content_length_weight
    }

    /// 计算访问因子
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回访问模式调整因子
    fn calculate_access_factor(&self, entry: &ContextEntry) -> f64 {
        // 访问频率因子
        let frequency_factor = (entry.access_count as f64).ln().max(0.0) / 10.0;
        
        // 最近访问因子
        let recency_factor = if let Ok(duration) = SystemTime::now().duration_since(entry.last_accessed) {
            let hours = duration.as_secs() as f64 / 3600.0;
            (-hours / 24.0).exp() // 24小时衰减
        } else {
            0.0
        };

        (frequency_factor + recency_factor) * self.config.access_weight
    }

    /// 计算时间因子
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回时间衰减调整因子
    fn calculate_time_factor(&self, entry: &ContextEntry) -> f64 {
        if let Ok(age) = SystemTime::now().duration_since(entry.created_at) {
            let days = age.as_secs() as f64 / 86400.0;
            let decay_factor = self.config.time_decay_factor.powf(days);
            (decay_factor - 1.0) * self.config.time_weight
        } else {
            0.0
        }
    }

    /// 计算上下文因子
    /// 
    /// # 参数
    /// 
    /// * `entry` - 当前条目
    /// * `context_entries` - 其他上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回上下文相关性调整因子
    fn calculate_context_factor(
        &self,
        entry: &ContextEntry,
        context_entries: &HashMap<uuid::Uuid, ContextEntry>,
    ) -> f64 {
        let mut context_score = 0.0;

        // 会话内相关性
        let session_entries: Vec<_> = context_entries
            .values()
            .filter(|e| e.session_id == entry.session_id && e.id != entry.id)
            .collect();

        if !session_entries.is_empty() {
            let avg_session_relevance: f64 = session_entries
                .iter()
                .map(|e| e.relevance_score)
                .sum::<f64>() / session_entries.len() as f64;
            context_score += avg_session_relevance * self.config.session_context_weight;
        }

        // 标签相关性
        if !entry.tags.is_empty() {
            let related_entries: Vec<_> = context_entries
                .values()
                .filter(|e| {
                    e.id != entry.id && e.tags.iter().any(|tag| entry.tags.contains(tag))
                })
                .collect();

            if !related_entries.is_empty() {
                let avg_tag_relevance: f64 = related_entries
                    .iter()
                    .map(|e| e.relevance_score)
                    .sum::<f64>() / related_entries.len() as f64;
                context_score += avg_tag_relevance * self.config.tag_context_weight;
            }
        }

        context_score
    }

    /// 应用访问权重更新
    /// 
    /// # 参数
    /// 
    /// * `current_score` - 当前相关性评分
    /// 
    /// # 返回
    /// 
    /// 返回更新后的相关性评分
    pub fn apply_access_boost(&self, current_score: f64) -> f64 {
        (current_score + self.config.access_boost).min(1.0)
    }

    /// 应用时间衰减
    /// 
    /// # 参数
    /// 
    /// * `current_score` - 当前相关性评分
    /// * `age` - 条目年龄
    /// 
    /// # 返回
    /// 
    /// 返回衰减后的相关性评分
    pub fn apply_time_decay(&self, current_score: f64, age: Duration) -> f64 {
        let days = age.as_secs() as f64 / 86400.0;
        let decay_factor = self.config.time_decay_factor.powf(days);
        current_score * decay_factor
    }

    /// 获取计算统计信息
    /// 
    /// # 返回
    /// 
    /// 返回相关性计算统计信息
    pub fn get_stats(&self) -> &RelevanceStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = RelevanceStats::new();
    }

    /// 获取配置
    /// 
    /// # 返回
    /// 
    /// 返回当前配置
    pub fn get_config(&self) -> &RelevanceConfig {
        &self.config
    }

    /// 更新配置
    /// 
    /// # 参数
    /// 
    /// * `config` - 新的配置
    pub fn update_config(&mut self, config: RelevanceConfig) {
        self.config = config;
        tracing::info!("相关性计算器配置已更新");
    }
}

impl Default for RelevanceCalculator {
    fn default() -> Self {
        Self::with_default_config()
    }
}

/// 相关性计算配置
/// 
/// 定义相关性计算的各种参数和权重
#[derive(Debug, Clone)]
pub struct RelevanceConfig {
    /// 默认相关性评分
    pub default_relevance_score: f64,
    /// 重要性权重配置
    pub importance_weights: ImportanceWeights,
    /// 类型权重配置
    pub type_weights: TypeWeights,
    /// 内容长度权重
    pub content_length_weight: f64,
    /// 内容长度基准（用于归一化）
    pub content_length_baseline: f64,
    /// 访问权重
    pub access_weight: f64,
    /// 访问提升值
    pub access_boost: f64,
    /// 时间权重
    pub time_weight: f64,
    /// 时间衰减因子
    pub time_decay_factor: f64,
    /// 会话上下文权重
    pub session_context_weight: f64,
    /// 标签上下文权重
    pub tag_context_weight: f64,
}

impl Default for RelevanceConfig {
    fn default() -> Self {
        Self {
            default_relevance_score: 0.5,
            importance_weights: ImportanceWeights::default(),
            type_weights: TypeWeights::default(),
            content_length_weight: 0.1,
            content_length_baseline: 1000.0,
            access_weight: 0.1,
            access_boost: 0.1,
            time_weight: 0.05,
            time_decay_factor: 0.95,
            session_context_weight: 0.1,
            tag_context_weight: 0.05,
        }
    }
}

/// 重要性权重配置
#[derive(Debug, Clone)]
pub struct ImportanceWeights {
    pub critical: f64,
    pub high: f64,
    pub normal: f64,
    pub low: f64,
}

impl Default for ImportanceWeights {
    fn default() -> Self {
        Self {
            critical: 0.3,
            high: 0.2,
            normal: 0.0,
            low: -0.1,
        }
    }
}

/// 类型权重配置
#[derive(Debug, Clone)]
pub struct TypeWeights {
    pub user_input: f64,
    pub agent_response: f64,
    pub error: f64,
    pub tool_call: f64,
    pub tool_result: f64,
    pub system_message: f64,
    pub state_update: f64,
    pub document_fragment: f64,
    pub code_fragment: f64,
    pub configuration: f64,
    pub custom: f64,
}

impl Default for TypeWeights {
    fn default() -> Self {
        Self {
            user_input: 0.1,
            agent_response: 0.1,
            error: 0.2,
            tool_call: 0.05,
            tool_result: 0.05,
            system_message: 0.0,
            state_update: 0.0,
            document_fragment: 0.05,
            code_fragment: 0.08,
            configuration: -0.05,
            custom: 0.0,
        }
    }
}

/// 相关性计算统计信息
#[derive(Debug, Clone)]
pub struct RelevanceStats {
    /// 总计算次数
    pub total_calculations: usize,
    /// 平均评分
    pub average_score: f64,
    /// 最高评分
    pub max_score: f64,
    /// 最低评分
    pub min_score: f64,
    /// 总计算时间
    pub total_calculation_time: Duration,
    /// 平均计算时间
    pub average_calculation_time: Duration,
}

impl RelevanceStats {
    /// 创建新的统计信息
    pub fn new() -> Self {
        Self {
            total_calculations: 0,
            average_score: 0.0,
            max_score: 0.0,
            min_score: 1.0,
            total_calculation_time: Duration::from_secs(0),
            average_calculation_time: Duration::from_secs(0),
        }
    }

    /// 记录计算结果
    /// 
    /// # 参数
    /// 
    /// * `score` - 计算的评分
    /// * `calculation_time` - 计算耗时
    pub fn record_calculation(&mut self, score: f64, calculation_time: Duration) {
        self.total_calculations += 1;
        self.max_score = self.max_score.max(score);
        self.min_score = self.min_score.min(score);
        self.total_calculation_time += calculation_time;

        // 更新平均值
        self.average_score = (self.average_score * (self.total_calculations - 1) as f64 + score)
            / self.total_calculations as f64;

        if self.total_calculations > 0 {
            self.average_calculation_time = self.total_calculation_time / self.total_calculations as u32;
        }
    }

    /// 生成统计报告
    /// 
    /// # 返回
    /// 
    /// 返回格式化的统计报告字符串
    pub fn generate_report(&self) -> String {
        format!(
            "=== 相关性计算统计报告 ===\n\
             总计算次数: {}\n\
             平均评分: {:.3}\n\
             最高评分: {:.3}\n\
             最低评分: {:.3}\n\
             总计算时间: {:?}\n\
             平均计算时间: {:?}",
            self.total_calculations,
            self.average_score,
            self.max_score,
            self.min_score,
            self.total_calculation_time,
            self.average_calculation_time
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use uuid::Uuid;

    fn create_test_entry(
        relevance: f64,
        importance: ImportanceLevel,
        entry_type: ContextEntryType,
        content: &str,
    ) -> ContextEntry {
        ContextEntry {
            id: Uuid::new_v4(),
            session_id: "test_session".to_string(),
            entry_type,
            content: content.to_string(),
            metadata: HashMap::new(),
            relevance_score: relevance,
            importance,
            created_at: SystemTime::now(),
            last_accessed: SystemTime::now(),
            access_count: 0,
            tags: Vec::new(),
            parent_id: None,
            children_ids: Vec::new(),
        }
    }

    #[test]
    fn test_relevance_calculation() {
        let mut calculator = RelevanceCalculator::with_default_config();

        let entry = create_test_entry(
            0.5,
            ImportanceLevel::High,
            ContextEntryType::UserInput,
            "这是一个测试内容",
        );

        let score = calculator.calculate_smart_relevance(&entry, None);
        assert!(score >= 0.0 && score <= 1.0);
        assert!(score > 0.5); // 高重要性和用户输入应该提高评分
    }

    #[test]
    fn test_batch_calculation() {
        let mut calculator = RelevanceCalculator::with_default_config();
        let mut entries = HashMap::new();

        for i in 0..5 {
            let entry = create_test_entry(
                0.5,
                ImportanceLevel::Normal,
                ContextEntryType::UserInput,
                &format!("测试内容 {}", i),
            );
            entries.insert(entry.id, entry);
        }

        let results = calculator.batch_calculate_relevance(&entries);
        assert_eq!(results.len(), 5);

        for score in results.values() {
            assert!(*score >= 0.0 && *score <= 1.0);
        }
    }

    #[test]
    fn test_access_boost() {
        let calculator = RelevanceCalculator::with_default_config();
        let original_score = 0.5;
        let boosted_score = calculator.apply_access_boost(original_score);
        
        assert!(boosted_score > original_score);
        assert!(boosted_score <= 1.0);
    }

    #[test]
    fn test_time_decay() {
        let calculator = RelevanceCalculator::with_default_config();
        let original_score = 0.8;
        let age = Duration::from_secs(86400 * 7); // 7天
        let decayed_score = calculator.apply_time_decay(original_score, age);
        
        assert!(decayed_score < original_score);
        assert!(decayed_score >= 0.0);
    }
}
