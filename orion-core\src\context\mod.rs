//! # 智能上下文管理模块
//!
//! 提供智能上下文管理功能，包括会话状态跟踪、上下文压缩、相关性评分等。
//! 支持多种上下文存储策略和智能检索机制。
//!
//! ## 模块结构
//!
//! - `types` - 核心类型定义，包括上下文条目、条目类型、重要性级别等
//! - `config` - 配置管理，包括管理器配置和压缩策略
//! - `query` - 查询相关类型，包括查询条件和时间范围
//! - `stats` - 统计信息管理，收集和分析上下文使用数据
//! - `index` - 索引管理，提供高效的条目检索功能
//! - `compression` - 压缩策略，实现智能的上下文压缩算法
//! - `relevance` - 相关性计算，提供智能的相关性评分机制
//! - `manager` - 核心管理器，整合所有子模块提供统一接口

// 子模块声明
pub mod types;
pub mod config;
pub mod query;
pub mod stats;
pub mod index;
pub mod compression;
pub mod relevance;
pub mod manager;

// 重新导出核心类型和结构体，保持向后兼容性
pub use self::types::{
    ContextEntry, ContextEntryType, ImportanceLevel,
};
pub use self::config::{
    ContextManagerConfig, CompressionStrategy,
};
pub use self::query::{
    ContextQuery, TimeRange,
};
pub use self::stats::ContextStats;
pub use self::manager::ContextManager;

// 为了保持与原始代码的兼容性，重新导出所有公共类型
pub use self::index::IndexManager;
pub use self::compression::CompressionManager;
pub use self::relevance::RelevanceCalculator;

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use std::time::SystemTime;
    use uuid::Uuid;

    /// 测试模块化后的基本功能
    #[tokio::test]
    async fn test_modular_context_manager() {
        let manager = ContextManager::with_default_config();
        
        // 创建测试条目
        let entry = ContextEntry {
            id: Uuid::new_v4(),
            session_id: "test_session".to_string(),
            entry_type: ContextEntryType::UserInput,
            content: "测试内容".to_string(),
            metadata: HashMap::new(),
            relevance_score: 0.8,
            importance: ImportanceLevel::High,
            created_at: SystemTime::now(),
            last_accessed: SystemTime::now(),
            access_count: 0,
            tags: vec!["test".to_string()],
            parent_id: None,
            children_ids: Vec::new(),
        };
        
        // 测试添加条目
        let entry_id = manager.add_entry(entry.clone()).await.unwrap();
        assert_eq!(entry_id, entry.id);
        
        // 测试获取条目
        let retrieved = manager.get_entry(entry_id).await.unwrap().unwrap();
        assert_eq!(retrieved.content, "测试内容");
        assert_eq!(retrieved.session_id, "test_session");
    }

    /// 测试类型兼容性
    #[test]
    fn test_type_compatibility() {
        let user_input = ContextEntryType::UserInput;
        let agent_response = ContextEntryType::AgentResponse;
        let custom = ContextEntryType::Custom("custom_type".to_string());
        
        assert_eq!(user_input, ContextEntryType::UserInput);
        assert_eq!(agent_response, ContextEntryType::AgentResponse);
        assert_eq!(custom, ContextEntryType::Custom("custom_type".to_string()));
    }

    /// 测试重要性级别
    #[test]
    fn test_importance_levels() {
        assert!(ImportanceLevel::Critical > ImportanceLevel::High);
        assert!(ImportanceLevel::High > ImportanceLevel::Normal);
        assert!(ImportanceLevel::Normal > ImportanceLevel::Low);
    }
}
