//! # 消息系统核心类型定义
//!
//! 本模块包含消息系统的所有核心类型定义，包括消息结构体、载荷类型、优先级等。
//! 这些类型是整个消息系统的基础，被其他模块广泛使用。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use uuid::Uuid;

/// Agent ID 类型别名
/// 
/// 用于标识系统中的各个 Agent，采用字符串类型便于扩展和调试
pub type AgentId = String;

/// 消息优先级，支持实时 Steering 机制
/// 
/// 优先级系统确保重要消息（如用户干预命令）能够优先处理，
/// 实现实时的系统控制和响应能力
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum MessagePriority {
    /// 高优先级 - 用户干预命令，实时 Steering
    /// 
    /// 用于紧急控制命令，如暂停、停止等操作
    High = 3,
    /// 普通优先级 - Agent 内部消息
    /// 
    /// 用于常规的 Agent 间通信和任务处理
    Normal = 2,
    /// 低优先级 - 日志和状态更新
    /// 
    /// 用于非关键的状态报告和日志信息
    Low = 1,
}

impl Default for MessagePriority {
    fn default() -> Self {
        MessagePriority::Normal
    }
}

/// 消息载荷类型
/// 
/// 定义了系统支持的各种消息类型，每种类型都有特定的用途和结构。
/// 采用枚举设计确保类型安全和模式匹配的便利性。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessagePayload {
    /// 文本消息
    /// 
    /// 最基础的消息类型，用于简单的文本通信
    Text(String),
    
    /// 任务请求
    /// 
    /// 用于 Agent 间的任务分配和请求处理
    TaskRequest {
        /// 任务唯一标识符
        task_id: String,
        /// 任务描述
        description: String,
        /// 任务参数，使用 JSON 值提供灵活性
        parameters: HashMap<String, serde_json::Value>,
    },
    
    /// 任务响应
    /// 
    /// 任务执行完成后的响应消息
    TaskResponse {
        /// 对应的任务 ID
        task_id: String,
        /// 执行是否成功
        success: bool,
        /// 执行结果（成功时）
        result: Option<serde_json::Value>,
        /// 错误信息（失败时）
        error: Option<String>,
    },
    
    /// 工作流控制
    /// 
    /// 用于工作流的生命周期管理
    WorkflowControl {
        /// 控制动作："start", "pause", "resume", "stop"
        action: String,
        /// 工作流标识符
        workflow_id: String,
        /// 控制参数
        parameters: HashMap<String, serde_json::Value>,
    },
    
    /// 实时 Steering 命令
    /// 
    /// 用于实时控制和干预系统行为，具有最高优先级
    SteeringCommand {
        /// 控制命令
        command: String,
        /// 目标组件或对象
        target: String,
        /// 命令参数
        parameters: HashMap<String, serde_json::Value>,
    },
    
    /// 状态更新
    /// 
    /// 用于组件状态的报告和同步
    StatusUpdate {
        /// 组件名称
        component: String,
        /// 状态信息
        status: String,
        /// 详细信息
        details: HashMap<String, serde_json::Value>,
    },
    
    /// 日志消息
    /// 
    /// 用于系统日志和调试信息的传递
    LogMessage {
        /// 日志级别
        level: String,
        /// 日志内容
        message: String,
        /// 日志元数据
        metadata: HashMap<String, serde_json::Value>,
    },
    
    /// 自定义消息
    /// 
    /// 提供扩展性，支持用户定义的消息类型
    Custom {
        /// 消息类型标识
        message_type: String,
        /// 消息数据
        data: serde_json::Value,
    },
}

/// 消息结构体
/// 
/// 系统中所有消息的统一结构，包含完整的消息元信息和载荷。
/// 设计时考虑了可追踪性、过期处理、会话管理等需求。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    /// 消息唯一标识
    /// 
    /// 使用 UUID 确保全局唯一性，便于消息追踪和去重
    pub id: Uuid,
    
    /// 发送者 Agent ID
    /// 
    /// 标识消息的发送方，用于路由和权限控制
    pub from: AgentId,
    
    /// 接收者 Agent ID
    /// 
    /// 标识消息的接收方，用于消息路由
    pub to: AgentId,
    
    /// 消息载荷
    /// 
    /// 实际的消息内容和数据
    pub payload: MessagePayload,
    
    /// 消息优先级
    /// 
    /// 用于消息队列的优先级调度
    pub priority: MessagePriority,
    
    /// 创建时间戳
    /// 
    /// 记录消息创建时间，用于排序和过期检查
    pub timestamp: SystemTime,
    
    /// 消息元数据
    /// 
    /// 存储额外的键值对信息，提供扩展性
    pub metadata: HashMap<String, String>,
    
    /// 关联的会话 ID
    /// 
    /// 用于将消息关联到特定的会话或上下文
    pub session_id: Option<String>,
    
    /// 消息过期时间
    /// 
    /// 设置消息的生存时间，防止过期消息堆积
    pub expires_at: Option<SystemTime>,
}

impl Message {
    /// 创建新消息
    /// 
    /// 创建一个基础的消息实例，使用默认的优先级和当前时间戳
    /// 
    /// # 参数
    /// 
    /// * `from` - 发送者 Agent ID
    /// * `to` - 接收者 Agent ID  
    /// * `payload` - 消息载荷
    /// 
    /// # 返回值
    /// 
    /// 返回新创建的消息实例
    pub fn new(
        from: AgentId,
        to: AgentId,
        payload: MessagePayload,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            from,
            to,
            payload,
            priority: MessagePriority::default(),
            timestamp: SystemTime::now(),
            metadata: HashMap::new(),
            session_id: None,
            expires_at: None,
        }
    }
    
    /// 设置消息优先级
    /// 
    /// 使用构建器模式设置消息优先级
    /// 
    /// # 参数
    /// 
    /// * `priority` - 消息优先级
    /// 
    /// # 返回值
    /// 
    /// 返回设置了优先级的消息实例
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }
    
    /// 设置会话 ID
    /// 
    /// 将消息关联到特定的会话
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话标识符
    /// 
    /// # 返回值
    /// 
    /// 返回设置了会话 ID 的消息实例
    pub fn with_session_id(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }
    
    /// 设置过期时间
    /// 
    /// 设置消息的生存时间，超过此时间的消息将被丢弃
    /// 
    /// # 参数
    /// 
    /// * `duration` - 从当前时间开始的生存时间
    /// 
    /// # 返回值
    /// 
    /// 返回设置了过期时间的消息实例
    pub fn with_expiry(mut self, duration: Duration) -> Self {
        self.expires_at = Some(SystemTime::now() + duration);
        self
    }
    
    /// 添加元数据
    /// 
    /// 向消息添加键值对元数据
    /// 
    /// # 参数
    /// 
    /// * `key` - 元数据键
    /// * `value` - 元数据值
    /// 
    /// # 返回值
    /// 
    /// 返回添加了元数据的消息实例
    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }
    
    /// 检查消息是否过期
    /// 
    /// 根据设置的过期时间检查消息是否已过期
    /// 
    /// # 返回值
    /// 
    /// 如果消息已过期返回 true，否则返回 false
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            SystemTime::now() > expires_at
        } else {
            false
        }
    }
    
    /// 创建实时 Steering 消息
    /// 
    /// 便捷方法，用于创建高优先级的 Steering 控制消息
    /// 
    /// # 参数
    /// 
    /// * `from` - 发送者 Agent ID
    /// * `to` - 接收者 Agent ID
    /// * `command` - 控制命令
    /// * `target` - 目标组件
    /// * `parameters` - 命令参数
    /// 
    /// # 返回值
    /// 
    /// 返回配置好的 Steering 消息
    pub fn steering_command(
        from: AgentId,
        to: AgentId,
        command: String,
        target: String,
        parameters: HashMap<String, serde_json::Value>,
    ) -> Self {
        Self::new(
            from,
            to,
            MessagePayload::SteeringCommand {
                command,
                target,
                parameters,
            },
        )
        .with_priority(MessagePriority::High)
    }
}
