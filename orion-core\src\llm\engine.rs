//! # LLM 引擎实现
//!
//! 管理多个 LLM 提供商，提供统一的接口来访问不同的 LLM 服务。

use crate::error::{OrionError, Result, LlmError};
use super::provider::LlmProvider;
use super::types::{LlmRequest, LlmResponse, StreamChunk, ModelConfig};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing;

/// LLM 引擎 - 管理多个 LLM 提供商
/// 
/// LLM 引擎是整个 LLM 系统的核心，负责：
/// - 注册和管理多个 LLM 提供商
/// - 路由请求到合适的提供商
/// - 提供统一的 API 接口
/// - 记录请求历史和统计信息
pub struct LlmEngine {
    /// 注册的提供商
    providers: Arc<RwLock<HashMap<String, Box<dyn LlmProvider>>>>,
    /// 默认提供商
    default_provider: Arc<RwLock<Option<String>>>,
    /// 请求历史（用于调试和分析）
    request_history: Arc<RwLock<Vec<(LlmRequest, Result<LlmResponse>)>>>,
}

impl LlmEngine {
    /// 创建新的 LLM 引擎
    /// 
    /// 创建一个空的 LLM 引擎实例，需要手动注册提供商。
    pub fn new() -> Self {
        Self {
            providers: Arc::new(RwLock::new(HashMap::new())),
            default_provider: Arc::new(RwLock::new(None)),
            request_history: Arc::new(RwLock::new(Vec::new())),
        }
    }
    
    /// 注册 LLM 提供商
    /// 
    /// 向引擎注册一个新的 LLM 提供商。如果这是第一个提供商，会自动设为默认提供商。
    /// 
    /// # 参数
    /// 
    /// * `name` - 提供商名称，用作唯一标识符
    /// * `provider` - 提供商实现
    /// 
    /// # 返回
    /// 
    /// 注册成功返回 Ok(())，否则返回错误信息。
    pub async fn register_provider(&self, name: String, provider: Box<dyn LlmProvider>) -> Result<()> {
        let mut providers = self.providers.write().await;
        providers.insert(name.clone(), provider);
        
        // 如果这是第一个提供商，设为默认
        let mut default = self.default_provider.write().await;
        if default.is_none() {
            *default = Some(name.clone());
        }
        
        tracing::info!("LLM 提供商 '{}' 已注册", name);
        Ok(())
    }
    
    /// 设置默认提供商
    /// 
    /// 设置默认使用的 LLM 提供商。当调用 `complete` 或 `stream` 方法时，
    /// 如果没有指定提供商，会使用默认提供商。
    /// 
    /// # 参数
    /// 
    /// * `name` - 提供商名称
    /// 
    /// # 返回
    /// 
    /// 设置成功返回 Ok(())，如果提供商不存在返回错误。
    pub async fn set_default_provider(&self, name: String) -> Result<()> {
        let providers = self.providers.read().await;
        if !providers.contains_key(&name) {
            return Err(OrionError::Llm(LlmError::engine_error(format!("提供商 '{}' 未注册", name))));
        }
        
        let mut default = self.default_provider.write().await;
        *default = Some(name.clone());
        
        tracing::info!("默认 LLM 提供商设置为 '{}'", name);
        Ok(())
    }
    
    /// 发送请求到指定提供商
    /// 
    /// 向指定的 LLM 提供商发送请求并获取响应。
    /// 
    /// # 参数
    /// 
    /// * `provider_name` - 提供商名称
    /// * `request` - LLM 请求对象
    /// 
    /// # 返回
    /// 
    /// 返回 LLM 响应，包含生成的内容和元数据。
    pub async fn complete_with_provider(&self, provider_name: &str, request: LlmRequest) -> Result<LlmResponse> {
        let providers = self.providers.read().await;
        let provider = providers.get(provider_name)
            .ok_or_else(|| OrionError::Llm(LlmError::engine_error(format!("提供商 '{}' 未注册", provider_name))))?;
        
        let result = provider.complete(request.clone()).await;
        
        // 记录请求历史
        let mut history = self.request_history.write().await;
        history.push((request, result.clone()));
        
        // 限制历史记录数量
        if history.len() > 1000 {
            history.drain(0..100);
        }
        
        result
    }
    
    /// 发送请求到默认提供商
    /// 
    /// 向默认的 LLM 提供商发送请求并获取响应。
    /// 
    /// # 参数
    /// 
    /// * `request` - LLM 请求对象
    /// 
    /// # 返回
    /// 
    /// 返回 LLM 响应，包含生成的内容和元数据。
    pub async fn complete(&self, request: LlmRequest) -> Result<LlmResponse> {
        let default = self.default_provider.read().await;
        let provider_name = default.as_ref()
            .ok_or_else(|| OrionError::Llm(LlmError::engine_error("未设置默认提供商")))?;
        
        self.complete_with_provider(provider_name, request).await
    }

    /// 发送流式请求到默认提供商
    /// 
    /// 向默认的 LLM 提供商发送流式请求并返回响应通道。
    /// 
    /// # 参数
    /// 
    /// * `request` - LLM 请求对象
    /// 
    /// # 返回
    /// 
    /// 返回一个接收器，可以逐步接收流式响应块。
    pub async fn stream(&self, request: LlmRequest) -> Result<tokio::sync::mpsc::Receiver<Result<StreamChunk>>> {
        let default = self.default_provider.read().await;
        let provider_name = default.as_ref()
            .ok_or_else(|| OrionError::Llm(LlmError::engine_error("未设置默认提供商")))?;

        self.stream_with_provider(provider_name, request).await
    }

    /// 发送流式请求到指定提供商
    /// 
    /// 向指定的 LLM 提供商发送流式请求并返回响应通道。
    /// 
    /// # 参数
    /// 
    /// * `provider_name` - 提供商名称
    /// * `request` - LLM 请求对象
    /// 
    /// # 返回
    /// 
    /// 返回一个接收器，可以逐步接收流式响应块。
    pub async fn stream_with_provider(&self, provider_name: &str, request: LlmRequest) -> Result<tokio::sync::mpsc::Receiver<Result<StreamChunk>>> {
        let providers = self.providers.read().await;
        let provider = providers.get(provider_name)
            .ok_or_else(|| OrionError::Llm(LlmError::engine_error(format!("提供商 '{}' 未注册", provider_name))))?;

        provider.stream(request).await
    }

    /// 获取所有注册的提供商
    /// 
    /// 返回所有已注册的提供商名称列表。
    pub async fn list_providers(&self) -> Vec<String> {
        let providers = self.providers.read().await;
        providers.keys().cloned().collect()
    }
    
    /// 获取默认提供商
    /// 
    /// 返回当前设置的默认提供商名称。
    pub async fn get_default_provider(&self) -> Option<String> {
        let default = self.default_provider.read().await;
        default.clone()
    }
    
    /// 验证提供商配置
    /// 
    /// 验证指定提供商的配置是否正确。
    /// 
    /// # 参数
    /// 
    /// * `provider_name` - 提供商名称
    /// * `config` - 模型配置
    /// 
    /// # 返回
    /// 
    /// 配置有效返回 Ok(())，否则返回错误信息。
    pub async fn validate_provider(&self, provider_name: &str, config: &ModelConfig) -> Result<()> {
        let providers = self.providers.read().await;
        let provider = providers.get(provider_name)
            .ok_or_else(|| OrionError::Llm(LlmError::engine_error(format!("提供商 '{}' 未注册", provider_name))))?;
        
        provider.validate_config(config).await
    }
    
    /// 获取请求历史统计
    /// 
    /// 返回请求历史的统计信息，包括总请求数和成功请求数。
    /// 
    /// # 返回
    /// 
    /// 返回 (总请求数, 成功请求数) 的元组。
    pub async fn get_request_stats(&self) -> (usize, usize) {
        let history = self.request_history.read().await;
        let total = history.len();
        let successful = history.iter().filter(|(_, result)| result.is_ok()).count();
        (total, successful)
    }
}

impl Default for LlmEngine {
    fn default() -> Self {
        Self::new()
    }
}
