//! # 运行命令工具函数
//!
//! 提供运行模块共用的工具函数和辅助功能。

use crate::error::{CliError, Result};
use orion_core::{
    agent::{TaskRequest, TaskType, TaskPriority},
    config::OrionConfig,
};
use rustyline::{DefaultEditor, Config};
use std::collections::HashMap;
use std::time::SystemTime;
use std::path::PathBuf;
use uuid::Uuid;

/// 加载配置
pub async fn load_config(config_path: Option<&String>) -> Result<OrionConfig> {
    let config = if let Some(config_path) = config_path {
        OrionConfig::from_file(config_path)
            .map_err(|e| CliError::ConfigError {
                error: format!("加载配置文件失败: {}", e),
            })?
    } else {
        OrionConfig::default()
    };
    
    tracing::debug!("配置加载完成: {:?}", config);
    Ok(config)
}

/// 创建任务
pub async fn create_task(content: String) -> TaskRequest {
    TaskRequest {
        id: Uuid::new_v4(),
        content,
        task_type: TaskType::Question, // 默认为问答任务
        session_id: None,
        user_id: None,
        priority: TaskPriority::Normal,
        parameters: HashMap::new(),
        context: HashMap::new(),
        created_at: SystemTime::now(),
        deadline: None,
    }
}

/// 从文件读取命令行
pub async fn read_commands_from_file(file_path: &str) -> Result<Vec<String>> {
    let content = tokio::fs::read_to_string(file_path).await
        .map_err(|e| CliError::IoError {
            error: format!("读取文件失败: {}", e),
        })?;
    
    let lines: Vec<String> = content.lines()
        .filter(|line| !line.trim().is_empty() && !line.trim().starts_with('#'))
        .map(|line| line.to_string())
        .collect();
    
    Ok(lines)
}

/// 创建 readline 编辑器
pub async fn create_readline_editor() -> Result<DefaultEditor> {
    let config = Config::builder()
        .history_ignore_space(true)
        .history_ignore_dups(true)?
        .build();
        
    let editor = DefaultEditor::with_config(config)
        .map_err(|e| CliError::IoError {
            error: format!("创建 readline 编辑器失败: {}", e),
        })?;
    
    Ok(editor)
}

/// 获取历史文件路径
pub fn get_history_file_path() -> Option<PathBuf> {
    dirs::config_dir().map(|dir| dir.join("orion").join("history.txt"))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_create_task() {
        let task = create_task("test content".to_string()).await;
        
        assert_eq!(task.content, "test content");
        assert_eq!(task.task_type, TaskType::Question);
        assert_eq!(task.priority, TaskPriority::Normal);
    }

    #[tokio::test]
    async fn test_load_default_config() {
        let config = load_config(None).await.unwrap();
        
        // 验证默认配置加载成功
        assert!(!config.agents.default_model.is_empty());
    }
}