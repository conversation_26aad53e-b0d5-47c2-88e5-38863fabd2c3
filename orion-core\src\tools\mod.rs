//! # 基础工具集模块
//!
//! 提供可扩展的工具系统，包括文件操作、网络请求、数据处理等基础工具。
//! 支持工具注册、参数验证和结果处理。
//!
//! ## 模块结构
//!
//! - [`types`] - 核心类型定义
//! - [`traits`] - 工具接口定义
//! - [`validation`] - 参数验证功能
//! - [`registry`] - 工具注册表
//! - [`implementations`] - 具体工具实现
//!
//! ## 使用示例
//!
//! ```rust
//! use orion_core::tools::{ToolRegistry, ToolRequest, FileReadTool};
//! use std::collections::HashMap;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // 创建工具注册表
//!     let registry = ToolRegistry::new();
//!     
//!     // 注册默认工具
//!     registry.register_default_tools().await?;
//!     
//!     // 创建工具请求
//!     let mut params = HashMap::new();
//!     params.insert("path".to_string(), serde_json::Value::String("test.txt".to_string()));
//!     
//!     let request = ToolRequest::new("file_read".to_string(), params);
//!     
//!     // 执行工具
//!     let result = registry.execute_tool(request).await?;
//!     
//!     if result.success {
//!         println!("文件内容: {}", result.data);
//!     } else {
//!         println!("执行失败: {:?}", result.error);
//!     }
//!     
//!     Ok(())
//! }
//! ```

// 子模块声明
pub mod types;
pub mod traits;
pub mod validation;
pub mod registry;
pub mod implementations;

// 重新导出核心类型
pub use types::{
    ToolParameter, ParameterType, ParameterConstraints, ToolDefinition, ToolExample,
    ToolRequest, ToolContext, ToolResult, ResourceUsage
};

// 重新导出核心接口
pub use traits::{Tool, ToolBuilder, ToolFactory};

// 重新导出验证功能
pub use validation::{ParameterValidator, validate_tool_parameters, validate_parameter};

// 重新导出注册表
pub use registry::{ToolRegistry, ToolRegistryStats};

// 重新导出工具实现
pub use implementations::{FileReadTool, FileWriteTool, HttpRequestTool};

use crate::error::Result;
use crate::sandbox::Sandbox;
use std::sync::Arc;

/// 工具系统构建器
/// 
/// 提供便捷的方式来构建和配置工具系统。
pub struct ToolSystemBuilder {
    /// 是否注册默认工具
    register_defaults: bool,
    /// 沙箱实例
    sandbox: Option<Arc<Sandbox>>,
    /// 自定义工具列表
    custom_tools: Vec<Box<dyn Tool>>,
}

impl ToolSystemBuilder {
    /// 创建新的工具系统构建器
    pub fn new() -> Self {
        Self {
            register_defaults: true,
            sandbox: None,
            custom_tools: Vec::new(),
        }
    }
    
    /// 设置是否注册默认工具
    pub fn with_defaults(mut self, register_defaults: bool) -> Self {
        self.register_defaults = register_defaults;
        self
    }
    
    /// 设置沙箱实例
    pub fn with_sandbox(mut self, sandbox: Arc<Sandbox>) -> Self {
        self.sandbox = Some(sandbox);
        self
    }
    
    /// 添加自定义工具
    pub fn add_tool(mut self, tool: Box<dyn Tool>) -> Self {
        self.custom_tools.push(tool);
        self
    }
    
    /// 构建工具注册表
    pub async fn build(self) -> Result<ToolRegistry> {
        let mut registry = ToolRegistry::new();
        
        // 设置沙箱
        if let Some(sandbox) = self.sandbox {
            registry = registry.with_sandbox(sandbox);
        }
        
        // 注册默认工具
        if self.register_defaults {
            registry.register_default_tools().await?;
        }
        
        // 注册自定义工具
        for tool in self.custom_tools {
            registry.register_tool(tool).await?;
        }
        
        Ok(registry)
    }
}

impl Default for ToolSystemBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 创建默认的工具注册表
/// 
/// 创建一个包含所有默认工具的注册表。
pub async fn create_default_registry() -> Result<ToolRegistry> {
    ToolSystemBuilder::new().build().await
}

/// 创建带沙箱的工具注册表
/// 
/// 创建一个包含沙箱支持的工具注册表。
/// 
/// # 参数
/// 
/// * `sandbox` - 沙箱实例
pub async fn create_registry_with_sandbox(sandbox: Arc<Sandbox>) -> Result<ToolRegistry> {
    ToolSystemBuilder::new()
        .with_sandbox(sandbox)
        .build()
        .await
}

/// 创建最小化的工具注册表
/// 
/// 创建一个不包含默认工具的空注册表。
pub async fn create_minimal_registry() -> Result<ToolRegistry> {
    ToolSystemBuilder::new()
        .with_defaults(false)
        .build()
        .await
}

/// 工具系统信息
#[derive(Debug, Clone)]
pub struct ToolSystemInfo {
    /// 版本信息
    pub version: String,
    /// 支持的工具类型
    pub supported_types: Vec<String>,
    /// 支持的分类
    pub supported_categories: Vec<String>,
}

impl ToolSystemInfo {
    /// 获取工具系统信息
    pub fn get() -> Self {
        Self {
            version: env!("CARGO_PKG_VERSION").to_string(),
            supported_types: vec![
                "filesystem".to_string(),
                "network".to_string(),
                "data".to_string(),
                "system".to_string(),
            ],
            supported_categories: vec![
                "filesystem".to_string(),
                "network".to_string(),
                "data_processing".to_string(),
                "system_info".to_string(),
                "security".to_string(),
            ],
        }
    }
}

/// 工具系统常量
pub mod constants {
    /// 默认超时时间（秒）
    pub const DEFAULT_TIMEOUT_SECONDS: u64 = 30;
    
    /// 最大超时时间（秒）
    pub const MAX_TIMEOUT_SECONDS: u64 = 300;
    
    /// 默认工具版本
    pub const DEFAULT_TOOL_VERSION: &str = "1.0.0";
    
    /// 支持的编码格式
    pub const SUPPORTED_ENCODINGS: &[&str] = &["utf-8", "gbk", "ascii"];
    
    /// 支持的 HTTP 方法
    pub const SUPPORTED_HTTP_METHODS: &[&str] = &[
        "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"
    ];
}

/// 便捷函数：创建文件读取请求
/// 
/// # 参数
/// 
/// * `path` - 文件路径
/// * `encoding` - 编码格式（可选）
pub fn create_file_read_request(path: &str, encoding: Option<&str>) -> ToolRequest {
    let mut params = std::collections::HashMap::new();
    params.insert("path".to_string(), serde_json::Value::String(path.to_string()));
    
    if let Some(enc) = encoding {
        params.insert("encoding".to_string(), serde_json::Value::String(enc.to_string()));
    }
    
    ToolRequest::new("file_read".to_string(), params)
}

/// 便捷函数：创建文件写入请求
/// 
/// # 参数
/// 
/// * `path` - 文件路径
/// * `content` - 文件内容
/// * `append` - 是否追加模式
pub fn create_file_write_request(path: &str, content: &str, append: bool) -> ToolRequest {
    let mut params = std::collections::HashMap::new();
    params.insert("path".to_string(), serde_json::Value::String(path.to_string()));
    params.insert("content".to_string(), serde_json::Value::String(content.to_string()));
    params.insert("append".to_string(), serde_json::Value::Bool(append));
    
    ToolRequest::new("file_write".to_string(), params)
}

/// 便捷函数：创建 HTTP 请求
/// 
/// # 参数
/// 
/// * `url` - 请求 URL
/// * `method` - HTTP 方法（可选，默认 GET）
/// * `body` - 请求体（可选）
pub fn create_http_request(
    url: &str, 
    method: Option<&str>, 
    body: Option<&str>
) -> ToolRequest {
    let mut params = std::collections::HashMap::new();
    params.insert("url".to_string(), serde_json::Value::String(url.to_string()));
    
    if let Some(m) = method {
        params.insert("method".to_string(), serde_json::Value::String(m.to_string()));
    }
    
    if let Some(b) = body {
        params.insert("body".to_string(), serde_json::Value::String(b.to_string()));
    }
    
    ToolRequest::new("http_request".to_string(), params)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_tool_system_builder() {
        let registry = ToolSystemBuilder::new()
            .with_defaults(true)
            .build()
            .await
            .unwrap();
        
        let tools = registry.list_tools().await;
        assert!(!tools.is_empty());
        
        // 验证默认工具是否注册
        assert!(registry.has_tool("file_read").await);
        assert!(registry.has_tool("file_write").await);
        assert!(registry.has_tool("http_request").await);
    }
    
    #[tokio::test]
    async fn test_minimal_registry() {
        let registry = create_minimal_registry().await.unwrap();
        let tools = registry.list_tools().await;
        assert!(tools.is_empty());
    }
    
    #[test]
    fn test_convenience_functions() {
        let file_req = create_file_read_request("test.txt", Some("utf-8"));
        assert_eq!(file_req.tool_name, "file_read");
        assert!(file_req.parameters.contains_key("path"));
        assert!(file_req.parameters.contains_key("encoding"));
        
        let write_req = create_file_write_request("test.txt", "content", false);
        assert_eq!(write_req.tool_name, "file_write");
        assert!(write_req.parameters.contains_key("path"));
        assert!(write_req.parameters.contains_key("content"));
        assert!(write_req.parameters.contains_key("append"));
        
        let http_req = create_http_request("https://example.com", Some("POST"), Some("data"));
        assert_eq!(http_req.tool_name, "http_request");
        assert!(http_req.parameters.contains_key("url"));
        assert!(http_req.parameters.contains_key("method"));
        assert!(http_req.parameters.contains_key("body"));
    }
}
