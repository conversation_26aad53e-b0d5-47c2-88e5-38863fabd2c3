//! # 事件循环模块
//!
//! 本模块实现了消息系统的事件循环，负责从消息总线接收消息并分发给注册的处理器。
//! 事件循环是消息处理的核心组件，支持多处理器并发处理、错误恢复、性能监控等功能。

use crate::error::{OrionError, Result};
use super::types::Message;
use super::bus::MessageBus;
use super::handler::MessageHandler;
use super::stats::{StatsManager, PerformanceMeasurement};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;

/// 事件循环 - 处理消息队列的事件分发
/// 
/// 事件循环是消息系统的处理引擎，负责：
/// - 从消息总线接收消息
/// - 将消息分发给注册的处理器
/// - 处理器执行和错误恢复
/// - 性能监控和统计收集
/// - 生命周期管理
pub struct EventLoop {
    /// 消息总线引用
    /// 
    /// 用于接收消息和发送响应
    message_bus: Arc<MessageBus>,
    
    /// 注册的消息处理器
    /// 
    /// 存储所有注册的处理器，按处理器 ID 索引
    handlers: Arc<RwLock<HashMap<String, Box<dyn MessageHandler>>>>,
    
    /// 是否正在运行
    /// 
    /// 标识事件循环的运行状态
    running: Arc<RwLock<bool>>,
    
    /// 统计管理器
    /// 
    /// 用于收集事件循环的性能统计
    stats_manager: Arc<StatsManager>,
    
    /// 消息接收超时时间
    /// 
    /// 控制事件循环的响应性和资源使用
    receive_timeout: Duration,
}

impl EventLoop {
    /// 创建新的事件循环
    /// 
    /// # 参数
    /// 
    /// * `message_bus` - 消息总线的 Arc 引用
    /// 
    /// # 返回值
    /// 
    /// 返回新创建的事件循环实例
    pub fn new(message_bus: Arc<MessageBus>) -> Self {
        let stats_manager = message_bus.get_stats_manager();
        
        Self {
            message_bus,
            handlers: Arc::new(RwLock::new(HashMap::new())),
            running: Arc::new(RwLock::new(false)),
            stats_manager,
            receive_timeout: Duration::from_millis(100),
        }
    }
    
    /// 使用自定义配置创建事件循环
    /// 
    /// # 参数
    /// 
    /// * `message_bus` - 消息总线的 Arc 引用
    /// * `receive_timeout` - 消息接收超时时间
    /// 
    /// # 返回值
    /// 
    /// 返回新创建的事件循环实例
    pub fn with_config(message_bus: Arc<MessageBus>, receive_timeout: Duration) -> Self {
        let stats_manager = message_bus.get_stats_manager();
        
        Self {
            message_bus,
            handlers: Arc::new(RwLock::new(HashMap::new())),
            running: Arc::new(RwLock::new(false)),
            stats_manager,
            receive_timeout,
        }
    }
    
    /// 注册消息处理器
    /// 
    /// 将处理器添加到事件循环中，处理器将按注册顺序处理消息
    /// 
    /// # 参数
    /// 
    /// * `handler` - 要注册的消息处理器
    pub async fn register_handler(&self, handler: Box<dyn MessageHandler>) {
        let handler_id = handler.handler_id();
        let mut handlers = self.handlers.write().await;
        handlers.insert(handler_id.clone(), handler);
        
        tracing::debug!("消息处理器 {} 已注册到事件循环", handler_id);
    }
    
    /// 注销消息处理器
    /// 
    /// # 参数
    /// 
    /// * `handler_id` - 要注销的处理器 ID
    /// 
    /// # 返回值
    /// 
    /// 如果处理器存在并被成功移除返回 true，否则返回 false
    pub async fn unregister_handler(&self, handler_id: &str) -> bool {
        let mut handlers = self.handlers.write().await;
        let removed = handlers.remove(handler_id).is_some();
        
        if removed {
            tracing::debug!("消息处理器 {} 已从事件循环注销", handler_id);
        }
        
        removed
    }
    
    /// 获取已注册的处理器列表
    /// 
    /// # 返回值
    /// 
    /// 返回所有已注册处理器的 ID 列表
    pub async fn get_registered_handlers(&self) -> Vec<String> {
        let handlers = self.handlers.read().await;
        handlers.keys().cloned().collect()
    }
    
    /// 启动事件循环
    /// 
    /// 将事件循环标记为运行状态，准备开始处理消息
    /// 
    /// # 返回值
    /// 
    /// * `Ok(())` - 启动成功
    /// * `Err(OrionError)` - 如果事件循环已经在运行
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.write().await;
        if *running {
            return Err(OrionError::MessageBusError("事件循环已经在运行".to_string()));
        }
        *running = true;
        
        tracing::info!("事件循环启动成功");
        Ok(())
    }
    
    /// 停止事件循环
    /// 
    /// 将事件循环标记为停止状态，当前正在处理的消息会完成处理
    /// 
    /// # 返回值
    /// 
    /// 总是返回 Ok(())
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.write().await;
        *running = false;
        
        tracing::info!("事件循环已停止");
        Ok(())
    }
    
    /// 检查事件循环是否正在运行
    /// 
    /// # 返回值
    /// 
    /// 如果正在运行返回 true，否则返回 false
    pub async fn is_running(&self) -> bool {
        *self.running.read().await
    }
    
    /// 运行事件循环（这个方法会阻塞直到停止）
    /// 
    /// 这是事件循环的主要方法，会持续从消息总线接收消息并处理，
    /// 直到收到停止信号或发生不可恢复的错误
    /// 
    /// # 返回值
    /// 
    /// * `Ok(())` - 正常停止
    /// * `Err(OrionError)` - 发生不可恢复的错误
    pub async fn run(&self) -> Result<()> {
        tracing::info!("事件循环开始运行");
        
        while *self.running.read().await {
            match self.message_bus.receive_message(self.receive_timeout).await {
                Ok(message) => {
                    // 开始性能测量
                    let measurement = PerformanceMeasurement::start(self.stats_manager.clone());
                    
                    // 处理消息
                    if let Err(e) = self.process_message(message).await {
                        tracing::error!("处理消息时发生错误: {}", e);
                        // 继续运行，不因单个消息处理失败而停止
                    }
                    
                    // 结束性能测量
                    measurement.finish().await;
                }
                Err(OrionError::MessageReceiveTimeout { .. }) => {
                    // 超时是正常的，继续循环
                    continue;
                }
                Err(e) => {
                    tracing::error!("事件循环接收消息失败: {}", e);
                    return Err(e);
                }
            }
        }
        
        tracing::info!("事件循环已停止运行");
        Ok(())
    }
    
    /// 处理单个消息
    /// 
    /// 将消息分发给所有注册的处理器，按优先级顺序处理
    /// 
    /// # 参数
    /// 
    /// * `message` - 要处理的消息
    /// 
    /// # 返回值
    /// 
    /// * `Ok(())` - 处理成功
    /// * `Err(OrionError)` - 处理失败
    async fn process_message(&self, message: Message) -> Result<()> {
        let handlers = self.handlers.read().await;
        
        if handlers.is_empty() {
            tracing::warn!("没有注册的消息处理器，消息被丢弃: {}", message.id);
            return Ok(());
        }
        
        // 收集可以处理此消息的处理器
        let mut applicable_handlers: Vec<_> = handlers
            .iter()
            .filter(|(_, handler)| handler.can_handle(&message))
            .collect();
        
        // 按优先级排序（高优先级在前）
        applicable_handlers.sort_by(|a, b| b.1.priority().cmp(&a.1.priority()));
        
        tracing::trace!(
            "消息 {} 将被 {} 个处理器处理",
            message.id,
            applicable_handlers.len()
        );
        
        // 依次调用处理器
        for (handler_id, handler) in applicable_handlers {
            match handler.handle_message(message.clone()).await {
                Ok(Some(response)) => {
                    // 处理器返回了响应消息，发送它
                    if let Err(e) = self.message_bus.send_message(response).await {
                        tracing::error!(
                            "处理器 {} 的响应消息发送失败: {}",
                            handler_id,
                            e
                        );
                    }
                }
                Ok(None) => {
                    // 处理器成功处理了消息，但没有响应
                    tracing::trace!("处理器 {} 成功处理消息，无响应", handler_id);
                }
                Err(e) => {
                    tracing::error!("处理器 {} 处理消息失败: {}", handler_id, e);
                    // 继续处理其他处理器，不因单个处理器失败而停止
                }
            }
        }
        
        Ok(())
    }
    
    /// 处理单个消息（同步版本，用于测试）
    /// 
    /// 这是一个便捷方法，用于在测试环境中同步处理单个消息
    /// 
    /// # 参数
    /// 
    /// * `message` - 要处理的消息
    /// 
    /// # 返回值
    /// 
    /// * `Ok(())` - 处理成功
    /// * `Err(OrionError)` - 处理失败
    pub async fn process_single_message(&self, message: Message) -> Result<()> {
        self.process_message(message).await
    }
    
    /// 获取事件循环统计信息
    /// 
    /// # 返回值
    /// 
    /// 返回格式化的统计信息字符串
    pub async fn get_stats_summary(&self) -> String {
        let handlers = self.handlers.read().await;
        let handler_count = handlers.len();
        let is_running = self.is_running().await;
        
        format!(
            "事件循环状态: {}\n注册处理器数量: {}\n接收超时: {:?}",
            if is_running { "运行中" } else { "已停止" },
            handler_count,
            self.receive_timeout
        )
    }
    
    /// 设置消息接收超时时间
    /// 
    /// # 参数
    /// 
    /// * `timeout` - 新的超时时间
    pub fn set_receive_timeout(&mut self, timeout: Duration) {
        self.receive_timeout = timeout;
        tracing::debug!("事件循环接收超时设置为: {:?}", timeout);
    }
}

/// 事件循环构建器
/// 
/// 提供便捷的方式来配置和创建事件循环
pub struct EventLoopBuilder {
    message_bus: Arc<MessageBus>,
    receive_timeout: Duration,
    handlers: Vec<Box<dyn MessageHandler>>,
}

impl EventLoopBuilder {
    /// 创建新的事件循环构建器
    /// 
    /// # 参数
    /// 
    /// * `message_bus` - 消息总线的 Arc 引用
    /// 
    /// # 返回值
    /// 
    /// 返回新的构建器实例
    pub fn new(message_bus: Arc<MessageBus>) -> Self {
        Self {
            message_bus,
            receive_timeout: Duration::from_millis(100),
            handlers: Vec::new(),
        }
    }
    
    /// 设置接收超时时间
    /// 
    /// # 参数
    /// 
    /// * `timeout` - 超时时间
    /// 
    /// # 返回值
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn with_receive_timeout(mut self, timeout: Duration) -> Self {
        self.receive_timeout = timeout;
        self
    }
    
    /// 添加消息处理器
    /// 
    /// # 参数
    /// 
    /// * `handler` - 要添加的处理器
    /// 
    /// # 返回值
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn with_handler(mut self, handler: Box<dyn MessageHandler>) -> Self {
        self.handlers.push(handler);
        self
    }
    
    /// 构建事件循环
    /// 
    /// # 返回值
    /// 
    /// 返回配置好的事件循环实例
    pub async fn build(self) -> EventLoop {
        let event_loop = EventLoop::with_config(self.message_bus, self.receive_timeout);
        
        // 注册所有处理器
        for handler in self.handlers {
            event_loop.register_handler(handler).await;
        }
        
        event_loop
    }
}
