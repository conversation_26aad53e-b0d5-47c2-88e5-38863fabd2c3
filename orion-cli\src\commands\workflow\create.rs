//! # 工作流创建功能
//!
//! 实现工作流的创建功能，支持从文件、模板或交互式创建。

use crate::error::{CliError, Result};
use crate::commands::workflow::{types::*, utils::*};
use orion_core::workflow::{
    WorkflowDefinition, WorkflowStep, WorkflowConfig, 
    StepType, StepConfig
};
use std::collections::HashMap;
use uuid::Uuid;

impl CreateWorkflow {
    /// 执行创建工作流
    pub async fn execute(&self) -> Result<()> {
        let workflow_manager = create_workflow_manager(&self.config).await?;

        let workflow_def = if let Some(file_path) = &self.file {
            // 从文件加载工作流定义
            self.load_workflow_from_file(file_path).await?
        } else if let Some(template) = &self.template {
            // 从模板创建工作流
            self.create_workflow_from_template(template)?
        } else if self.interactive {
            // 交互式创建工作流
            self.create_workflow_interactively().await?
        } else {
            return Err(CliError::InvalidArgument {
                error: "必须指定工作流文件、模板或使用交互模式".to_string(),
            });
        };

        // 注册工作流
        workflow_manager.register_workflow(workflow_def.clone()).await
            .map_err(|e| CliError::ExecutionError {
                error: format!("注册工作流失败: {}", e),
            })?;

        println!("✅ 工作流 '{}' 创建成功", workflow_def.name);
        println!("📝 ID: {}", workflow_def.id);
        println!("📖 描述: {}", if workflow_def.description.is_empty() { "无" } else { &workflow_def.description });
        println!("🔧 步骤数: {}", workflow_def.steps.len());

        Ok(())
    }
    
    /// 从文件加载工作流定义
    async fn load_workflow_from_file(&self, file_path: &std::path::PathBuf) -> Result<WorkflowDefinition> {
        let content = tokio::fs::read_to_string(file_path).await
            .map_err(|e| CliError::IoError {
                error: format!("读取工作流文件失败: {}", e),
            })?;

        parse_file_content(file_path, &content).await
    }
    
    /// 从模板创建工作流
    fn create_workflow_from_template(&self, template: &str) -> Result<WorkflowDefinition> {
        let template_cmd = TemplateWorkflow {
            template_type: template.to_string(),
            output: None,
            name: Some(self.name.clone()),
        };
        
        template_cmd.create_workflow_definition()
    }
    
    /// 交互式创建工作流
    async fn create_workflow_interactively(&self) -> Result<WorkflowDefinition> {
        use std::io::{self, Write};

        println!("🚀 交互式工作流创建器");
        println!();

        // 获取基本信息
        let name = self.name.clone();

        print!("工作流描述: ");
        io::stdout().flush().unwrap();
        let mut description = String::new();
        io::stdin().read_line(&mut description).unwrap();
        let description = description.trim();

        print!("工作流版本 [1.0.0]: ");
        io::stdout().flush().unwrap();
        let mut version = String::new();
        io::stdin().read_line(&mut version).unwrap();
        let version = if version.trim().is_empty() {
            "1.0.0".to_string()
        } else {
            version.trim().to_string()
        };

        // 创建基本工作流定义
        let mut workflow_def = WorkflowDefinition {
            id: Uuid::new_v4(),
            name,
            version,
            description: if description.is_empty() { "".to_string() } else { description.to_string() },
            tags: Vec::new(),
            input_parameters: Vec::new(),
            output_parameters: Vec::new(),
            steps: Vec::new(),
            initial_step_id: Uuid::new_v4(),
            config: WorkflowConfig::default(),
            created_at: std::time::SystemTime::now(),
            updated_at: std::time::SystemTime::now(),
        };

        // 添加步骤
        loop {
            println!();
            println!("添加工作流步骤 (当前步骤数: {})", workflow_def.steps.len());
            print!("步骤名称 (留空结束): ");
            io::stdout().flush().unwrap();

            let mut step_name = String::new();
            io::stdin().read_line(&mut step_name).unwrap();
            let step_name = step_name.trim();

            if step_name.is_empty() {
                break;
            }

            let step = self.create_step_interactively(step_name)?;

            if workflow_def.steps.is_empty() {
                workflow_def.initial_step_id = step.id.clone();
            }

            workflow_def.steps.push(step);
        }

        if workflow_def.steps.is_empty() {
            return Err(CliError::InvalidArgument {
                error: "工作流必须至少包含一个步骤".to_string(),
            });
        }

        Ok(workflow_def)
    }
    
    /// 交互式创建步骤
    fn create_step_interactively(&self, name: &str) -> Result<WorkflowStep> {
        use std::io::{self, Write};

        print!("步骤类型 [tool_call/llm_call/condition/loop/parallel/data_transform/delay/human_input/sub_workflow/custom]: ");
        io::stdout().flush().unwrap();

        let mut step_type_str = String::new();
        io::stdin().read_line(&mut step_type_str).unwrap();
        let step_type_str = step_type_str.trim();

        let step_type = match step_type_str {
            "tool_call" | "" => StepType::ToolCall { tool_name: "default_tool".to_string() },
            "llm_call" => StepType::LlmCall {
                model_name: "gpt-4".to_string(),
                prompt_template: "默认提示".to_string()
            },
            "condition" => StepType::Condition { expression: "true".to_string() },
            "loop" => StepType::Loop {
                loop_type: orion_core::workflow::LoopType::For,
                condition: "true".to_string(),
                max_iterations: Some(10)
            },
            "parallel" => StepType::Parallel {
                parallel_steps: Vec::new(),
                wait_strategy: orion_core::workflow::WaitStrategy::All
            },
            "data_transform" => StepType::Transform {
                script: "// 默认数据转换脚本".to_string(),
                language: "javascript".to_string(),
            },
            "delay" => StepType::Delay { duration_seconds: 1 },
            "sub_workflow" => StepType::SubWorkflow { workflow_id: Uuid::new_v4() },
            "custom" => StepType::Custom {
                custom_type: "custom".to_string(),
                config: serde_json::Value::Null
            },
            _ => {
                return Err(CliError::InvalidArgument {
                    error: format!("无效的步骤类型: {}", step_type_str),
                });
            }
        };

        print!("步骤描述: ");
        io::stdout().flush().unwrap();
        let mut description = String::new();
        io::stdin().read_line(&mut description).unwrap();
        let description = description.trim();

        Ok(WorkflowStep {
            id: Uuid::new_v4(),
            name: name.to_string(),
            step_type,
            description: if description.is_empty() { "".to_string() } else { description.to_string() },
            config: StepConfig::default(),
            input_mapping: HashMap::new(),
            output_mapping: HashMap::new(),
            condition: None,
            retry_config: None,
            timeout_seconds: None,
            next_steps: HashMap::new(),
            error_handler: None,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_create_workflow_command() {
        let cmd = CreateWorkflow {
            config: PathBuf::from("test.toml"),
            name: "测试工作流".to_string(),
            description: Some("测试描述".to_string()),
            file: None,
            interactive: false,
            template: Some("simple".to_string()),
        };
        
        assert_eq!(cmd.name, "测试工作流");
        assert_eq!(cmd.template, Some("simple".to_string()));
    }

    #[test]
    fn test_create_from_template() {
        let cmd = CreateWorkflow {
            config: PathBuf::from("test.toml"),
            name: "模板测试".to_string(),
            description: None,
            file: None,
            interactive: false,
            template: Some("simple".to_string()),
        };

        let result = cmd.create_workflow_from_template("simple");
        assert!(result.is_ok());
        
        let workflow_def = result.unwrap();
        assert_eq!(workflow_def.name, "模板测试");
        assert!(!workflow_def.steps.is_empty());
    }
}