//! 构建脚本 - 生成版本和构建信息

use std::process::Command;

fn main() {
    // 生成构建时间戳
    let build_timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC").to_string();
    println!("cargo:rustc-env=VERGEN_BUILD_TIMESTAMP={}", build_timestamp);
    
    // 获取 Git 提交哈希
    let git_hash = get_git_hash().unwrap_or_else(|| "unknown".to_string());
    println!("cargo:rustc-env=VERGEN_GIT_SHA={}", git_hash);
    
    // 获取 Rust 编译器版本
    let rustc_version = get_rustc_version().unwrap_or_else(|| "unknown".to_string());
    println!("cargo:rustc-env=VERGEN_RUSTC_SEMVER={}", rustc_version);
    
    // 告诉 Cargo 在 Git 状态改变时重新运行构建脚本
    println!("cargo:rerun-if-changed=.git/HEAD");
    println!("cargo:rerun-if-changed=.git/index");
}

/// 获取 Git 提交哈希
fn get_git_hash() -> Option<String> {
    let output = Command::new("git")
        .args(["rev-parse", "--short", "HEAD"])
        .output()
        .ok()?;
    
    if output.status.success() {
        let hash = String::from_utf8(output.stdout).ok()?;
        Some(hash.trim().to_string())
    } else {
        None
    }
}

/// 获取 Rust 编译器版本
fn get_rustc_version() -> Option<String> {
    let output = Command::new("rustc")
        .args(["--version"])
        .output()
        .ok()?;
    
    if output.status.success() {
        let version = String::from_utf8(output.stdout).ok()?;
        // 提取版本号部分，例如从 "rustc 1.70.0 (90c541806 2023-05-31)" 提取 "1.70.0"
        let version_part = version
            .split_whitespace()
            .nth(1)?
            .to_string();
        Some(version_part)
    } else {
        None
    }
}