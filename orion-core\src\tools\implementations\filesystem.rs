//! # 文件系统工具实现
//!
//! 提供文件读取、写入等文件系统操作工具。

use crate::error::{OrionError, Result};
use crate::tools::{
    Tool, ToolDefinition, ToolParameter, ParameterType, ParameterConstraints,
    ToolRequest, ToolResult, ToolExample
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;

/// 文件读取工具
/// 
/// 提供安全的文件读取功能，支持多种编码格式。
pub struct FileReadTool;

impl FileReadTool {
    /// 创建新的文件读取工具实例
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for FileReadTool {
    fn definition(&self) -> ToolDefinition {
        ToolDefinition {
            name: "file_read".to_string(),
            version: "1.0.0".to_string(),
            description: "读取文件内容，支持多种编码格式".to_string(),
            category: "filesystem".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "path".to_string(),
                    param_type: ParameterType::FilePath,
                    description: "要读取的文件路径".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                ToolParameter {
                    name: "encoding".to_string(),
                    param_type: ParameterType::String,
                    description: "文件编码格式（默认 utf-8）".to_string(),
                    required: false,
                    default_value: Some(serde_json::Value::String("utf-8".to_string())),
                    constraints: Some(ParameterConstraints {
                        enum_values: Some(vec![
                            serde_json::Value::String("utf-8".to_string()),
                            serde_json::Value::String("gbk".to_string()),
                            serde_json::Value::String("ascii".to_string()),
                        ]),
                        ..Default::default()
                    }),
                },
            ],
            return_type: ParameterType::String,
            requires_sandbox: false,
            tags: vec!["file".to_string(), "read".to_string(), "filesystem".to_string()],
            examples: vec![
                ToolExample {
                    description: "读取文本文件".to_string(),
                    input: {
                        let mut map = HashMap::new();
                        map.insert("path".to_string(), serde_json::Value::String("/path/to/file.txt".to_string()));
                        map
                    },
                    expected_output: serde_json::Value::String("文件内容".to_string()),
                },
            ],
        }
    }
    
    async fn execute(&self, request: ToolRequest) -> Result<ToolResult> {
        let start_time = std::time::Instant::now();
        
        // 验证参数
        self.validate_parameters(&request.parameters)?;
        
        // 获取参数
        let path = request.parameters.get("path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| OrionError::ToolError("file_read: 缺少 path 参数".to_string()))?;
        
        let _encoding = request.parameters.get("encoding")
            .and_then(|v| v.as_str())
            .unwrap_or("utf-8");
        
        // 安全检查：确保路径不包含危险字符
        if path.contains("..") || path.contains("~") {
            return Ok(ToolResult::failure(
                request.id,
                "安全错误: 路径包含不安全的字符".to_string(),
                start_time.elapsed().as_millis() as u64,
            ));
        }
        
        // 读取文件
        match fs::read_to_string(path).await {
            Ok(content) => {
                tracing::info!("成功读取文件: {}", path);
                Ok(ToolResult::success(
                    request.id,
                    serde_json::Value::String(content),
                    start_time.elapsed().as_millis() as u64,
                ))
            }
            Err(e) => {
                tracing::error!("读取文件失败: {} - {}", path, e);
                Ok(ToolResult::failure(
                    request.id,
                    format!("读取文件失败: {}", e),
                    start_time.elapsed().as_millis() as u64,
                ))
            }
        }
    }
}

/// 文件写入工具
/// 
/// 提供安全的文件写入功能，支持覆盖和追加模式。
pub struct FileWriteTool;

impl FileWriteTool {
    /// 创建新的文件写入工具实例
    pub fn new() -> Self {
        Self
    }
}

#[async_trait]
impl Tool for FileWriteTool {
    fn definition(&self) -> ToolDefinition {
        ToolDefinition {
            name: "file_write".to_string(),
            version: "1.0.0".to_string(),
            description: "写入文件内容，支持覆盖和追加模式".to_string(),
            category: "filesystem".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "path".to_string(),
                    param_type: ParameterType::FilePath,
                    description: "要写入的文件路径".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                ToolParameter {
                    name: "content".to_string(),
                    param_type: ParameterType::String,
                    description: "要写入的内容".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                ToolParameter {
                    name: "append".to_string(),
                    param_type: ParameterType::Boolean,
                    description: "是否追加到文件末尾（默认覆盖）".to_string(),
                    required: false,
                    default_value: Some(serde_json::Value::Bool(false)),
                    constraints: None,
                },
            ],
            return_type: ParameterType::Boolean,
            requires_sandbox: true,
            tags: vec!["file".to_string(), "write".to_string(), "filesystem".to_string()],
            examples: vec![
                ToolExample {
                    description: "写入文本文件".to_string(),
                    input: {
                        let mut map = HashMap::new();
                        map.insert("path".to_string(), serde_json::Value::String("/path/to/file.txt".to_string()));
                        map.insert("content".to_string(), serde_json::Value::String("Hello, World!".to_string()));
                        map
                    },
                    expected_output: serde_json::Value::Bool(true),
                },
            ],
        }
    }
    
    async fn execute(&self, request: ToolRequest) -> Result<ToolResult> {
        let start_time = std::time::Instant::now();
        
        // 验证参数
        self.validate_parameters(&request.parameters)?;
        
        // 获取参数
        let path = request.parameters.get("path")
            .and_then(|v| v.as_str())
            .ok_or_else(|| OrionError::ToolError("file_write: 缺少 path 参数".to_string()))?;
        
        let content = request.parameters.get("content")
            .and_then(|v| v.as_str())
            .ok_or_else(|| OrionError::ToolError("file_write: 缺少 content 参数".to_string()))?;
        
        let append = request.parameters.get("append")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);
        
        // 安全检查：确保路径不包含危险字符
        if path.contains("..") || path.contains("~") {
            return Ok(ToolResult::failure(
                request.id,
                "安全错误: 路径包含不安全的字符".to_string(),
                start_time.elapsed().as_millis() as u64,
            ));
        }
        
        // 确保父目录存在
        if let Some(parent) = Path::new(path).parent() {
            if let Err(e) = fs::create_dir_all(parent).await {
                tracing::error!("创建目录失败: {} - {}", parent.display(), e);
                return Ok(ToolResult::failure(
                    request.id,
                    format!("创建目录失败: {}", e),
                    start_time.elapsed().as_millis() as u64,
                ));
            }
        }
        
        // 写入文件
        let result = if append {
            // 追加模式
            match fs::read_to_string(path).await {
                Ok(existing_content) => {
                    let new_content = format!("{}{}", existing_content, content);
                    fs::write(path, new_content).await
                }
                Err(_) => {
                    // 文件不存在，直接写入
                    fs::write(path, content).await
                }
            }
        } else {
            // 覆盖模式
            fs::write(path, content).await
        };
        
        match result {
            Ok(_) => {
                tracing::info!("成功写入文件: {}", path);
                Ok(ToolResult::success(
                    request.id,
                    serde_json::Value::Bool(true),
                    start_time.elapsed().as_millis() as u64,
                ))
            }
            Err(e) => {
                tracing::error!("写入文件失败: {} - {}", path, e);
                Ok(ToolResult::failure(
                    request.id,
                    format!("写入文件失败: {}", e),
                    start_time.elapsed().as_millis() as u64,
                ))
            }
        }
    }
}

impl Default for FileReadTool {
    fn default() -> Self {
        Self::new()
    }
}

impl Default for FileWriteTool {
    fn default() -> Self {
        Self::new()
    }
}
