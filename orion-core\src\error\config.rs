//! # 配置错误模块
//!
//! 包含所有与配置管理相关的错误类型。
//! 涵盖配置文件读取、解析、验证、环境变量等配置操作中的错误。

use std::path::PathBuf;
use thiserror::Error;

/// 配置相关错误枚举
/// 
/// 定义了与配置系统相关的各种错误情况。
/// 包括文件读取失败、格式错误、验证失败等问题。
#[derive(Error, Debug, Clone)]
pub enum ConfigError {
    /// 配置文件未找到错误
    /// 
    /// 当指定的配置文件不存在时抛出。
    #[error("配置文件未找到: {path}")]
    FileNotFound {
        /// 配置文件路径
        path: PathBuf,
    },

    /// 配置文件读取错误
    /// 
    /// 当无法读取配置文件时抛出。
    #[error("配置文件读取失败: {path}, 错误: {message}")]
    FileReadError {
        /// 配置文件路径
        path: PathBuf,
        /// 错误信息
        message: String,
    },

    /// 配置文件解析错误
    /// 
    /// 当配置文件格式不正确时抛出。
    #[error("配置文件解析失败: {path}, 错误: {message}")]
    ParseError {
        /// 配置文件路径
        path: PathBuf,
        /// 错误信息
        message: String,
    },

    /// 配置验证错误
    /// 
    /// 当配置内容不符合要求时抛出。
    #[error("配置验证失败: {field}, 错误: {message}")]
    ValidationError {
        /// 字段名称
        field: String,
        /// 错误信息
        message: String,
    },

    /// 环境变量错误
    /// 
    /// 当环境变量相关操作失败时抛出。
    #[error("环境变量错误: {variable}, 错误: {message}")]
    EnvironmentVariableError {
        /// 环境变量名称
        variable: String,
        /// 错误信息
        message: String,
    },

    /// 配置缺失错误
    /// 
    /// 当必需的配置项缺失时抛出。
    #[error("配置项缺失: {key}")]
    MissingConfiguration {
        /// 配置键名
        key: String,
    },

    /// 配置类型错误
    /// 
    /// 当配置值类型不正确时抛出。
    #[error("配置类型错误: {key}, 期望类型: {expected_type}, 实际值: {actual_value}")]
    TypeMismatch {
        /// 配置键名
        key: String,
        /// 期望的类型
        expected_type: String,
        /// 实际的值
        actual_value: String,
    },
}

impl ConfigError {
    /// 创建文件未找到错误
    pub fn file_not_found(path: impl Into<PathBuf>) -> Self {
        Self::FileNotFound {
            path: path.into(),
        }
    }

    /// 创建文件读取错误
    pub fn file_read_error(path: impl Into<PathBuf>, message: impl Into<String>) -> Self {
        Self::FileReadError {
            path: path.into(),
            message: message.into(),
        }
    }

    /// 创建解析错误
    pub fn parse_error(path: impl Into<PathBuf>, message: impl Into<String>) -> Self {
        Self::ParseError {
            path: path.into(),
            message: message.into(),
        }
    }

    /// 创建验证错误
    pub fn validation_error(field: impl Into<String>, message: impl Into<String>) -> Self {
        Self::ValidationError {
            field: field.into(),
            message: message.into(),
        }
    }

    /// 创建环境变量错误
    pub fn environment_variable_error(
        variable: impl Into<String>,
        message: impl Into<String>,
    ) -> Self {
        Self::EnvironmentVariableError {
            variable: variable.into(),
            message: message.into(),
        }
    }

    /// 创建配置缺失错误
    pub fn missing_configuration(key: impl Into<String>) -> Self {
        Self::MissingConfiguration {
            key: key.into(),
        }
    }

    /// 创建类型不匹配错误
    pub fn type_mismatch(
        key: impl Into<String>,
        expected_type: impl Into<String>,
        actual_value: impl Into<String>,
    ) -> Self {
        Self::TypeMismatch {
            key: key.into(),
            expected_type: expected_type.into(),
            actual_value: actual_value.into(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_error_creation() {
        let error = ConfigError::file_not_found("/path/to/config.toml");
        assert!(error.to_string().contains("配置文件未找到"));
        assert!(error.to_string().contains("/path/to/config.toml"));
    }

    #[test]
    fn test_validation_error() {
        let error = ConfigError::validation_error("api_key", "不能为空");
        assert!(error.to_string().contains("配置验证失败"));
        assert!(error.to_string().contains("api_key"));
        assert!(error.to_string().contains("不能为空"));
    }

    #[test]
    fn test_type_mismatch() {
        let error = ConfigError::type_mismatch("port", "number", "abc");
        assert!(error.to_string().contains("配置类型错误"));
        assert!(error.to_string().contains("port"));
        assert!(error.to_string().contains("number"));
        assert!(error.to_string().contains("abc"));
    }

    #[test]
    fn test_missing_configuration() {
        let error = ConfigError::missing_configuration("database_url");
        assert!(error.to_string().contains("配置项缺失"));
        assert!(error.to_string().contains("database_url"));
    }
}
