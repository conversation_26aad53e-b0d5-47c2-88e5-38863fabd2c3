//! # Kimi（月之暗面）提供商实现
//!
//! 实现 Kimi API 的 LLM 提供商，支持 Moonshot 系列模型。

use crate::error::{OrionError, Result, LlmError};
use super::super::provider::LlmProvider;
use super::super::types::{
    LlmRequest, LlmResponse, StreamChunk, ModelConfig, MessageRole, 
    ToolCall, TokenUsage, FinishReason
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use uuid::Uuid;
use tracing;

/// <PERSON>i（月之暗面）提供商实现
/// 
/// 支持 Moonshot 系列模型，包括不同上下文长度的版本。
pub struct KimiProvider {
    /// HTTP 客户端
    client: reqwest::Client,
    /// 模型配置
    config: ModelConfig,
}

impl KimiProvider {
    /// 创建新的 Kimi 提供商
    /// 
    /// # 参数
    /// 
    /// * `config` - 模型配置，包含 API 密钥、端点等信息
    /// 
    /// # 返回
    /// 
    /// 返回配置好的 Kimi 提供商实例
    pub fn new(config: ModelConfig) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .map_err(|e| OrionError::Llm(LlmError::engine_error(format!("创建 HTTP 客户端失败: {}", e))))?;

        Ok(Self { client, config })
    }
}

#[async_trait]
impl LlmProvider for KimiProvider {
    fn name(&self) -> &str {
        "kimi"
    }

    async fn supported_models(&self) -> Result<Vec<String>> {
        Ok(vec![
            "moonshot-v1-8k".to_string(),
            "moonshot-v1-32k".to_string(),
            "moonshot-v1-128k".to_string(),
        ])
    }

    async fn complete(&self, request: LlmRequest) -> Result<LlmResponse> {
        // 构建 Kimi API 请求
        let mut body = serde_json::json!({
            "model": self.config.name,
            "messages": request.messages.iter().map(|msg| {
                serde_json::json!({
                    "role": match msg.role {
                        MessageRole::System => "system",
                        MessageRole::User => "user",
                        MessageRole::Assistant => "assistant",
                        MessageRole::Tool => "tool",
                    },
                    "content": msg.content
                })
            }).collect::<Vec<_>>(),
        });

        // 添加模型参数
        let params = request.parameters.as_ref().unwrap_or(&self.config.parameters);
        body["temperature"] = serde_json::json!(params.temperature);
        body["max_tokens"] = serde_json::json!(params.max_tokens);

        if let Some(top_p) = params.top_p {
            body["top_p"] = serde_json::json!(top_p);
        }

        if !params.stop_sequences.is_empty() {
            body["stop"] = serde_json::json!(params.stop_sequences);
        }

        // 添加工具定义（如果支持）
        if !request.tools.is_empty() {
            body["tools"] = serde_json::json!(request.tools.iter().map(|tool| {
                serde_json::json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());
        }

        // 发送请求到 Kimi API
        let endpoint = self.config.endpoint.as_deref().unwrap_or("https://api.moonshot.cn/v1/chat/completions");
        let api_key = self.config.api_key.as_ref()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("kimi", "缺少 API 密钥")))?;

        tracing::debug!("发送请求到 Kimi API: {}", endpoint);

        let response = self.client
            .post(endpoint)
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| OrionError::Llm(LlmError::api_call_failed("kimi", format!("请求失败: {}", e))))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_else(|_| "未知错误".to_string());
            return Err(OrionError::Llm(LlmError::api_call_failed("kimi", format!("API 错误 ({}): {}", status, error_text))));
        }

        let response_json: serde_json::Value = response.json().await
            .map_err(|e| OrionError::Llm(LlmError::api_call_failed("kimi", format!("解析响应失败: {}", e))))?;

        tracing::debug!("Kimi API 响应: {}", serde_json::to_string_pretty(&response_json).unwrap_or_default());

        // 解析响应
        let choices = response_json["choices"].as_array()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("kimi", format!("响应中缺少 choices 字段: {}", response_json))))?;

        if choices.is_empty() {
            return Err(OrionError::Llm(LlmError::api_call_failed("kimi", "响应中 choices 数组为空")));
        }

        let choice = choices[0].as_object()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("kimi", "无效的 choice 格式")))?;

        let message = choice.get("message")
            .and_then(|m| m.as_object())
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("kimi", "无效的消息格式")))?;

        let content = message.get("content")
            .and_then(|c| c.as_str())
            .unwrap_or("")
            .to_string();

        // 解析工具调用（如果支持）
        let mut tool_calls = Vec::new();
        if let Some(calls) = message.get("tool_calls").and_then(|tc| tc.as_array()) {
            for call in calls {
                if let (Some(id), Some(function)) = (call["id"].as_str(), call["function"].as_object()) {
                    if let Some(name) = function["name"].as_str() {
                        let arguments = function["arguments"].clone();
                        tool_calls.push(ToolCall {
                            id: id.to_string(),
                            name: name.to_string(),
                            arguments,
                        });
                    }
                }
            }
        }

        // 解析使用统计
        let usage = if let Some(usage_obj) = response_json["usage"].as_object() {
            TokenUsage {
                input_tokens: usage_obj["prompt_tokens"].as_u64().unwrap_or(0) as u32,
                output_tokens: usage_obj["completion_tokens"].as_u64().unwrap_or(0) as u32,
                total_tokens: usage_obj["total_tokens"].as_u64().unwrap_or(0) as u32,
            }
        } else {
            TokenUsage::default()
        };

        // 解析完成原因
        let finish_reason = match choice["finish_reason"].as_str() {
            Some("stop") => FinishReason::Stop,
            Some("length") => FinishReason::Length,
            Some("tool_calls") => FinishReason::ToolCalls,
            Some("content_filter") => FinishReason::ContentFilter,
            Some(other) => FinishReason::Error(format!("未知完成原因: {}", other)),
            None => FinishReason::Error("缺少完成原因".to_string()),
        };

        Ok(LlmResponse {
            request_id: request.id,
            content,
            tool_calls,
            usage,
            finish_reason,
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        })
    }

    async fn stream(&self, request: LlmRequest) -> Result<tokio::sync::mpsc::Receiver<Result<StreamChunk>>> {
        use tokio::sync::mpsc;

        // 创建通道
        let (tx, rx) = mpsc::channel::<Result<StreamChunk>>(100);

        // 构建流式请求
        let mut body = serde_json::json!({
            "model": self.config.name,
            "messages": request.messages.iter().map(|msg| {
                serde_json::json!({
                    "role": match msg.role {
                        MessageRole::System => "system",
                        MessageRole::User => "user",
                        MessageRole::Assistant => "assistant",
                        MessageRole::Tool => "tool",
                    },
                    "content": msg.content
                })
            }).collect::<Vec<_>>(),
            "stream": true,  // 启用流式响应
        });

        // 添加模型参数
        let params = request.parameters.as_ref().unwrap_or(&self.config.parameters);
        body["temperature"] = serde_json::json!(params.temperature);
        body["max_tokens"] = serde_json::json!(params.max_tokens);

        if let Some(top_p) = params.top_p {
            body["top_p"] = serde_json::json!(top_p);
        }

        if !params.stop_sequences.is_empty() {
            body["stop"] = serde_json::json!(params.stop_sequences);
        }

        // 添加工具定义（如果支持）
        if !request.tools.is_empty() {
            body["tools"] = serde_json::json!(request.tools.iter().map(|tool| {
                serde_json::json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());
        }

        let endpoint = self.config.endpoint.as_deref().unwrap_or("https://api.moonshot.cn/v1/chat/completions").to_string();
        let api_key = self.config.api_key.as_ref()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("kimi", "缺少 API 密钥")))?
            .clone();

        let client = self.client.clone();
        let request_id = request.id;

        // 在后台任务中处理流式响应
        tokio::spawn(async move {
            let result = KimiProvider::handle_stream_response(
                client,
                endpoint,
                api_key,
                body,
                request_id,
                tx,
            ).await;

            if let Err(e) = result {
                tracing::error!("流式响应处理失败: {}", e);
            }
        });

        Ok(rx)
    }

    async fn validate_config(&self, config: &ModelConfig) -> Result<()> {
        if config.api_key.is_none() {
            return Err(OrionError::Llm(LlmError::api_call_failed("kimi", "缺少 API 密钥")));
        }

        let supported = self.supported_models().await?;
        if !supported.contains(&config.name) {
            return Err(OrionError::Llm(LlmError::unsupported_model(format!(
                "{}，支持的模型: {:?}",
                config.name,
                supported
            ))));
        }

        Ok(())
    }
}

impl KimiProvider {
    /// 处理流式响应
    ///
    /// 处理 Kimi API 的 Server-Sent Events (SSE) 流式响应。
    async fn handle_stream_response(
        client: reqwest::Client,
        endpoint: String,
        api_key: String,
        body: serde_json::Value,
        request_id: Uuid,
        tx: tokio::sync::mpsc::Sender<Result<StreamChunk>>,
    ) -> Result<()> {
        use futures::StreamExt;

        tracing::debug!("发送流式请求到 Kimi API: {}", endpoint);

        // 发送流式请求
        let response = client
            .post(&endpoint)
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .header("Accept", "text/event-stream")
            .header("Cache-Control", "no-cache")
            .header("Connection", "keep-alive")
            .timeout(std::time::Duration::from_secs(120)) // 增加超时时间
            .json(&body)
            .send()
            .await
            .map_err(|e| OrionError::Llm(LlmError::api_call_failed("kimi", format!("流式请求失败: {}", e))))?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_else(|_| "未知错误".to_string());
            return Err(OrionError::Llm(LlmError::api_call_failed("kimi", format!("流式请求错误 ({}): {}", status, error_text))));
        }

        // 处理 SSE 流
        let mut stream = response.bytes_stream();
        let mut buffer = String::new();

        while let Some(chunk_result) = stream.next().await {
            let chunk = chunk_result
                .map_err(|e| OrionError::Llm(LlmError::api_call_failed("kimi", format!("读取流式数据失败: {}", e))))?;

            let chunk_str = String::from_utf8_lossy(&chunk);
            buffer.push_str(&chunk_str);

            // 处理完整的 SSE 事件
            while let Some(event_end) = buffer.find("\n\n") {
                let event = buffer[..event_end].to_string();
                buffer = buffer[event_end + 2..].to_string();

                if let Err(e) = Self::process_sse_event(&event, request_id, &tx).await {
                    tracing::error!("处理 SSE 事件失败: {}", e);
                    let _ = tx.send(Err(e)).await;
                    return Ok(());
                }
            }
        }

        // 处理剩余的缓冲区内容
        if !buffer.trim().is_empty() {
            if let Err(e) = Self::process_sse_event(&buffer, request_id, &tx).await {
                tracing::error!("处理最后的 SSE 事件失败: {}", e);
                let _ = tx.send(Err(e)).await;
            }
        }

        Ok(())
    }

    /// 处理 SSE 事件
    ///
    /// 解析单个 Server-Sent Event 并发送到通道。
    async fn process_sse_event(
        event: &str,
        request_id: Uuid,
        tx: &tokio::sync::mpsc::Sender<Result<StreamChunk>>,
    ) -> Result<()> {
        // 解析 SSE 事件格式
        let lines: Vec<&str> = event.lines().collect();
        let mut data_lines = Vec::new();

        for line in lines {
            if line.starts_with("data: ") {
                let data = &line[6..]; // 移除 "data: " 前缀
                if data.trim() == "[DONE]" {
                    // 流结束标记
                    let final_chunk = StreamChunk {
                        request_id,
                        delta: String::new(),
                        is_final: true,
                        tool_calls: Vec::new(),
                        finish_reason: Some(FinishReason::Stop),
                    };
                    let _ = tx.send(Ok(final_chunk)).await;
                    return Ok(());
                }
                data_lines.push(data);
            }
        }

        // 处理每个数据行
        for data in data_lines {
            if data.trim().is_empty() {
                continue;
            }

            // 解析 JSON 数据
            let json_data: serde_json::Value = serde_json::from_str(data)
                .map_err(|e| OrionError::Llm(LlmError::api_call_failed("kimi", format!("解析流式响应 JSON 失败: {}", e))))?;

            tracing::debug!("收到流式数据: {}", serde_json::to_string_pretty(&json_data).unwrap_or_default());

            // 解析 choices
            if let Some(choices) = json_data["choices"].as_array() {
                for choice in choices {
                    if let Some(delta) = choice["delta"].as_object() {
                        let content = delta.get("content")
                            .and_then(|c| c.as_str())
                            .unwrap_or("")
                            .to_string();

                        // 解析完成原因
                        let finish_reason = choice.get("finish_reason")
                            .and_then(|fr| fr.as_str())
                            .map(|reason| match reason {
                                "stop" => FinishReason::Stop,
                                "length" => FinishReason::Length,
                                "tool_calls" => FinishReason::ToolCalls,
                                "content_filter" => FinishReason::ContentFilter,
                                other => FinishReason::Error(format!("未知完成原因: {}", other)),
                            });

                        // 解析工具调用（如果有）
                        let mut tool_calls = Vec::new();
                        if let Some(calls) = delta.get("tool_calls").and_then(|tc| tc.as_array()) {
                            for call in calls {
                                if let (Some(id), Some(function)) = (
                                    call["id"].as_str(),
                                    call["function"].as_object()
                                ) {
                                    if let Some(name) = function["name"].as_str() {
                                        let arguments = function["arguments"].clone();
                                        tool_calls.push(ToolCall {
                                            id: id.to_string(),
                                            name: name.to_string(),
                                            arguments,
                                        });
                                    }
                                }
                            }
                        }

                        let is_final = finish_reason.is_some();

                        // 创建流式块
                        let chunk = StreamChunk {
                            request_id,
                            delta: content,
                            is_final,
                            tool_calls,
                            finish_reason,
                        };

                        // 发送块到通道
                        if let Err(_) = tx.send(Ok(chunk)).await {
                            // 接收端已关闭
                            return Ok(());
                        }
                    }
                }
            }
        }

        Ok(())
    }
}
