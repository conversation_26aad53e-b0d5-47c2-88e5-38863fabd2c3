//! # 错误处理模块
//!
//! 定义 Orion Core 的错误类型和处理机制，使用 thiserror 提供结构化错误信息。

use std::path::PathBuf;
use thiserror::Error;

// 声明错误子模块
pub mod core;
pub mod llm;
pub mod workflow;
pub mod network;
pub mod config;
pub mod types;

// 重新导出错误类型
pub use core::CoreError;
pub use llm::LlmError;
pub use workflow::WorkflowError;
pub use network::NetworkError;
pub use config::ConfigError;

// 重新导出通用类型
pub use types::{
    ErrorSeverity, ErrorCategory, ErrorCode, ErrorContext, 
    RecoveryStrategy, ErrorStats
};

/// Orion Core 的主要错误类型
#[derive(Error, Debug, Clone)]
pub enum OrionError {
    /// 核心系统错误
    #[error("核心系统错误: {0}")]
    Core(#[from] CoreError),

    /// 配置相关错误
    #[error("配置错误: {0}")]
    Config(#[from] ConfigError),

    /// LLM相关错误
    #[error("LLM错误: {0}")]
    Llm(#[from] LlmError),

    /// 网络相关错误
    #[error("网络错误: {0}")]
    Network(#[from] NetworkError),

    /// 消息队列错误
    #[error("消息队列错误: {0}")]
    MessageBusError(String),

    /// 消息发送失败
    #[error("消息发送失败: 从 {from} 到 {to}")]
    MessageSendError { from: String, to: String },

    /// 消息接收超时
    #[error("消息接收超时: {timeout_ms}ms")]
    MessageReceiveTimeout { timeout_ms: u64 },

    /// 安全沙箱错误
    #[error("安全沙箱错误: {0}")]
    SecurityError(String),

    /// 文件访问被拒绝
    #[error("文件访问被拒绝: {path}, 原因: {reason}")]
    FileAccessDenied { path: PathBuf, reason: String },

    /// 命令执行被拒绝
    #[error("命令执行被拒绝: {command}, 原因: {reason}")]
    CommandExecutionDenied { command: String, reason: String },

    /// 工具系统错误
    #[error("工具系统错误: {0}")]
    ToolError(String),

    /// 工具不存在
    #[error("工具不存在: {tool_name}")]
    ToolNotFound { tool_name: String },

    /// 工具执行失败
    #[error("工具执行失败: {tool_name}, 错误: {message}")]
    ToolExecutionError { tool_name: String, message: String },

    /// 上下文管理错误
    #[error("上下文管理错误: {0}")]
    ContextError(String),

    /// Token 限制超出
    #[error("Token 限制超出: 当前 {current}, 最大 {max}")]
    TokenLimitExceeded { current: usize, max: usize },

    /// 上下文压缩失败
    #[error("上下文压缩失败: {reason}")]
    ContextCompressionError { reason: String },

    /// 工作流相关错误
    #[error("工作流错误: {0}")]
    Workflow(#[from] WorkflowError),

    /// Agent 错误
    #[error("Agent 错误: {0}")]
    AgentError(String),

    /// Agent 不可用
    #[error("Agent 不可用: {agent_id}")]
    AgentUnavailable { agent_id: String },

    /// 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(String),

    /// 文件系统错误
    #[error("文件系统错误: {0}")]
    FileSystemError(String),

    /// 序列化错误
    #[error("序列化错误: {0}")]
    SerializationError(String),

    /// JSON 序列化错误
    #[error("JSON 序列化错误: {0}")]
    JsonError(String),

    /// TOML 序列化错误
    #[error("TOML 序列化错误: {0}")]
    TomlSerializeError(String),

    /// 时间相关错误
    #[error("时间错误: {0}")]
    TimeError(String),

    /// 系统时间错误
    #[error("系统时间错误: {0}")]
    SystemTimeError(String),


}

/// Orion Core 的 Result 类型别名
pub type Result<T> = std::result::Result<T, OrionError>;

impl OrionError {
    /// 获取错误的严重程度
    pub fn severity(&self) -> ErrorSeverity {
        use types::ErrorSeverity;
        match self {
            // 严重级别错误
            OrionError::Core(CoreError::InitializationError { .. }) => ErrorSeverity::Critical,
            OrionError::Core(CoreError::InternalError { .. }) => ErrorSeverity::Critical,

            // 高级别错误
            OrionError::Config(_) => ErrorSeverity::High,

            OrionError::MessageBusError(_)
            | OrionError::SecurityError(_)
            | OrionError::AgentUnavailable { .. } => ErrorSeverity::High,

            // 中级别错误
            OrionError::Llm(_) => ErrorSeverity::Medium,

            OrionError::Workflow(_) => ErrorSeverity::Medium,

            OrionError::ToolError(_) | OrionError::ToolExecutionError { .. } => {
                ErrorSeverity::Medium
            }

            // 低级别错误
            OrionError::Network(_)
            | OrionError::MessageSendError { .. }
            | OrionError::MessageReceiveTimeout { .. } => ErrorSeverity::Low,

            OrionError::FileAccessDenied { .. } | OrionError::CommandExecutionDenied { .. } => {
                ErrorSeverity::Low
            }

            OrionError::ToolNotFound { .. }
            | OrionError::ContextError(_)
            | OrionError::TokenLimitExceeded { .. }
            | OrionError::ContextCompressionError { .. } => ErrorSeverity::Low,

            OrionError::AgentError(_)
            | OrionError::DatabaseError(_)
            | OrionError::FileSystemError(_)
            | OrionError::SerializationError(_)
            | OrionError::JsonError(_)
            | OrionError::TomlSerializeError(_)
            | OrionError::TimeError(_)
            | OrionError::SystemTimeError(_) => ErrorSeverity::Low,
        }
    }

    /// 检查错误是否可以重试
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            OrionError::Network(network_error) if network_error.is_retryable()
        ) || matches!(
            self,
            OrionError::Llm(LlmError::ApiCallFailed { .. })
                | OrionError::MessageSendError { .. }
                | OrionError::MessageReceiveTimeout { .. }
                | OrionError::DatabaseError(_)
        )
    }

    /// 获取错误的用户友好描述
    pub fn user_message(&self) -> String {
        match self {
            OrionError::Core(CoreError::InitializationError { .. }) => "系统初始化失败，请检查配置和依赖".to_string(),
            OrionError::Config(ConfigError::FileNotFound { .. }) => {
                "配置文件未找到，请运行 'orion config init' 创建默认配置".to_string()
            }
            OrionError::Llm(LlmError::UnsupportedModel { model }) => {
                format!("不支持的模型 '{}'，请检查配置文件中的模型设置", model)
            }
            OrionError::Network(_) => "网络连接失败，请检查网络设置和代理配置".to_string(),
            OrionError::TokenLimitExceeded { .. } => {
                "上下文长度超出限制，系统将自动压缩历史对话".to_string()
            }
            _ => self.to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_severity() {
        use types::ErrorSeverity;
        assert_eq!(
            OrionError::Core(CoreError::initialization_failed("test")).severity(),
            ErrorSeverity::Critical
        );
        assert_eq!(
            OrionError::Config(ConfigError::validation_error("test", "invalid")).severity(),
            ErrorSeverity::High
        );
        assert_eq!(
            OrionError::Llm(LlmError::engine_error("test")).severity(),
            ErrorSeverity::Medium
        );

        // 测试网络错误
        assert_eq!(
            OrionError::Network(NetworkError::request_failed("test error")).severity(),
            ErrorSeverity::Low
        );
    }

    #[test]
    fn test_error_retryable() {
        // 测试网络错误重试
        assert!(OrionError::Network(NetworkError::connection_timeout("url", 30)).is_retryable());
        assert!(!OrionError::Config(ConfigError::validation_error("test", "invalid")).is_retryable());
    }

    #[test]
    fn test_user_message() {
        let error = OrionError::Llm(LlmError::unsupported_model("gpt-5"));
        assert!(error.user_message().contains("gpt-5"));
    }
}
// 为外部错误类型实现 From trait
impl From<reqwest::Error> for OrionError {
    fn from(err: reqwest::Error) -> Self {
        OrionError::Network(NetworkError::request_failed(err.to_string()))
    }
}

impl From<rusqlite::Error> for OrionError {
    fn from(err: rusqlite::Error) -> Self {
        OrionError::DatabaseError(err.to_string())
    }
}

impl From<std::io::Error> for OrionError {
    fn from(err: std::io::Error) -> Self {
        OrionError::FileSystemError(err.to_string())
    }
}

impl From<serde_json::Error> for OrionError {
    fn from(err: serde_json::Error) -> Self {
        OrionError::JsonError(err.to_string())
    }
}

impl From<toml::ser::Error> for OrionError {
    fn from(err: toml::ser::Error) -> Self {
        OrionError::TomlSerializeError(err.to_string())
    }
}

impl From<std::time::SystemTimeError> for OrionError {
    fn from(err: std::time::SystemTimeError) -> Self {
        OrionError::SystemTimeError(err.to_string())
    }
}