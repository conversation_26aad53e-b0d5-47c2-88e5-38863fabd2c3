//! # 工具执行和测试功能
//!
//! 实现工具的执行和测试功能。

use crate::error::{CliError, Result};
use crate::commands::tools::{types::*, utils::*};
use orion_core::tools::{ToolRequest, ToolContext, ToolResult};
use std::collections::HashMap;
use uuid::Uuid;

impl RunTool {
    /// 执行运行工具
    pub async fn execute(&self) -> Result<()> {
        let tool_registry = create_tool_registry(&self.config).await?;
        
        // 解析参数
        let params = self.parse_parameters().await?;
        
        // 创建工具请求
        let request = ToolRequest {
            id: Uuid::new_v4(),
            tool_name: self.tool_name.clone(),
            parameters: params,
            context: ToolContext {
                working_directory: None,
                environment: HashMap::new(),
                session_id: Some(Uuid::new_v4().to_string()),
                user_id: Some("cli-user".to_string()),
                timeout_seconds: self.timeout,
            },
            timestamp: std::time::SystemTime::now(),
        };
        
        if self.verbose {
            println!("🚀 执行工具: {}", self.tool_name);
            println!("📝 参数: {}", serde_json::to_string_pretty(&request.parameters).unwrap_or_default());
            println!();
        }
        
        // 执行工具
        let start_time = std::time::Instant::now();
        let result = if self.sandbox {
            tool_registry.execute_tool_with_sandbox(request).await
        } else {
            tool_registry.execute_tool(request).await
        }.map_err(|e| CliError::ExecutionError {
            error: format!("执行工具失败: {}", e),
        })?;
        let execution_time = start_time.elapsed();
        
        // 打印结果
        self.print_tool_result(&result, execution_time)?;
        
        Ok(())
    }
    
    /// 解析参数
    async fn parse_parameters(&self) -> Result<HashMap<String, serde_json::Value>> {
        let mut params = HashMap::new();
        
        // 从命令行参数解析
        if let Some(params_str) = &self.params {
            let parsed: HashMap<String, serde_json::Value> = serde_json::from_str(params_str)
                .map_err(|e| CliError::InvalidArgument {
                    error: format!("解析参数失败: {}", e),
                })?;
            params.extend(parsed);
        }
        
        // 从文件解析
        if let Some(params_file) = &self.params_file {
            let content = tokio::fs::read_to_string(params_file).await
                .map_err(|e| CliError::IoError {
                    error: format!("读取参数文件失败: {}", e),
                })?;
            
            let file_params: HashMap<String, serde_json::Value> = if params_file.extension()
                .and_then(|ext| ext.to_str()) == Some("yaml") {
                serde_yaml::from_str(&content)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("解析 YAML 参数文件失败: {}", e),
                    })?
            } else {
                serde_json::from_str(&content)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("解析 JSON 参数文件失败: {}", e),
                    })?
            };
            
            params.extend(file_params);
        }
        
        Ok(params)
    }
    
    /// 打印工具结果
    fn print_tool_result(&self, result: &ToolResult, execution_time: std::time::Duration) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(result, &self.format)?;
            }
            "text" | _ => {
                if result.success {
                    println!("✅ 工具执行成功");
                } else {
                    println!("❌ 工具执行失败");
                }
                
                if result.data != serde_json::Value::Null {
                    println!("📤 输出:");
                    println!("{}", serde_json::to_string_pretty(&result.data).unwrap_or_default());
                }
                
                if let Some(error) = &result.error {
                    println!("❌ 错误: {}", error);
                }
                
                println!("📊 资源使用:");
                println!("  执行时间: {:.2}秒", execution_time.as_secs_f64());
                println!("  内存使用峰值: {:.2} MB", result.resources_used.peak_memory_bytes as f64 / 1024.0 / 1024.0);
                println!("  CPU 时间: {:.2}毫秒", result.resources_used.cpu_time_ms);
            }
        }
        
        Ok(())
    }
}

impl TestTool {
    /// 执行测试工具
    pub async fn execute(&self) -> Result<()> {
        let _tool_registry = create_tool_registry(&self.config).await?;
        
        if self.all {
            println!("🧪 运行工具 '{}' 的所有测试", self.tool_name);
        } else if let Some(test_case) = &self.test_case {
            println!("🧪 运行工具 '{}' 的测试用例: {}", self.tool_name, test_case);
        } else {
            println!("🧪 运行工具 '{}' 的默认测试", self.tool_name);
        }
        
        if self.verbose {
            println!("详细测试输出模式已启用");
        }
        
        // 模拟测试执行
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
        
        println!("✅ 测试完成");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_run_tool_command() {
        let cmd = RunTool {
            config: PathBuf::from("test.toml"),
            tool_name: "file_read".to_string(),
            params: Some("{\"path\": \"/tmp/test.txt\"}".to_string()),
            params_file: None,
            format: "text".to_string(),
            timeout: Some(30),
            sandbox: true,
            verbose: false,
        };
        
        assert_eq!(cmd.tool_name, "file_read");
        assert!(cmd.sandbox);
        assert_eq!(cmd.timeout, Some(30));
    }

    #[tokio::test]
    async fn test_parse_parameters() {
        let cmd = RunTool {
            config: PathBuf::from("test.toml"),
            tool_name: "test_tool".to_string(),
            params: Some("{\"key\": \"value\"}".to_string()),
            params_file: None,
            format: "text".to_string(),
            timeout: None,
            sandbox: false,
            verbose: false,
        };

        let params = cmd.parse_parameters().await.unwrap();
        assert_eq!(params.get("key").unwrap(), "value");
    }

    #[tokio::test]
    async fn test_test_tool_command() {
        let cmd = TestTool {
            config: PathBuf::from("test.toml"),
            tool_name: "test_tool".to_string(),
            test_case: Some("basic_test".to_string()),
            all: false,
            verbose: true,
        };
        
        assert_eq!(cmd.tool_name, "test_tool");
        assert_eq!(cmd.test_case, Some("basic_test".to_string()));
        assert!(cmd.verbose);
    }
}