//! # Agent 核心实现模块
//!
//! 提供Agent的核心结构体和主要功能实现，
//! 整合所有子模块，提供统一的Agent接口。

use crate::agent::config::AgentConfig;
use crate::agent::state::StateManager;
use crate::agent::stats::StatsManager;
use crate::agent::task::TaskProcessor;
use crate::agent::thinking::ThinkingManager;
use crate::agent::types::{AgentState, AgentStats, TaskRequest, TaskResponse, ResponseType};
use crate::context::{ContextManager, ContextEntry, ContextEntryType, ImportanceLevel};
use crate::error::{OrionError, Result};
use crate::llm::LlmEngine;
use crate::message::MessageBus;
use crate::sandbox::Sandbox;
use crate::tools::ToolRegistry;
use crate::workflow::WorkflowManager;

use std::collections::HashMap;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use uuid::Uuid;

/// Agent 核心结构体
/// 
/// 整合所有Agent功能模块，提供统一的Agent接口
pub struct Agent {
    /// Agent配置
    config: AgentConfig,
    /// 状态管理器
    state_manager: StateManager,
    /// 统计信息管理器
    stats_manager: StatsManager,
    /// 思考过程管理器
    thinking_manager: Arc<ThinkingManager>,
    /// 任务处理器
    task_processor: TaskProcessor,
    /// LLM引擎
    llm_engine: Arc<LlmEngine>,
    /// 工具注册表
    tool_registry: Arc<ToolRegistry>,
    /// 上下文管理器
    context_manager: Arc<ContextManager>,
    /// 沙箱（可选）
    sandbox: Option<Arc<Sandbox>>,
    /// 工作流管理器
    workflow_manager: Arc<WorkflowManager>,
    /// 消息总线
    message_bus: Arc<MessageBus>,
    /// 当前任务
    current_task: Arc<RwLock<Option<TaskRequest>>>,
}

impl Agent {
    /// 获取消息总线的引用
    ///
    /// # 返回
    /// 返回消息总线的Arc引用
    pub fn message_bus(&self) -> &Arc<MessageBus> {
        &self.message_bus
    }
    /// 创建新的Agent实例
    /// 
    /// # 参数
    /// * `config` - Agent配置
    /// * `llm_engine` - LLM引擎
    /// * `tool_registry` - 工具注册表
    /// * `context_manager` - 上下文管理器
    /// * `workflow_manager` - 工作流管理器
    /// * `message_bus` - 消息总线
    /// 
    /// # 返回
    /// 返回新创建的Agent实例
    pub fn new(
        config: AgentConfig,
        llm_engine: Arc<LlmEngine>,
        tool_registry: Arc<ToolRegistry>,
        context_manager: Arc<ContextManager>,
        workflow_manager: Arc<WorkflowManager>,
        message_bus: Arc<MessageBus>,
    ) -> Result<Self> {
        // 验证配置
        config.validate().map_err(|e| OrionError::AgentError(e))?;

        // 创建沙箱（如果启用）
        let sandbox = if config.enable_sandbox {
            Some(Arc::new(Sandbox::new()))
        } else {
            None
        };

        // 创建各种管理器
        let state_manager = StateManager::new(config.name.clone());
        let stats_manager = StatsManager::new(config.name.clone());
        let thinking_manager = Arc::new(ThinkingManager::new(config.name.clone()));

        // 创建任务处理器
        let task_processor = TaskProcessor::new(
            config.clone(),
            llm_engine.clone(),
            tool_registry.clone(),
            context_manager.clone(),
            workflow_manager.clone(),
            thinking_manager.clone(),
        );

        tracing::info!("Agent '{}' 创建成功", config.name);

        Ok(Self {
            config,
            state_manager,
            stats_manager,
            thinking_manager,
            task_processor,
            llm_engine,
            tool_registry,
            context_manager,
            sandbox,
            workflow_manager,
            message_bus,
            current_task: Arc::new(RwLock::new(None)),
        })
    }

    /// 获取Agent配置
    /// 
    /// # 返回
    /// 返回Agent配置的引用
    pub fn get_config(&self) -> &AgentConfig {
        &self.config
    }

    /// 获取当前状态
    /// 
    /// # 返回
    /// 返回当前Agent状态
    pub async fn get_state(&self) -> AgentState {
        self.state_manager.get_state().await
    }

    /// 获取统计信息
    /// 
    /// # 返回
    /// 返回Agent统计信息
    pub async fn get_stats(&self) -> AgentStats {
        self.stats_manager.get_stats().await
    }

    /// 暂停Agent
    /// 
    /// # 返回
    /// 如果暂停成功返回Ok(())，否则返回错误
    pub async fn pause(&self) -> Result<()> {
        self.state_manager.pause().await
    }

    /// 恢复Agent
    /// 
    /// # 返回
    /// 如果恢复成功返回Ok(())，否则返回错误
    pub async fn resume(&self) -> Result<()> {
        self.state_manager.resume().await
    }

    /// 停止Agent
    /// 
    /// # 返回
    /// 如果停止成功返回Ok(())，否则返回错误
    pub async fn stop(&self) -> Result<()> {
        self.state_manager.stop().await
    }

    /// 检查Agent是否可以接受新任务
    /// 
    /// # 返回
    /// 如果可以接受新任务返回true，否则返回false
    pub async fn can_accept_task(&self) -> bool {
        self.state_manager.can_accept_task().await
    }

    /// 处理任务
    /// 
    /// Agent处理任务的主要入口点
    /// 
    /// # 参数
    /// * `task` - 要处理的任务
    /// 
    /// # 返回
    /// 返回任务处理结果
    pub async fn process_task(&self, task: TaskRequest) -> Result<TaskResponse> {
        let start_time = std::time::Instant::now();

        // 检查是否可以接受任务
        if !self.can_accept_task().await {
            return Err(OrionError::AgentError(
                format!("Agent当前状态不能接受新任务: {:?}", self.get_state().await)
            ));
        }

        // 更新当前任务
        {
            let mut current_task = self.current_task.write().await;
            *current_task = Some(task.clone());
        }

        // 更新状态为思考中
        self.state_manager.set_state(AgentState::Thinking).await?;

        // 开始思考过程
        self.thinking_manager.start_thinking().await;

        // 添加任务到上下文（如果启用记忆）
        if self.config.enable_memory {
            if let Err(e) = self.add_task_to_context(&task).await {
                tracing::error!("添加任务到上下文失败: {}", e);
                // 重置状态并返回错误
                self.thinking_manager.end_thinking().await;
                if let Err(state_err) = self.state_manager.set_state(AgentState::Idle).await {
                    tracing::error!("重置Agent状态失败: {}", state_err);
                }
                {
                    let mut current_task = self.current_task.write().await;
                    *current_task = None;
                }
                return Err(e);
            }
        }

        // 处理任务
        let result = self.task_processor.process_task(&task).await;

        // 计算处理时间
        let processing_time_ms = start_time.elapsed().as_millis() as u64;

        // 创建响应
        let response = match result {
            Ok((content, response_type, tool_results, generated_files, metadata, token_usage)) => {
                TaskResponse {
                    task_id: task.id,
                    content,
                    response_type,
                    success: true,
                    error: None,
                    tool_results,
                    generated_files,
                    metadata,
                    processing_time_ms,
                    token_usage,
                    created_at: SystemTime::now(),
                }
            }
            Err(e) => {
                // 记录错误信息（不设置错误状态，稍后统一重置为Idle）
                tracing::error!("处理任务时发生错误: {}", e);

                TaskResponse {
                    task_id: task.id,
                    content: format!("处理任务时发生错误: {}", e),
                    response_type: ResponseType::Error,
                    success: false,
                    error: Some(e.to_string()),
                    tool_results: Vec::new(),
                    generated_files: Vec::new(),
                    metadata: HashMap::new(),
                    processing_time_ms,
                    token_usage: None,
                    created_at: SystemTime::now(),
                }
            }
        };

        // 添加响应到上下文（如果启用记忆）
        if self.config.enable_memory {
            self.add_response_to_context(&task, &response).await?;
        }

        // 更新统计信息
        self.stats_manager.update_task_stats(&response).await;

        // 结束思考过程
        self.thinking_manager.end_thinking().await;

        // 更新状态为空闲（无论成功还是失败都要重置状态，让用户能继续对话）
        if let Err(state_err) = self.state_manager.set_state(AgentState::Idle).await {
            tracing::error!("重置Agent状态为Idle失败: {}", state_err);
        }

        // 清除当前任务
        {
            let mut current_task = self.current_task.write().await;
            *current_task = None;
        }

        tracing::info!(
            "Agent '{}' 完成任务处理: {} ({}ms)",
            self.config.name,
            if response.success { "成功" } else { "失败" },
            processing_time_ms
        );

        Ok(response)
    }

    /// 流式处理任务
    /// 
    /// 提供流式响应的任务处理
    /// 
    /// # 参数
    /// * `task` - 要处理的任务
    /// 
    /// # 返回
    /// 返回流式响应的接收器
    pub async fn process_task_stream(
        &self,
        task: TaskRequest,
    ) -> Result<tokio::sync::mpsc::Receiver<Result<crate::llm::StreamChunk>>> {
        // 检查是否可以接受任务
        if !self.can_accept_task().await {
            return Err(OrionError::AgentError(
                format!("Agent当前状态不能接受新任务: {:?}", self.get_state().await)
            ));
        }

        // 更新当前任务
        {
            let mut current_task = self.current_task.write().await;
            *current_task = Some(task.clone());
        }

        // 更新状态为思考中
        self.state_manager.set_state(AgentState::Thinking).await?;

        // 开始思考过程
        self.thinking_manager.start_thinking().await;

        // 添加任务到上下文（如果启用记忆）
        if self.config.enable_memory {
            self.add_task_to_context(&task).await?;
        }

        // 根据任务类型处理流式响应
        match task.task_type {
            crate::agent::types::TaskType::Question 
            | crate::agent::types::TaskType::CodeGeneration 
            | crate::agent::types::TaskType::DocumentAnalysis 
            | crate::agent::types::TaskType::Custom(_) => {
                self.handle_question_task_stream(task).await
            }
            _ => {
                Err(OrionError::AgentError(
                    format!("任务类型 {:?} 不支持流式响应", task.task_type)
                ))
            }
        }
    }

    /// 处理流式问答任务
    ///
    /// 内部方法，处理支持流式响应的任务类型
    async fn handle_question_task_stream(
        &self,
        task: TaskRequest,
    ) -> Result<tokio::sync::mpsc::Receiver<Result<crate::llm::StreamChunk>>> {
        // 添加策略规划思考步骤
        self.thinking_manager.add_strategy_planning(
            "规划问答策略（流式）".to_string(),
            0.8,
        ).await;

        // 获取相关上下文 - 优化对话历史检索
        let context_entries = if self.config.enable_memory {
            // 首先获取最近的对话历史（按时间顺序）
            let mut recent_conversation = if let Some(session_id) = &task.session_id {
                let query = crate::context::ContextQuery {
                    session_id: Some(session_id.clone()),
                    query_text: None, // 不根据内容过滤，获取完整对话
                    entry_types: None,
                    tags: None,
                    min_relevance: None,
                    min_importance: None,
                    time_range: None,
                    limit: Some(20), // 增加对话历史数量
                    include_children: false,
                };
                let mut entries = self.context_manager.query_entries(query).await?;
                // 按时间顺序排序（从早到晚）
                entries.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                entries
            } else {
                Vec::new()
            };

            // 如果对话历史不足，再根据内容相关性补充
            if recent_conversation.len() < 5 {
                let relevance_query = crate::context::ContextQuery {
                    session_id: task.session_id.clone(),
                    query_text: Some(task.content.clone()),
                    entry_types: None,
                    tags: None,
                    min_relevance: Some(0.4), // 提高相关性阈值
                    min_importance: None,
                    time_range: None,
                    limit: Some(10),
                    include_children: false,
                };
                let relevant_entries = self.context_manager.query_entries(relevance_query).await?;
                
                // 合并并去重
                for entry in relevant_entries {
                    if !recent_conversation.iter().any(|e| e.id == entry.id) {
                        recent_conversation.push(entry);
                    }
                }
            }

            recent_conversation
        } else {
            Vec::new()
        };

        // 构建LLM请求
        let mut messages = vec![
            crate::llm::LlmMessage {
                role: crate::llm::MessageRole::System,
                content: self.config.system_prompt.clone(),
                metadata: HashMap::new(),
                timestamp: SystemTime::now(),
            },
        ];

        // 添加上下文信息 - 优化对话格式
        if !context_entries.is_empty() {
            // 构建结构化的对话历史
            let mut conversation_history = String::new();
            conversation_history.push_str("=== 对话历史 ===\n");
            
            for entry in &context_entries {
                match entry.entry_type {
                    crate::context::ContextEntryType::UserInput => {
                        conversation_history.push_str(&format!("用户: {}\n", entry.content));
                    },
                    crate::context::ContextEntryType::AgentResponse => {
                        conversation_history.push_str(&format!("助手: {}\n", entry.content));
                    },
                    _ => {
                        conversation_history.push_str(&format!("[系统] {}\n", entry.content));
                    }
                }
            }
            conversation_history.push_str("=== 对话历史结束 ===\n");
            
            // 添加对话上下文指导
            let context_instruction = format!(
                "{}{}",
                conversation_history,
                "请基于以上对话历史来理解当前用户的问题。如果这是一个游戏或有特定规则的对话，请严格按照之前建立的规则和模式来回应。保持对话的连贯性和一致性。"
            );

            messages.push(crate::llm::LlmMessage {
                role: crate::llm::MessageRole::System,
                content: context_instruction,
                metadata: HashMap::new(),
                timestamp: SystemTime::now(),
            });
        }

        // 添加用户问题
        messages.push(crate::llm::LlmMessage {
            role: crate::llm::MessageRole::User,
            content: task.content.clone(),
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        });

        // 获取可用工具
        let available_tools = self.task_processor.get_available_tools().await;

        let llm_request = crate::llm::LlmRequest {
            id: Uuid::new_v4(),
            messages,
            parameters: Some(crate::llm::ModelParameters {
                temperature: self.config.temperature,
                max_tokens: self.config.max_tokens.unwrap_or(4096),
                top_p: None,
                top_k: None,
                frequency_penalty: None,
                presence_penalty: None,
                stop_sequences: vec![],
            }),
            tools: available_tools,
            streaming: Some(true),  // 启用流式响应
            metadata: HashMap::new(),
        };

        // 调用LLM流式接口
        let mut stream_receiver = match self.llm_engine.stream(llm_request).await {
            Ok(receiver) => receiver,
            Err(e) => {
                // API调用失败时重置状态
                tracing::error!("LLM API调用失败，重置Agent状态: {}", e);
                
                // 结束思考过程
                self.thinking_manager.end_thinking().await;
                
                // 重置状态为空闲
                if let Err(state_err) = self.state_manager.set_state(AgentState::Idle).await {
                    tracing::error!("重置Agent状态失败: {}", state_err);
                }
                
                // 清除当前任务
                {
                    let mut current_task = self.current_task.write().await;
                    *current_task = None;
                }
                
                return Err(e);
            }
        };
        
        // 创建一个新的通道来包装原始流，添加状态管理
        let (tx, rx) = tokio::sync::mpsc::channel(32);
        
        // 克隆必要的引用用于异步任务
        let state_manager = self.state_manager.clone();
        let stats_manager = self.stats_manager.clone();
        let thinking_manager = self.thinking_manager.clone();
        let current_task = self.current_task.clone();
        let agent_name = self.config.name.clone();
        let task_id = task.id;
        let start_time = std::time::Instant::now();
        
        // 启动一个任务来处理流式响应，但只收集数据，不立即重置状态
        tokio::spawn(async move {
            let mut is_final = false;
            let mut content_chunks = Vec::new();
            
            while let Some(chunk_result) = stream_receiver.recv().await {
                match &chunk_result {
                    Ok(chunk) => {
                        // 收集内容用于上下文
                        if !chunk.delta.is_empty() {
                            content_chunks.push(chunk.delta.clone());
                        }
                        
                        if chunk.is_final {
                            is_final = true;
                        }
                    }
                    Err(e) => {
                        // 处理流式响应错误，执行完整的状态重置
                        tracing::error!("流式响应处理失败: {}", e);
                        
                        // 结束思考过程
                        thinking_manager.end_thinking().await;
                        
                        // 重置状态为空闲
                        if let Err(state_err) = state_manager.set_state(AgentState::Idle).await {
                            tracing::error!("重置Agent状态失败: {}", state_err);
                        }
                        
                        // 清除当前任务
                        {
                            let mut current_task_guard = current_task.write().await;
                            *current_task_guard = None;
                        }
                        
                        // 不再继续处理，直接退出循环
                        break;
                    }
                }
                
                // 转发消息到外部接收器
                if tx.send(chunk_result).await.is_err() {
                    // 接收器已关闭，执行状态清理
                    tracing::warn!("流式响应接收器已关闭，执行状态重置");
                    
                    // 结束思考过程
                    thinking_manager.end_thinking().await;
                    
                    // 重置状态为空闲
                    if let Err(state_err) = state_manager.set_state(AgentState::Idle).await {
                        tracing::error!("重置Agent状态失败: {}", state_err);
                    }
                    
                    // 清除当前任务
                    {
                        let mut current_task_guard = current_task.write().await;
                        *current_task_guard = None;
                    }
                    
                    break; // 接收器已关闭
                }
                
                // 如果是最终块，执行延迟清理
                if is_final {
                    // 启动一个延迟任务来处理状态重置
                    let state_manager_clone = state_manager.clone();
                    let stats_manager_clone = stats_manager.clone();
                    let thinking_manager_clone = thinking_manager.clone();
                    let current_task_clone = current_task.clone();
                    let agent_name_clone = agent_name.clone();
                    
                    tokio::spawn(async move {
                        // 更精确的等待时间估算
                        let total_content = content_chunks.join("");
                        let char_count = total_content.chars().count();
                        
                        // 基于实际打字机速度计算：30ms/字符 + 额外缓冲时间
                        // 对于长文本，给更多缓冲时间
                        let base_time = char_count as u64 * 30;
                        let buffer_time = if char_count > 100 {
                            1500  // 长文本给1.5秒缓冲
                        } else if char_count > 50 {
                            1000  // 中等文本给1秒缓冲
                        } else {
                            600   // 短文本给0.6秒缓冲
                        };
                        
                        let total_wait_time = base_time + buffer_time;
                        let max_wait_time = 5000; // 最大等待5秒
                        
                        let wait_duration = std::cmp::min(total_wait_time, max_wait_time);
                        
                        tracing::debug!(
                            "等待打字机完成: {}字符, {}ms基础时间, {}ms缓冲, 总等待{}ms",
                            char_count, base_time, buffer_time, wait_duration
                        );
                        
                        tokio::time::sleep(tokio::time::Duration::from_millis(wait_duration)).await;
                        
                        let processing_time_ms = start_time.elapsed().as_millis() as u64;
                        
                        // 结束思考过程
                        thinking_manager_clone.end_thinking().await;
                        
                        // 更新状态为空闲
                        if let Err(e) = state_manager_clone.set_state(AgentState::Idle).await {
                            tracing::error!("重置Agent状态失败: {}", e);
                        }
                        
                        // 清除当前任务
                        {
                            let mut current_task_guard = current_task_clone.write().await;
                            *current_task_guard = None;
                        }
                        
                        // 更新统计信息 - 创建一个简化的TaskResponse
                        let estimated_tokens = if !total_content.is_empty() {
                            // 简单估算token使用（每个字符约0.75个token）
                            let total_tokens = (total_content.len() as f64 * 0.75) as u32;
                            Some(crate::llm::TokenUsage {
                                input_tokens: 0,
                                output_tokens: total_tokens,
                                total_tokens: total_tokens,
                            })
                        } else {
                            None
                        };
                        
                        let mock_response = crate::agent::types::TaskResponse {
                            task_id,
                            content: total_content,
                            response_type: crate::agent::types::ResponseType::Text,
                            success: true,
                            error: None,
                            tool_results: Vec::new(),
                            generated_files: Vec::new(),
                            metadata: std::collections::HashMap::new(),
                            processing_time_ms,
                            token_usage: estimated_tokens,
                            created_at: SystemTime::now(),
                        };
                        stats_manager_clone.update_task_stats(&mock_response).await;
                        
                        tracing::debug!(
                            "Agent '{}' 完成流式任务处理: 成功 ({}ms)",
                            agent_name_clone,
                            processing_time_ms
                        );
                    });
                    
                    break;
                }
            }
            
            // 确保无论如何都要清理状态，特别是当循环因错误提前退出时
            if !is_final {
                // 流式处理非正常结束，立即重置状态
                tracing::warn!("流式处理非正常结束，立即重置Agent状态");
                
                // 结束思考过程
                thinking_manager.end_thinking().await;
                
                // 重置状态为空闲
                if let Err(state_err) = state_manager.set_state(AgentState::Idle).await {
                    tracing::error!("重置Agent状态失败: {}", state_err);
                }
                
                // 清除当前任务
                {
                    let mut current_task_guard = current_task.write().await;
                    *current_task_guard = None;
                }
                
                tracing::debug!(
                    "Agent '{}' 流式任务处理异常结束",
                    agent_name
                );
            }
        });

        Ok(rx)
    }

    /// 添加任务到上下文
    ///
    /// 将任务信息添加到上下文管理器中
    async fn add_task_to_context(&self, task: &TaskRequest) -> Result<()> {
        let context_entry = ContextEntry {
            id: Uuid::new_v4(),
            session_id: task.session_id.clone().unwrap_or_else(|| "default".to_string()),
            entry_type: ContextEntryType::UserInput,
            content: task.content.clone(),
            metadata: task.context.clone(),
            relevance_score: 1.0,
            importance: match task.priority {
                crate::agent::types::TaskPriority::Urgent => ImportanceLevel::Critical,
                crate::agent::types::TaskPriority::High => ImportanceLevel::High,
                crate::agent::types::TaskPriority::Normal => ImportanceLevel::Normal,
                crate::agent::types::TaskPriority::Low => ImportanceLevel::Low,
            },
            created_at: task.created_at,
            last_accessed: SystemTime::now(),
            access_count: 0,
            tags: vec![format!("task_type:{:?}", task.task_type)],
            parent_id: None,
            children_ids: Vec::new(),
        };

        self.context_manager.add_entry(context_entry).await?;
        Ok(())
    }

    /// 添加响应到上下文
    ///
    /// 将任务响应添加到上下文管理器中
    async fn add_response_to_context(&self, task: &TaskRequest, response: &TaskResponse) -> Result<()> {
        let context_entry = ContextEntry {
            id: Uuid::new_v4(),
            session_id: task.session_id.clone().unwrap_or_else(|| "default".to_string()),
            entry_type: ContextEntryType::AgentResponse,
            content: response.content.clone(),
            metadata: response.metadata.clone(),
            relevance_score: if response.success { 1.0 } else { 0.5 },
            importance: ImportanceLevel::Normal,
            created_at: response.created_at,
            last_accessed: SystemTime::now(),
            access_count: 0,
            tags: vec![format!("response_type:{:?}", response.response_type)],
            parent_id: None,
            children_ids: Vec::new(),
        };

        self.context_manager.add_entry(context_entry).await?;
        Ok(())
    }

    /// 获取当前任务
    ///
    /// # 返回
    /// 返回当前正在处理的任务（如果有）
    pub async fn get_current_task(&self) -> Option<TaskRequest> {
        self.current_task.read().await.clone()
    }

    /// 获取思考过程摘要
    ///
    /// # 返回
    /// 返回当前思考过程的摘要
    pub async fn get_thinking_summary(&self) -> String {
        self.thinking_manager.get_summary().await
    }

    /// 生成状态报告
    ///
    /// # 返回
    /// 返回Agent的详细状态报告
    pub async fn generate_status_report(&self) -> String {
        let _state = self.get_state().await;
        let current_task = self.get_current_task().await;
        let thinking_summary = self.get_thinking_summary().await;
        let state_description = self.state_manager.get_state_description().await;

        format!(
            "Agent '{}' 状态报告\n\
            ==================\n\
            当前状态: {}\n\
            当前任务: {}\n\
            思考过程: {}\n\
            \n\
            {}",
            self.config.name,
            state_description,
            if let Some(task) = current_task {
                format!("正在处理: {}", task.content)
            } else {
                "无".to_string()
            },
            thinking_summary,
            self.stats_manager.generate_report().await
        )
    }
}

impl Clone for Agent {
    /// 克隆Agent实例
    ///
    /// 注意：克隆的实例共享相同的状态和资源
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            state_manager: self.state_manager.clone(),
            stats_manager: self.stats_manager.clone(),
            thinking_manager: self.thinking_manager.clone(),
            task_processor: TaskProcessor::new(
                self.config.clone(),
                self.llm_engine.clone(),
                self.tool_registry.clone(),
                self.context_manager.clone(),
                self.workflow_manager.clone(),
                self.thinking_manager.clone(),
            ),
            llm_engine: self.llm_engine.clone(),
            tool_registry: self.tool_registry.clone(),
            context_manager: self.context_manager.clone(),
            sandbox: self.sandbox.clone(),
            workflow_manager: self.workflow_manager.clone(),
            message_bus: self.message_bus.clone(),
            current_task: self.current_task.clone(),
        }
    }
}
