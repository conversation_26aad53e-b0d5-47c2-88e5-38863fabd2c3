# Orion Development Environment - Fixed Version
# PowerShell Profile Configuration

$ORION_ROOT = "E:\Orion"

# Orion development command with fixed path handling
function orion {
    param(
        [Parameter(ValueFromRemainingArguments = $true)]
        [string[]]$Arguments
    )
    
    # Use the fixed version of the script
    $scriptPath = Join-Path $ORION_ROOT "scripts\orion-dev-fixed.bat"
    
    # If fixed script doesn't exist, fall back to original
    if (-not (Test-Path $scriptPath)) {
        $scriptPath = Join-Path $ORION_ROOT "scripts\orion-dev.bat"
    }
    
    # Set working directory to Orion root to ensure config file is found
    Push-Location $ORION_ROOT
    try {
        & $scriptPath @Arguments
    }
    finally {
        Pop-Location
    }
}

# Quick navigation to Orion project
function cdo {
    Set-Location $ORION_ROOT
}

# Clean cache function
function orion-clean {
    $cacheDir = Join-Path $ORION_ROOT ".dev-cache"
    $targetDir = Join-Path $ORION_ROOT "target"
    
    if (Test-Path $cacheDir) { 
        Remove-Item -Recurse -Force $cacheDir 
        Write-Host "Dev cache cleaned" -ForegroundColor Yellow
    }
    if (Test-Path $targetDir) { 
        Remove-Item -Recurse -Force $targetDir 
        Write-Host "Target directory cleaned" -ForegroundColor Yellow
    }
    
    Write-Host "Cache cleaning completed" -ForegroundColor Green
}

# Create config command
function orion-init {
    Push-Location $ORION_ROOT
    try {
        & "target\debug\orion.exe" config init
        Write-Host "Config initialized in $ORION_ROOT" -ForegroundColor Green
    }
    finally {
        Pop-Location
    }
}

Write-Host "Orion development environment loaded (Fixed Version)" -ForegroundColor Green
Write-Host "Commands:" -ForegroundColor Cyan
Write-Host "  orion        - Run Orion (auto-compile, fixed paths)" -ForegroundColor White
Write-Host "  cdo          - Navigate to project directory" -ForegroundColor White
Write-Host "  orion-clean  - Clean cache and target" -ForegroundColor White
Write-Host "  orion-init   - Initialize config file" -ForegroundColor White