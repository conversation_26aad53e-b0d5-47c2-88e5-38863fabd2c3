//! # 工作流错误模块
//!
//! 包含所有与工作流执行相关的错误类型。
//! 涵盖工作流定义、步骤执行、状态管理、依赖处理等工作流操作中的错误。

use thiserror::Error;

/// 工作流相关错误枚举
/// 
/// 定义了与工作流系统相关的各种错误情况。
/// 包括工作流执行失败、步骤错误、状态异常等问题。
#[derive(Error, Debug, Clone)]
pub enum WorkflowError {
    /// 通用工作流错误
    /// 
    /// 用于不属于其他特定类别的工作流相关错误。
    #[error("工作流错误: {message}")]
    ExecutionError {
        /// 错误详细信息
        message: String,
    },

    /// 工作流步骤执行错误
    /// 
    /// 当工作流中的某个步骤执行失败时抛出。
    #[error("工作流步骤执行失败: 步骤 '{step_name}', 错误: {message}")]
    StepExecutionFailed {
        /// 步骤名称
        step_name: String,
        /// 错误信息
        message: String,
    },

    /// 无效的工作流状态错误
    /// 
    /// 当工作流处于不正确的状态时抛出。
    #[error("无效的工作流状态: 当前状态 '{current_state}', 期望状态 '{expected_state}'")]
    InvalidState {
        /// 当前状态
        current_state: String,
        /// 期望状态
        expected_state: String,
    },

    /// 工作流定义错误
    /// 
    /// 当工作流定义有问题时抛出。
    #[error("工作流定义错误: {message}")]
    DefinitionError {
        /// 错误详细信息
        message: String,
    },
}

impl WorkflowError {
    /// 创建通用执行错误
    pub fn execution_error(message: impl Into<String>) -> Self {
        Self::ExecutionError {
            message: message.into(),
        }
    }

    /// 创建步骤执行失败错误
    pub fn step_execution_failed(
        step_name: impl Into<String>,
        message: impl Into<String>,
    ) -> Self {
        Self::StepExecutionFailed {
            step_name: step_name.into(),
            message: message.into(),
        }
    }

    /// 创建无效状态错误
    pub fn invalid_state(
        current_state: impl Into<String>,
        expected_state: impl Into<String>,
    ) -> Self {
        Self::InvalidState {
            current_state: current_state.into(),
            expected_state: expected_state.into(),
        }
    }

    /// 创建定义错误
    pub fn definition_error(message: impl Into<String>) -> Self {
        Self::DefinitionError {
            message: message.into(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_workflow_error_creation() {
        let error = WorkflowError::execution_error("测试工作流错误");
        assert!(error.to_string().contains("工作流错误"));
        assert!(error.to_string().contains("测试工作流错误"));
    }

    #[test]
    fn test_step_execution_failed() {
        let error = WorkflowError::step_execution_failed("步骤1", "执行失败");
        assert!(error.to_string().contains("工作流步骤执行失败"));
        assert!(error.to_string().contains("步骤1"));
        assert!(error.to_string().contains("执行失败"));
    }

    #[test]
    fn test_invalid_state() {
        let error = WorkflowError::invalid_state("运行中", "已停止");
        assert!(error.to_string().contains("无效的工作流状态"));
        assert!(error.to_string().contains("运行中"));
        assert!(error.to_string().contains("已停止"));
    }

    #[test]
    fn test_definition_error() {
        let error = WorkflowError::definition_error("缺少必需字段");
        assert!(error.to_string().contains("工作流定义错误"));
        assert!(error.to_string().contains("缺少必需字段"));
    }
}
