//! # 消息系统测试模块
//!
//! 本模块包含消息系统各个组件的单元测试和集成测试。
//! 测试覆盖了消息创建、总线操作、事件循环处理、统计收集等核心功能。

use super::*;
use std::collections::HashMap;
use std::time::Duration;
use tokio::time::sleep;

/// 测试消息创建和基本属性
#[tokio::test]
async fn test_message_creation() {
    let message = Message::new(
        "agent1".to_string(),
        "agent2".to_string(),
        MessagePayload::Text("Hello".to_string()),
    );
    
    assert_eq!(message.from, "agent1");
    assert_eq!(message.to, "agent2");
    assert_eq!(message.priority, MessagePriority::Normal);
    assert!(!message.is_expired());
    assert!(message.metadata.is_empty());
    assert!(message.session_id.is_none());
}

/// 测试消息构建器模式
#[tokio::test]
async fn test_message_builder_pattern() {
    let message = Message::new(
        "agent1".to_string(),
        "agent2".to_string(),
        MessagePayload::Text("Hello".to_string()),
    )
    .with_priority(MessagePriority::High)
    .with_session_id("session_123".to_string())
    .with_metadata("key".to_string(), "value".to_string())
    .with_expiry(Duration::from_secs(60));
    
    assert_eq!(message.priority, MessagePriority::High);
    assert_eq!(message.session_id, Some("session_123".to_string()));
    assert_eq!(message.metadata.get("key"), Some(&"value".to_string()));
    assert!(message.expires_at.is_some());
}

/// 测试消息过期功能
#[tokio::test]
async fn test_message_expiry() {
    let message = Message::new(
        "agent1".to_string(),
        "agent2".to_string(),
        MessagePayload::Text("Hello".to_string()),
    )
    .with_expiry(Duration::from_millis(10));
    
    assert!(!message.is_expired());
    
    sleep(Duration::from_millis(20)).await;
    assert!(message.is_expired());
}

/// 测试 Steering 命令消息创建
#[tokio::test]
async fn test_steering_message() {
    let message = Message::steering_command(
        "user".to_string(),
        "agent".to_string(),
        "pause".to_string(),
        "workflow".to_string(),
        HashMap::new(),
    );
    
    assert_eq!(message.priority, MessagePriority::High);
    
    if let MessagePayload::SteeringCommand { command, target, .. } = message.payload {
        assert_eq!(command, "pause");
        assert_eq!(target, "workflow");
    } else {
        panic!("Expected SteeringCommand payload");
    }
}

/// 测试消息总线基本功能
#[tokio::test]
async fn test_message_bus_basic() {
    let bus = MessageBus::new();
    bus.start().await.unwrap();
    
    let message = Message::new(
        "agent1".to_string(),
        "agent2".to_string(),
        MessagePayload::Text("Hello".to_string()),
    );
    
    bus.send_message(message).await.unwrap();
    
    let received = bus.receive_message(Duration::from_millis(100)).await.unwrap();
    assert_eq!(received.from, "agent1");
    assert_eq!(received.to, "agent2");
    
    bus.stop().await.unwrap();
}

/// 测试 Agent 注册和直接路由
#[tokio::test]
async fn test_agent_registration() {
    let bus = MessageBus::new();
    bus.start().await.unwrap();
    
    let mut receiver = bus.register_agent("test_agent".to_string()).await;
    assert!(bus.is_agent_registered(&"test_agent".to_string()).await);
    
    let message = Message::new(
        "sender".to_string(),
        "test_agent".to_string(),
        MessagePayload::Text("Direct message".to_string()),
    );
    
    bus.send_message(message).await.unwrap();
    
    let received = receiver.recv().await.unwrap();
    assert_eq!(received.to, "test_agent");
    
    bus.unregister_agent(&"test_agent".to_string()).await.unwrap();
    assert!(!bus.is_agent_registered(&"test_agent".to_string()).await);
    
    bus.stop().await.unwrap();
}

/// 测试消息广播功能
#[tokio::test]
async fn test_message_broadcast() {
    let bus = MessageBus::new();
    bus.start().await.unwrap();
    
    let mut receiver1 = bus.register_agent("agent1".to_string()).await;
    let mut receiver2 = bus.register_agent("agent2".to_string()).await;
    
    let message = Message::new(
        "broadcaster".to_string(),
        "".to_string(), // 广播时会被覆盖
        MessagePayload::Text("Broadcast message".to_string()),
    );
    
    bus.broadcast_message(message).await.unwrap();
    
    let received1 = receiver1.recv().await.unwrap();
    let received2 = receiver2.recv().await.unwrap();
    
    assert_eq!(received1.to, "agent1");
    assert_eq!(received2.to, "agent2");
    assert_ne!(received1.id, received2.id); // 每个接收者应该有不同的消息 ID
    
    bus.stop().await.unwrap();
}

/// 测试统计信息收集
#[tokio::test]
async fn test_stats_collection() {
    let stats_manager = Arc::new(StatsManager::new(100));
    let bus = MessageBus::with_stats_manager(stats_manager.clone());
    bus.start().await.unwrap();
    
    let message = Message::new(
        "sender".to_string(),
        "receiver".to_string(),
        MessagePayload::Text("Test message".to_string()),
    );
    
    bus.send_message(message).await.unwrap();
    let _received = bus.receive_message(Duration::from_millis(100)).await.unwrap();
    
    let stats = bus.get_stats().await;
    assert_eq!(stats.messages_sent, 1);
    assert_eq!(stats.messages_received, 1);
    assert_eq!(stats.messages_dropped, 0);
    
    bus.stop().await.unwrap();
}

/// 测试回声处理器
#[tokio::test]
async fn test_echo_handler() {
    let handler = EchoHandler::new("test_echo".to_string());
    
    let message = Message::new(
        "sender".to_string(),
        "receiver".to_string(),
        MessagePayload::Text("Hello".to_string()),
    );
    
    let response = handler.handle_message(message).await.unwrap();
    assert!(response.is_some());
    
    let response = response.unwrap();
    assert_eq!(response.from, "receiver");
    assert_eq!(response.to, "sender");
    
    if let MessagePayload::Text(text) = response.payload {
        assert_eq!(text, "Echo: Hello");
    } else {
        panic!("Expected Text payload");
    }
}

/// 测试日志处理器
#[tokio::test]
async fn test_logging_handler() {
    let handler = LoggingHandler::new("test_logger".to_string(), "info".to_string());
    
    let message = Message::new(
        "sender".to_string(),
        "receiver".to_string(),
        MessagePayload::Text("Test log".to_string()),
    );
    
    let response = handler.handle_message(message).await.unwrap();
    assert!(response.is_none()); // 日志处理器不返回响应
}

/// 测试事件循环基本功能
#[tokio::test]
async fn test_event_loop_basic() {
    let bus = Arc::new(MessageBus::new());
    bus.start().await.unwrap();
    
    let event_loop = EventLoop::new(bus.clone());
    event_loop.register_handler(Box::new(EchoHandler::new("echo".to_string()))).await;
    event_loop.start().await.unwrap();
    
    let message = Message::new(
        "sender".to_string(),
        "receiver".to_string(),
        MessagePayload::Text("Hello".to_string()),
    );
    
    // 直接处理单个消息进行测试
    event_loop.process_single_message(message).await.unwrap();
    
    event_loop.stop().await.unwrap();
    bus.stop().await.unwrap();
}

/// 测试过滤处理器
#[tokio::test]
async fn test_filter_handler() {
    let echo_handler = Box::new(EchoHandler::new("echo".to_string()));
    let filter_handler = FilterHandler::new("filter".to_string(), echo_handler)
        .with_min_priority(MessagePriority::High);
    
    // 高优先级消息应该通过
    let high_priority_message = Message::new(
        "sender".to_string(),
        "receiver".to_string(),
        MessagePayload::Text("High priority".to_string()),
    )
    .with_priority(MessagePriority::High);
    
    let response = filter_handler.handle_message(high_priority_message).await.unwrap();
    assert!(response.is_some());
    
    // 低优先级消息应该被过滤
    let low_priority_message = Message::new(
        "sender".to_string(),
        "receiver".to_string(),
        MessagePayload::Text("Low priority".to_string()),
    )
    .with_priority(MessagePriority::Low);
    
    let response = filter_handler.handle_message(low_priority_message).await.unwrap();
    assert!(response.is_none());
}

/// 测试消息系统构建器
#[tokio::test]
async fn test_message_system_builder() {
    let (bus, event_loop) = MessageSystemBuilder::new()
        .with_handler(Box::new(EchoHandler::new("echo".to_string())))
        .with_handler(Box::new(LoggingHandler::new("logger".to_string(), "debug".to_string())))
        .with_receive_timeout(Duration::from_millis(50))
        .build()
        .await;
    
    bus.start().await.unwrap();
    event_loop.start().await.unwrap();
    
    let handlers = event_loop.get_registered_handlers().await;
    assert_eq!(handlers.len(), 2);
    assert!(handlers.contains(&"echo".to_string()));
    assert!(handlers.contains(&"logger".to_string()));
    
    event_loop.stop().await.unwrap();
    bus.stop().await.unwrap();
}

/// 测试默认消息系统创建
#[tokio::test]
async fn test_create_default_message_system() {
    let (bus, event_loop) = create_default_message_system().await;
    
    bus.start().await.unwrap();
    event_loop.start().await.unwrap();
    
    let handlers = event_loop.get_registered_handlers().await;
    assert!(!handlers.is_empty());
    
    event_loop.stop().await.unwrap();
    bus.stop().await.unwrap();
}

/// 测试测试用消息系统创建
#[tokio::test]
async fn test_create_test_message_system() {
    let (bus, event_loop) = create_test_message_system().await;
    
    bus.start().await.unwrap();
    event_loop.start().await.unwrap();
    
    let handlers = event_loop.get_registered_handlers().await;
    assert_eq!(handlers.len(), 2);
    
    event_loop.stop().await.unwrap();
    bus.stop().await.unwrap();
}
