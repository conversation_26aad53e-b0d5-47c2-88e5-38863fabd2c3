//! # 资源监控模块
//!
//! 提供进程资源使用监控功能，包括内存、CPU 使用率等指标的实时监控。
//! 支持跨平台的资源监控实现。

// ResourceMonitor 在本模块中定义，不需要从 types 导入
use std::time::{Duration, Instant};
use tokio::time::sleep;
use tracing;

/// 进程监控结果
/// 
/// 包含进程执行期间的资源使用统计信息
#[derive(Debug, Default, Clone)]
pub struct ProcessMonitorResult {
    /// 峰值内存使用（MB）
    pub peak_memory_mb: u64,
    /// 平均 CPU 使用率（百分比）
    pub cpu_usage_percent: f32,
    /// 监控持续时间（毫秒）
    pub monitor_duration_ms: u64,
    /// 采样次数
    pub sample_count: u32,
}

impl ProcessMonitorResult {
    /// 创建新的监控结果
    pub fn new() -> Self {
        Self::default()
    }

    /// 更新内存使用
    pub fn update_memory(&mut self, memory_mb: u64) {
        if memory_mb > self.peak_memory_mb {
            self.peak_memory_mb = memory_mb;
        }
    }

    /// 更新 CPU 使用率（计算平均值）
    pub fn update_cpu(&mut self, cpu_percent: f32) {
        let total_cpu = self.cpu_usage_percent * self.sample_count as f32;
        self.sample_count += 1;
        self.cpu_usage_percent = (total_cpu + cpu_percent) / self.sample_count as f32;
    }

    /// 设置监控持续时间
    pub fn set_duration(&mut self, duration: Duration) {
        self.monitor_duration_ms = duration.as_millis() as u64;
    }
}

/// 资源监控器
/// 
/// 提供进程资源监控的核心功能
pub struct ResourceMonitor {
    /// 监控间隔（毫秒）
    monitor_interval_ms: u64,
    /// 是否启用详细日志
    verbose_logging: bool,
}

impl ResourceMonitor {
    /// 创建新的资源监控器
    pub fn new() -> Self {
        Self {
            monitor_interval_ms: 100, // 默认100ms间隔
            verbose_logging: false,
        }
    }

    /// 设置监控间隔
    pub fn with_interval(mut self, interval_ms: u64) -> Self {
        self.monitor_interval_ms = interval_ms;
        self
    }

    /// 启用详细日志
    pub fn with_verbose_logging(mut self, verbose: bool) -> Self {
        self.verbose_logging = verbose;
        self
    }

    /// 监控指定进程的资源使用
    /// 
    /// 持续监控直到进程结束或被取消
    pub async fn monitor_process(process_id: Option<u32>) -> ProcessMonitorResult {
        let mut result = ProcessMonitorResult::new();
        let start_time = Instant::now();

        if let Some(pid) = process_id {
            tracing::debug!("开始监控进程: {}", pid);

            loop {
                // 检查进程是否还在运行
                if !Self::is_process_running(pid) {
                    break;
                }

                // 获取资源使用情况
                if let Ok(memory_mb) = Self::get_process_memory(pid) {
                    result.update_memory(memory_mb);
                }

                if let Ok(cpu_percent) = Self::get_process_cpu(pid) {
                    result.update_cpu(cpu_percent);
                }

                // 等待下一次采样
                sleep(Duration::from_millis(100)).await;
            }

            tracing::debug!("进程监控结束: {}", pid);
        } else {
            tracing::warn!("无效的进程ID，跳过监控");
        }

        result.set_duration(start_time.elapsed());
        result
    }

    /// 检查进程是否正在运行
    fn is_process_running(pid: u32) -> bool {
        #[cfg(target_os = "windows")]
        {
            Self::is_process_running_windows(pid)
        }

        #[cfg(unix)]
        {
            Self::is_process_running_unix(pid)
        }

        #[cfg(not(any(target_os = "windows", unix)))]
        {
            // 其他平台的简化实现
            true
        }
    }

    /// Windows 平台检查进程是否运行
    #[cfg(target_os = "windows")]
    fn is_process_running_windows(pid: u32) -> bool {
        use std::process::Command;
        
        let output = Command::new("tasklist")
            .args(&["/FI", &format!("PID eq {}", pid), "/FO", "CSV"])
            .output();

        if let Ok(output) = output {
            let output_str = String::from_utf8_lossy(&output.stdout);
            output_str.lines().count() > 1 // 第一行是标题，第二行是进程信息
        } else {
            false
        }
    }

    /// Unix 平台检查进程是否运行
    #[cfg(unix)]
    fn is_process_running_unix(pid: u32) -> bool {
        use std::fs;
        
        fs::metadata(format!("/proc/{}", pid)).is_ok()
    }

    /// 获取进程内存使用（MB）
    fn get_process_memory(pid: u32) -> Result<u64, String> {
        #[cfg(target_os = "windows")]
        {
            Self::get_process_memory_windows(pid)
        }

        #[cfg(unix)]
        {
            Self::get_process_memory_unix(pid)
        }

        #[cfg(not(any(target_os = "windows", unix)))]
        {
            // 其他平台返回默认值
            Ok(0)
        }
    }

    /// Windows 平台获取进程内存使用
    #[cfg(target_os = "windows")]
    fn get_process_memory_windows(pid: u32) -> Result<u64, String> {
        use std::process::Command;

        let output = Command::new("tasklist")
            .args(&["/FI", &format!("PID eq {}", pid), "/FO", "CSV"])
            .output()
            .map_err(|e| format!("执行tasklist失败: {}", e))?;

        let output_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = output_str.lines().collect();

        if lines.len() > 1 {
            // 解析CSV格式的输出
            let fields: Vec<&str> = lines[1].split(',').collect();
            if fields.len() > 4 {
                let memory_str = fields[4].trim_matches('"').replace(",", "");
                if let Some(memory_kb_str) = memory_str.strip_suffix(" K") {
                    if let Ok(memory_kb) = memory_kb_str.parse::<u64>() {
                        return Ok(memory_kb / 1024); // 转换为MB
                    }
                }
            }
        }

        Err("无法解析内存使用信息".to_string())
    }

    /// Unix 平台获取进程内存使用
    #[cfg(unix)]
    fn get_process_memory_unix(pid: u32) -> Result<u64, String> {
        use std::fs;

        let status_path = format!("/proc/{}/status", pid);
        let status_content = fs::read_to_string(status_path)
            .map_err(|e| format!("读取进程状态失败: {}", e))?;

        for line in status_content.lines() {
            if line.starts_with("VmRSS:") {
                let parts: Vec<&str> = line.split_whitespace().collect();
                if parts.len() >= 2 {
                    if let Ok(memory_kb) = parts[1].parse::<u64>() {
                        return Ok(memory_kb / 1024); // 转换为MB
                    }
                }
            }
        }

        Err("无法找到内存使用信息".to_string())
    }

    /// 获取进程 CPU 使用率（百分比）
    fn get_process_cpu(pid: u32) -> Result<f32, String> {
        #[cfg(target_os = "windows")]
        {
            Self::get_process_cpu_windows(pid)
        }

        #[cfg(unix)]
        {
            Self::get_process_cpu_unix(pid)
        }

        #[cfg(not(any(target_os = "windows", unix)))]
        {
            // 其他平台返回默认值
            Ok(0.0)
        }
    }

    /// Windows 平台获取进程 CPU 使用率
    #[cfg(target_os = "windows")]
    fn get_process_cpu_windows(_pid: u32) -> Result<f32, String> {
        // Windows 平台的 CPU 使用率获取比较复杂，这里提供简化实现
        // 实际应用中可以使用 WMI 或 Performance Counters
        Ok(0.0)
    }

    /// Unix 平台获取进程 CPU 使用率
    #[cfg(unix)]
    fn get_process_cpu_unix(pid: u32) -> Result<f32, String> {
        use std::fs;

        let stat_path = format!("/proc/{}/stat", pid);
        let stat_content = fs::read_to_string(stat_path)
            .map_err(|e| format!("读取进程统计失败: {}", e))?;

        let fields: Vec<&str> = stat_content.split_whitespace().collect();
        if fields.len() > 15 {
            // 简化的 CPU 使用率计算
            // 实际实现需要计算时间差和系统总 CPU 时间
            let utime: u64 = fields[13].parse().unwrap_or(0);
            let stime: u64 = fields[14].parse().unwrap_or(0);
            let total_time = utime + stime;
            
            // 这里返回一个简化的值，实际应用中需要更复杂的计算
            Ok((total_time % 100) as f32)
        } else {
            Err("无法解析进程统计信息".to_string())
        }
    }

    /// 获取系统资源使用概览
    pub async fn get_system_overview() -> SystemResourceOverview {
        SystemResourceOverview {
            total_memory_mb: Self::get_total_memory().unwrap_or(0),
            available_memory_mb: Self::get_available_memory().unwrap_or(0),
            cpu_usage_percent: Self::get_system_cpu_usage().unwrap_or(0.0),
            active_processes: Self::get_active_process_count().unwrap_or(0),
        }
    }

    /// 获取系统总内存
    fn get_total_memory() -> Result<u64, String> {
        // 简化实现，实际应用中应该使用系统API
        Ok(8192) // 假设8GB
    }

    /// 获取可用内存
    fn get_available_memory() -> Result<u64, String> {
        // 简化实现
        Ok(4096) // 假设4GB可用
    }

    /// 获取系统 CPU 使用率
    fn get_system_cpu_usage() -> Result<f32, String> {
        // 简化实现
        Ok(25.0) // 假设25%使用率
    }

    /// 获取活动进程数量
    fn get_active_process_count() -> Result<u32, String> {
        // 简化实现
        Ok(150) // 假设150个进程
    }
}

impl Default for ResourceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

/// 系统资源概览
/// 
/// 提供系统整体资源使用情况的快照
#[derive(Debug, Clone)]
pub struct SystemResourceOverview {
    /// 系统总内存（MB）
    pub total_memory_mb: u64,
    /// 可用内存（MB）
    pub available_memory_mb: u64,
    /// 系统 CPU 使用率（百分比）
    pub cpu_usage_percent: f32,
    /// 活动进程数量
    pub active_processes: u32,
}

impl SystemResourceOverview {
    /// 获取内存使用率
    pub fn memory_usage_percent(&self) -> f32 {
        if self.total_memory_mb > 0 {
            ((self.total_memory_mb - self.available_memory_mb) as f32 / self.total_memory_mb as f32) * 100.0
        } else {
            0.0
        }
    }

    /// 检查系统资源是否充足
    pub fn has_sufficient_resources(&self, required_memory_mb: u64) -> bool {
        self.available_memory_mb >= required_memory_mb && self.cpu_usage_percent < 90.0
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_monitor_result_creation() {
        let mut result = ProcessMonitorResult::new();
        assert_eq!(result.peak_memory_mb, 0);
        assert_eq!(result.cpu_usage_percent, 0.0);

        result.update_memory(100);
        result.update_cpu(50.0);

        assert_eq!(result.peak_memory_mb, 100);
        assert_eq!(result.cpu_usage_percent, 50.0);
    }

    #[test]
    fn test_resource_monitor_creation() {
        let monitor = ResourceMonitor::new();
        assert_eq!(monitor.monitor_interval_ms, 100);
        assert!(!monitor.verbose_logging);

        let monitor = ResourceMonitor::new()
            .with_interval(200)
            .with_verbose_logging(true);
        assert_eq!(monitor.monitor_interval_ms, 200);
        assert!(monitor.verbose_logging);
    }

    #[tokio::test]
    async fn test_system_overview() {
        let overview = ResourceMonitor::get_system_overview().await;
        assert!(overview.total_memory_mb > 0);
        assert!(overview.cpu_usage_percent >= 0.0);
    }
}
