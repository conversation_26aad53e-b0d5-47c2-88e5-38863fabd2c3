//! # LLM 核心类型定义
//!
//! 定义 LLM 系统中使用的所有核心数据结构，包括消息、请求、响应等。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use uuid::Uuid;

/// LLM 模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    /// 模型名称
    pub name: String,
    /// 提供商（如 "openai", "anthropic", "local"）
    pub provider: String,
    /// API 端点
    pub endpoint: Option<String>,
    /// API 密钥
    pub api_key: Option<String>,
    /// 模型参数
    pub parameters: ModelParameters,
    /// 请求超时时间（秒）
    pub timeout_seconds: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 是否启用流式响应
    pub streaming: bool,
}

/// 模型参数
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelParameters {
    /// 温度参数（0.0-2.0）
    pub temperature: f32,
    /// 最大输出 token 数
    pub max_tokens: u32,
    /// Top-p 采样参数
    pub top_p: Option<f32>,
    /// Top-k 采样参数
    pub top_k: Option<u32>,
    /// 频率惩罚
    pub frequency_penalty: Option<f32>,
    /// 存在惩罚
    pub presence_penalty: Option<f32>,
    /// 停止词列表
    pub stop_sequences: Vec<String>,
}

impl Default for ModelParameters {
    fn default() -> Self {
        Self {
            temperature: 0.7,
            max_tokens: 4096,
            top_p: None,
            top_k: None,
            frequency_penalty: None,
            presence_penalty: None,
            stop_sequences: Vec::new(),
        }
    }
}

/// 消息角色
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum MessageRole {
    /// 系统消息
    System,
    /// 用户消息
    User,
    /// 助手消息
    Assistant,
    /// 工具调用消息
    Tool,
}

/// LLM 消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmMessage {
    /// 消息角色
    pub role: MessageRole,
    /// 消息内容
    pub content: String,
    /// 消息元数据
    pub metadata: HashMap<String, serde_json::Value>,
    /// 消息时间戳
    pub timestamp: SystemTime,
}

impl LlmMessage {
    /// 创建系统消息
    pub fn system(content: impl Into<String>) -> Self {
        Self {
            role: MessageRole::System,
            content: content.into(),
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        }
    }
    
    /// 创建用户消息
    pub fn user(content: impl Into<String>) -> Self {
        Self {
            role: MessageRole::User,
            content: content.into(),
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        }
    }
    
    /// 创建助手消息
    pub fn assistant(content: impl Into<String>) -> Self {
        Self {
            role: MessageRole::Assistant,
            content: content.into(),
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        }
    }
    
    /// 添加元数据
    pub fn with_metadata(mut self, key: impl Into<String>, value: serde_json::Value) -> Self {
        self.metadata.insert(key.into(), value);
        self
    }
}

/// 工具定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolDefinition {
    /// 工具名称
    pub name: String,
    /// 工具描述
    pub description: String,
    /// 参数模式（JSON Schema）
    pub parameters: serde_json::Value,
}

/// LLM 请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmRequest {
    /// 请求 ID
    pub id: Uuid,
    /// 消息列表
    pub messages: Vec<LlmMessage>,
    /// 模型参数（覆盖默认配置）
    pub parameters: Option<ModelParameters>,
    /// 工具定义（用于函数调用）
    pub tools: Vec<ToolDefinition>,
    /// 是否启用流式响应
    pub streaming: Option<bool>,
    /// 请求元数据
    pub metadata: HashMap<String, serde_json::Value>,
}

impl LlmRequest {
    /// 创建新请求
    pub fn new(messages: Vec<LlmMessage>) -> Self {
        Self {
            id: Uuid::new_v4(),
            messages,
            parameters: None,
            tools: Vec::new(),
            streaming: None,
            metadata: HashMap::new(),
        }
    }
    
    /// 设置模型参数
    pub fn with_parameters(mut self, parameters: ModelParameters) -> Self {
        self.parameters = Some(parameters);
        self
    }
    
    /// 添加工具
    pub fn with_tools(mut self, tools: Vec<ToolDefinition>) -> Self {
        self.tools = tools;
        self
    }
    
    /// 启用流式响应
    pub fn with_streaming(mut self, streaming: bool) -> Self {
        self.streaming = Some(streaming);
        self
    }
}

/// 工具调用
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    /// 调用 ID
    pub id: String,
    /// 工具名称
    pub name: String,
    /// 调用参数
    pub arguments: serde_json::Value,
}

/// Token 使用统计
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct TokenUsage {
    /// 输入 token 数
    pub input_tokens: u32,
    /// 输出 token 数
    pub output_tokens: u32,
    /// 总 token 数
    pub total_tokens: u32,
}

/// 完成原因
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FinishReason {
    /// 正常完成
    Stop,
    /// 达到最大长度
    Length,
    /// 工具调用
    ToolCalls,
    /// 内容过滤
    ContentFilter,
    /// 错误
    Error(String),
}

/// LLM 响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmResponse {
    /// 请求 ID
    pub request_id: Uuid,
    /// 响应内容
    pub content: String,
    /// 工具调用
    pub tool_calls: Vec<ToolCall>,
    /// 使用的 token 数量
    pub usage: TokenUsage,
    /// 完成原因
    pub finish_reason: FinishReason,
    /// 响应元数据
    pub metadata: HashMap<String, serde_json::Value>,
    /// 响应时间戳
    pub timestamp: SystemTime,
}

/// 流式响应块
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StreamChunk {
    /// 请求 ID
    pub request_id: Uuid,
    /// 增量内容
    pub delta: String,
    /// 是否为最后一块
    pub is_final: bool,
    /// 工具调用（如果有）
    pub tool_calls: Vec<ToolCall>,
    /// 完成原因（仅在最后一块）
    pub finish_reason: Option<FinishReason>,
}
