//! # 工作流列表功能
//!
//! 实现工作流的列出功能，支持列出定义和实例。

use crate::error::Result;
use crate::commands::workflow::{types::ListWorkflows, utils::*};
use orion_core::workflow::{WorkflowDefinition, WorkflowInstance};

impl ListWorkflows {
    /// 执行列出工作流
    pub async fn execute(&self) -> Result<()> {
        let workflow_manager = create_workflow_manager(&self.config).await?;

        if self.instances {
            // 列出工作流实例
            let instances = workflow_manager.list_instances(None).await;

            let filtered_instances: Vec<_> = instances.into_iter()
                .filter(|instance| {
                    if let Some(status) = &self.status {
                        format!("{:?}", instance.status).to_lowercase() == status.to_lowercase()
                    } else {
                        true
                    }
                })
                .take(self.limit.unwrap_or(usize::MAX))
                .collect();

            self.print_instances(&filtered_instances)?;
        } else {
            // 列出工作流定义
            let definitions = workflow_manager.list_workflows().await;

            let limited_definitions: Vec<_> = definitions.into_iter()
                .take(self.limit.unwrap_or(usize::MAX))
                .collect();

            self.print_definitions(&limited_definitions)?;
        }

        Ok(())
    }
    
    /// 打印工作流定义
    fn print_definitions(&self, definitions: &[WorkflowDefinition]) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(definitions, &self.format)?;
            }
            "table" | _ => {
                if definitions.is_empty() {
                    println!("没有找到工作流定义");
                    return Ok(());
                }

                println!("{:<36} {:<20} {:<10} {:<50}", "ID", "名称", "版本", "描述");
                println!("{}", "-".repeat(120));

                for def in definitions {
                    println!(
                        "{:<36} {:<20} {:<10} {:<50}",
                        def.id,
                        def.name,
                        def.version,
                        if def.description.is_empty() { "-" } else { &def.description }
                    );
                }
            }
        }

        Ok(())
    }
    
    /// 打印工作流实例
    fn print_instances(&self, instances: &[WorkflowInstance]) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(instances, &self.format)?;
            }
            "table" | _ => {
                if instances.is_empty() {
                    println!("没有找到工作流实例");
                    return Ok(());
                }

                println!("{:<36} {:<20} {:<12} {:<20} {:<20}", "实例ID", "工作流名称", "状态", "创建时间", "更新时间");
                println!("{}", "-".repeat(110));

                for instance in instances {
                    let created_at = instance.created_at
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs();
                    let updated_at = instance.created_at  // 使用 created_at 因为没有 updated_at
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs();

                    println!(
                        "{:<36} {:<20} {:<12} {:<20} {:<20}",
                        instance.id,
                        instance.workflow_id,
                        format!("{:?}", instance.status),
                        format_timestamp(created_at),
                        format_timestamp(updated_at)
                    );
                }
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_list_workflows_command() {
        let cmd = ListWorkflows {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            status: None,
            instances: false,
            limit: None,
        };
        
        assert_eq!(cmd.format, "table");
        assert!(!cmd.instances);
    }

    #[test]
    fn test_print_empty_definitions() {
        let cmd = ListWorkflows {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            status: None,
            instances: false,
            limit: None,
        };

        let empty_definitions: Vec<WorkflowDefinition> = vec![];
        let result = cmd.print_definitions(&empty_definitions);
        assert!(result.is_ok());
    }
}