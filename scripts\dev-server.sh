#!/bin/bash

# Orion 热重载开发服务器
# 使用 cargo-watch 监控文件变更并自动重新编译

ORION_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 检查是否安装了 cargo-watch
if ! command -v cargo-watch &> /dev/null; then
    echo "📦 正在安装 cargo-watch..."
    cargo install cargo-watch
fi

echo "🚀 启动 Orion 开发服务器..."
echo "📁 项目根目录: $ORION_ROOT"
echo "👀 监控文件变更中..."
echo ""

cd "$ORION_ROOT"

# 启动文件监控和自动编译
cargo-watch \
    --watch orion-cli/src \
    --watch orion-core/src \
    --watch Cargo.toml \
    --watch orion-cli/Cargo.toml \
    --watch orion-core/Cargo.toml \
    --shell 'cargo build --bin orion && echo "✅ 编译完成，二进制文件已更新"' \
    --clear \
    --ignore-nothing