# Orion PowerShell 配置文件
# 将此内容添加到 PowerShell Profile 中

# Orion 项目路径
$ORION_ROOT = "E:\Orion"

# Orion 开发函数
function orion {
    param(
        [Parameter(ValueFromRemainingArguments = $true)]
        [string[]]$Arguments
    )
    
    $scriptPath = Join-Path $ORION_ROOT "scripts\orion-dev.bat"
    & $scriptPath @Arguments
}

# 快速导航函数
function cdo {
    Set-Location $ORION_ROOT
}

# 清理缓存函数
function orion-clean {
    $cacheDir = Join-Path $ORION_ROOT ".dev-cache"
    $targetDir = Join-Path $ORION_ROOT "target"
    
    if (Test-Path $cacheDir) { Remove-Item -Recurse -Force $cacheDir }
    if (Test-Path $targetDir) { Remove-Item -Recurse -Force $targetDir }
    
    Write-Host "🧹 缓存已清理" -ForegroundColor Green
}

Write-Host "🚀 Orion 开发环境已加载" -ForegroundColor Green
Write-Host "💡 使用 'orion' 命令运行（自动编译）" -ForegroundColor Cyan
Write-Host "💡 使用 'cdo' 快速进入项目目录" -ForegroundColor Cyan