//! # 工具显示功能
//!
//! 实现工具详情的显示功能。

use crate::error::{CliError, Result};
use crate::commands::tools::{types::ShowTool, utils::*};
use orion_core::tools::ToolDefinition;

impl ShowTool {
    /// 执行显示工具详情
    pub async fn execute(&self) -> Result<()> {
        let tool_registry = create_tool_registry(&self.config).await?;

        let tool_definition = tool_registry.get_tool_definition(&self.tool_name).await
            .map_err(|e| CliError::ExecutionError {
                error: format!("获取工具失败: {}", e),
            })?;

        self.print_tool_details(&tool_definition)?;

        Ok(())
    }
    
    /// 打印工具详情
    fn print_tool_details(&self, def: &ToolDefinition) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(def, &self.format)?;
            }
            "text" | _ => {
                println!("🔧 工具详情");
                println!();
                println!("名称: {}", def.name);
                println!("描述: {}", def.description);
                println!("版本: {}", def.version);
                println!("分类: {}", def.category);
                
                if !def.tags.is_empty() {
                    println!("标签: {}", def.tags.join(", "));
                }
                
                if self.parameters && !def.parameters.is_empty() {
                    println!();
                    println!("📋 参数:");
                    for param in &def.parameters {
                        println!("  • {} ({})", param.name, format_parameter_type(&param.param_type));
                        println!("    {}", param.description);
                        if param.required {
                            println!("    [必需]");
                        }
                        if let Some(default) = &param.default_value {
                            println!("    默认值: {}", default);
                        }
                        println!();
                    }
                }
                
                if self.examples && !def.examples.is_empty() {
                    println!("💡 示例:");
                    for (i, example) in def.examples.iter().enumerate() {
                        println!("  {}. {}", i + 1, example.description);
                        println!("     输入: {}", serde_json::to_string(&example.input).unwrap_or_default());
                        println!("     输出: {}", serde_json::to_string(&example.expected_output).unwrap_or_default());
                        println!();
                    }
                }
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_show_tool_command() {
        let cmd = ShowTool {
            config: PathBuf::from("test.toml"),
            tool_name: "file_read".to_string(),
            format: "text".to_string(),
            examples: true,
            parameters: true,
        };
        
        assert_eq!(cmd.tool_name, "file_read");
        assert_eq!(cmd.format, "text");
        assert!(cmd.examples);
        assert!(cmd.parameters);
    }
}