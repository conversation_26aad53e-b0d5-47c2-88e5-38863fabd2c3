//! # UI 模块
//!
//! 提供终端用户界面相关功能，包括主题、组件、布局等。

pub mod ascii_art;
pub mod colors;
pub mod components;
pub mod constants;
pub mod themes;
pub mod utils;

pub use ascii_art::{ORION_SHORT_LOGO, ORION_LONG_LOGO};
pub use colors::Colors;
pub use constants::*;
pub use themes::ThemeManager;

use crossterm::{
    execute,
    terminal::{size, Clear, ClearType},
    cursor::{MoveTo, Hide, Show},
    style::{Color, SetForegroundColor, ResetColor, Print},
};
use std::io::{stdout, Write};

/// UI 管理器
pub struct UIManager {
    theme_manager: ThemeManager,
    terminal_width: u16,
    terminal_height: u16,
}

impl UIManager {
    /// 创建新的 UI 管理器
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let (width, height) = size()?;
        Ok(Self {
            theme_manager: ThemeManager::new(),
            terminal_width: width,
            terminal_height: height,
        })
    }

    /// 初始化终端
    pub fn init_terminal(&self) -> Result<(), Box<dyn std::error::Error>> {
        execute!(stdout(), Hide)?;
        Ok(())
    }

    /// 清理终端
    pub fn cleanup_terminal(&self) -> Result<(), Box<dyn std::error::Error>> {
        execute!(stdout(), Show, ResetColor)?;
        Ok(())
    }

    /// 清屏
    pub fn clear_screen(&self) -> Result<(), Box<dyn std::error::Error>> {
        execute!(stdout(), Clear(ClearType::All), MoveTo(0, 0))?;
        Ok(())
    }

    /// 获取终端尺寸
    pub fn get_terminal_size(&self) -> (u16, u16) {
        (self.terminal_width, self.terminal_height)
    }

    /// 更新终端尺寸
    pub fn update_terminal_size(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let (width, height) = size()?;
        self.terminal_width = width;
        self.terminal_height = height;
        Ok(())
    }

    /// 打印彩色文本
    pub fn print_colored(&self, text: &str, color: Color) -> Result<(), Box<dyn std::error::Error>> {
        execute!(
            stdout(),
            SetForegroundColor(color),
            Print(text),
            ResetColor
        )?;
        stdout().flush()?;
        Ok(())
    }

    /// 打印渐变文本（简化版）
    pub fn print_gradient(&self, text: &str, start_color: Color, end_color: Color) -> Result<(), Box<dyn std::error::Error>> {
        // 简化的渐变实现，实际可以更复杂
        let lines: Vec<&str> = text.lines().collect();
        let total_lines = lines.len();
        
        for (i, line) in lines.iter().enumerate() {
            let color = if i < total_lines / 2 {
                start_color
            } else {
                end_color
            };
            
            execute!(
                stdout(),
                SetForegroundColor(color),
                Print(line),
                Print("\n"),
                ResetColor
            )?;
        }
        
        stdout().flush()?;
        Ok(())
    }

    /// 获取主题管理器的引用
    pub fn theme_manager(&self) -> &ThemeManager {
        &self.theme_manager
    }

    /// 获取主题管理器的可变引用
    pub fn theme_manager_mut(&mut self) -> &mut ThemeManager {
        &mut self.theme_manager
    }
}

impl Default for UIManager {
    fn default() -> Self {
        Self::new().unwrap_or_else(|_| Self {
            theme_manager: ThemeManager::new(),
            terminal_width: 80,
            terminal_height: 24,
        })
    }
}