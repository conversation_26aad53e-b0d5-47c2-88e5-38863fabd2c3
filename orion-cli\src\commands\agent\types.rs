//! # Agent 命令类型定义
//!
//! 定义所有 Agent 相关的命令结构体和枚举。

use clap::{Args, Subcommand};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// Agent 命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct AgentCommand {
    #[command(subcommand)]
    pub action: AgentAction,
}

/// Agent 操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum AgentAction {
    /// 列出 Agent
    List(ListAgents),
    /// 创建 Agent
    Create(CreateAgent),
    /// 启动 Agent
    Start(StartAgent),
    /// 停止 Agent
    Stop(StopAgent),
    /// 重启 Agent
    Restart(RestartAgent),
    /// Agent 状态
    Status(AgentStatus),
    /// Agent 统计
    Stats(AgentStats),
    /// 与 Agent 交互
    Chat(ChatWithAgent),
    /// 配置 Agent
    Config(ConfigAgent),
    /// 删除 Agent
    Delete(DeleteAgent),
    /// 克隆 Agent
    <PERSON>lone(CloneAgent),
    /// 导出 Agent
    Export(ExportAgent),
    /// 导入 Agent
    Import(ImportAgent),
}

/// 列出 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ListAgents {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 输出格式
    #[arg(short, long, default_value = "table", value_parser = ["table", "json", "yaml"])]
    pub format: String,
    
    /// 显示详细信息
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 过滤状态
    #[arg(long, value_parser = ["idle", "busy", "paused", "stopped", "error"])]
    pub status: Option<String>,
    
    /// 显示统计信息
    #[arg(long)]
    pub stats: bool,
}

/// 创建 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct CreateAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub name: String,
    
    /// Agent 描述
    #[arg(short, long)]
    pub description: Option<String>,
    
    /// LLM 模型
    #[arg(short, long)]
    pub model: Option<String>,
    
    /// 系统提示
    #[arg(long)]
    pub system_prompt: Option<String>,
    
    /// 温度参数
    #[arg(long)]
    pub temperature: Option<f32>,
    
    /// 最大令牌数
    #[arg(long)]
    pub max_tokens: Option<u32>,
    
    /// 启用沙箱
    #[arg(long)]
    pub sandbox: Option<bool>,
    
    /// 启用记忆
    #[arg(long)]
    pub memory: Option<bool>,
    
    /// 启用学习
    #[arg(long)]
    pub learning: Option<bool>,
    
    /// 从模板创建
    #[arg(short, long, value_parser = ["basic", "assistant", "coder", "analyst", "custom"])]
    pub template: Option<String>,
    
    /// 交互模式
    #[arg(short, long)]
    pub interactive: bool,
    
    /// 自动启动
    #[arg(long)]
    pub auto_start: bool,
}

/// 启动 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct StartAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 后台运行
    #[arg(short, long)]
    pub daemon: bool,
    
    /// 监控模式
    #[arg(short, long)]
    pub monitor: bool,
    
    /// 日志级别
    #[arg(long, value_parser = ["trace", "debug", "info", "warn", "error"])]
    pub log_level: Option<String>,
    
    /// 工作目录
    #[arg(long)]
    pub workdir: Option<PathBuf>,
}

/// 停止 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct StopAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 强制停止
    #[arg(short, long)]
    pub force: bool,
    
    /// 超时时间（秒）
    #[arg(long, default_value_t = 30)]
    pub timeout: u64,
    
    /// 保存状态
    #[arg(long, default_value_t = true)]
    pub save_state: bool,
}

/// 重启 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct RestartAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 强制重启
    #[arg(short, long)]
    pub force: bool,
    
    /// 重启延迟（秒）
    #[arg(long, default_value_t = 5)]
    pub delay: u64,
}

/// Agent 状态
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct AgentStatus {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 输出格式
    #[arg(short, long, default_value = "text", value_parser = ["text", "json", "yaml"])]
    pub format: String,
    
    /// 显示详细信息
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 实时监控
    #[arg(short, long)]
    pub watch: bool,
    
    /// 刷新间隔（秒）
    #[arg(long, default_value_t = 5)]
    pub interval: u64,
}

/// Agent 统计
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct AgentStats {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 输出格式
    #[arg(short, long, default_value = "text", value_parser = ["text", "json", "yaml"])]
    pub format: String,
    
    /// 时间范围（小时）
    #[arg(long)]
    pub hours: Option<u64>,
    
    /// 重置统计
    #[arg(long)]
    pub reset: bool,
    
    /// 导出统计
    #[arg(long)]
    pub export: Option<PathBuf>,
}

/// 与 Agent 交互
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ChatWithAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 消息内容
    pub message: Option<String>,
    
    /// 从文件读取消息
    #[arg(short, long)]
    pub file: Option<PathBuf>,
    
    /// 交互模式
    #[arg(short, long)]
    pub interactive: bool,
    
    /// 会话 ID
    #[arg(long)]
    pub session_id: Option<String>,
    
    /// 任务类型
    #[arg(long, value_parser = ["qa", "code_generation", "document_analysis", "data_processing", "workflow_execution", "tool_call", "custom"])]
    pub task_type: Option<String>,
    
    /// 任务优先级
    #[arg(long, value_parser = ["low", "normal", "high", "urgent"])]
    pub priority: Option<String>,
    
    /// 超时时间（秒）
    #[arg(long)]
    pub timeout: Option<u64>,
    
    /// 输出格式
    #[arg(long, default_value = "text", value_parser = ["text", "json", "yaml"])]
    pub format: String,
}

/// 配置 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ConfigAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 配置键
    pub key: Option<String>,
    
    /// 配置值
    pub value: Option<String>,
    
    /// 列出所有配置
    #[arg(short, long)]
    pub list: bool,
    
    /// 重置配置
    #[arg(long)]
    pub reset: bool,
    
    /// 从文件加载配置
    #[arg(long)]
    pub load: Option<PathBuf>,
    
    /// 保存配置到文件
    #[arg(long)]
    pub save: Option<PathBuf>,
}

/// 删除 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct DeleteAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 确认删除
    #[arg(short, long)]
    pub yes: bool,
    
    /// 保留数据
    #[arg(long)]
    pub keep_data: bool,
    
    /// 备份配置
    #[arg(long)]
    pub backup: bool,
}

/// 克隆 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct CloneAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 源 Agent 名称
    pub source_agent: String,
    
    /// 新 Agent 名称
    pub new_agent: String,
    
    /// 克隆数据
    #[arg(long)]
    pub clone_data: bool,
    
    /// 克隆配置
    #[arg(long, default_value_t = true)]
    pub clone_config: bool,
}

/// 导出 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ExportAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// Agent 名称
    pub agent_name: String,
    
    /// 输出文件
    #[arg(short, long)]
    pub output: PathBuf,
    
    /// 导出格式
    #[arg(short, long, default_value = "yaml", value_parser = ["yaml", "json", "toml"])]
    pub format: String,
    
    /// 包含数据
    #[arg(long)]
    pub include_data: bool,
    
    /// 包含统计
    #[arg(long)]
    pub include_stats: bool,
}

/// 导入 Agent
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ImportAgent {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 输入文件
    pub input: PathBuf,
    
    /// 新 Agent 名称
    #[arg(short, long)]
    pub name: Option<String>,
    
    /// 覆盖已存在的 Agent
    #[arg(short, long)]
    pub force: bool,
    
    /// 验证配置
    #[arg(long, default_value_t = true)]
    pub validate: bool,
}