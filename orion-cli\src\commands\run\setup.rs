//! # Agent 设置和初始化
//!
//! 实现 Agent 和相关组件的创建和初始化。

use crate::error::{CliError, Result};
use crate::commands::run::types::*;
use orion_core::{
    agent::{Agent, AgentConfig},
    config::OrionConfig,
    context::ContextManager,
    llm::{LlmEngine, ModelConfig, ModelParameters, KimiProvider},
    message::MessageBus,
    tools::ToolRegistry,
    workflow::{WorkflowManager, DefaultWorkflowExecutor},
};
use std::sync::Arc;

impl RunCommand {
    /// 创建 Agent 和相关组件
    pub async fn create_agent(&self, config: OrionConfig) -> Result<(Agent, AgentComponents)> {
        // 创建消息总线
        let message_bus = Arc::new(MessageBus::new());
        
        // 创建 LLM 引擎
        let llm_engine = Arc::new(LlmEngine::new());

        // 注册 Kimi 提供商
        self.register_kimi_provider(&llm_engine).await?;
        
        // 创建工具注册表
        let tool_registry = Arc::new(ToolRegistry::new());
        
        // 注册内置工具
        self.register_builtin_tools(&tool_registry).await?;
        
        // 创建上下文管理器
        let context_manager = Arc::new(ContextManager::with_default_config());
        
        // 创建工作流管理器
        let workflow_executor = Arc::new(DefaultWorkflowExecutor::new(
            tool_registry.clone(),
            message_bus.clone(),
            self.max_concurrent_tasks,
        ));
        let workflow_manager = Arc::new(WorkflowManager::new(workflow_executor));
        
        // 创建 Agent 配置
        let mut agent_config = AgentConfig::default();
        agent_config.name = self.name.clone();
        agent_config.enable_sandbox = self.sandbox;
        
        // 从全局配置更新 Agent 配置
        agent_config.default_model = config.agents.default_model.clone();
        agent_config.max_context_length = config.agents.max_context_length;
        
        // 创建 Agent
        let agent = Agent::new(
            agent_config,
            llm_engine.clone(),
            tool_registry.clone(),
            context_manager.clone(),
            workflow_manager.clone(),
            message_bus.clone(),
        ).map_err(|e| CliError::InitializationError {
            error: format!("创建 Agent 失败: {}", e),
        })?;
        
        let components = AgentComponents {
            message_bus,
            llm_engine,
            tool_registry,
            context_manager,
            workflow_manager,
        };
        
        Ok((agent, components))
    }
    
    /// 注册 Kimi LLM 提供商
    async fn register_kimi_provider(&self, llm_engine: &LlmEngine) -> Result<()> {
        // 创建 Kimi 模型配置
        let kimi_config = ModelConfig {
            name: "moonshot-v1-8k".to_string(),
            provider: "kimi".to_string(),
            endpoint: Some("https://api.moonshot.cn/v1/chat/completions".to_string()),
            api_key: Some("sk-9mTvKhD3FlHnOv0gjlhDjxh7c4wQ8gHi84o0rxbJqmeFR1dA".to_string()),
            parameters: ModelParameters {
                temperature: 0.7,
                max_tokens: 4096,
                top_p: Some(0.9),
                top_k: None,
                frequency_penalty: None,
                presence_penalty: None,
                stop_sequences: Vec::new(),
            },
            timeout_seconds: 60,
            max_retries: 3,
            streaming: false,
        };

        // 创建 Kimi 提供商
        let kimi_provider = KimiProvider::new(kimi_config)
            .map_err(|e| CliError::InitializationError {
                error: format!("创建 Kimi 提供商失败: {}", e),
            })?;

        // 注册提供商
        llm_engine.register_provider("kimi".to_string(), Box::new(kimi_provider))
            .await
            .map_err(|e| CliError::InitializationError {
                error: format!("注册 Kimi 提供商失败: {}", e),
            })?;

        // 设置为默认提供商
        llm_engine.set_default_provider("kimi".to_string())
            .await
            .map_err(|e| CliError::InitializationError {
                error: format!("设置默认 LLM 提供商失败: {}", e),
            })?;

        tracing::info!("Kimi LLM 提供商注册完成");
        Ok(())
    }

    /// 注册内置工具
    async fn register_builtin_tools(&self, tool_registry: &ToolRegistry) -> Result<()> {
        // 注册文件操作工具
        tool_registry.register_tool(Box::new(orion_core::tools::FileReadTool))
            .await
            .map_err(|e| CliError::InitializationError {
                error: format!("注册文件读取工具失败: {}", e),
            })?;

        tool_registry.register_tool(Box::new(orion_core::tools::FileWriteTool))
            .await
            .map_err(|e| CliError::InitializationError {
                error: format!("注册文件写入工具失败: {}", e),
            })?;
        
        // 注册 HTTP 请求工具
        tool_registry.register_tool(Box::new(orion_core::tools::HttpRequestTool::new()))
            .await
            .map_err(|e| CliError::InitializationError {
                error: format!("注册 HTTP 请求工具失败: {}", e),
            })?;
        
        tracing::info!("内置工具注册完成");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_agent_creation_config() {
        let cmd = RunCommand {
            config: None,
            name: "test-agent".to_string(),
            interactive: false,
            verbose: false,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 5,
            workdir: None,
            command: Some("hello".to_string()),
            file: None,
            output_format: "text".to_string(),
            stream: false,
            typing_speed: 30,
        };
        
        // 测试配置参数
        assert_eq!(cmd.name, "test-agent");
        assert!(cmd.sandbox);
        assert_eq!(cmd.max_concurrent_tasks, 5);
    }
}