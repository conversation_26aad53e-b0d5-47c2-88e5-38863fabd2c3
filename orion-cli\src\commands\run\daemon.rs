//! # 守护进程模式
//!
//! 实现Agent的守护进程运行模式。

use crate::error::{CliError, Result};
use crate::commands::run::types::RunCommand;
use orion_core::agent::Agent;

impl RunCommand {
    /// 运行守护进程模式
    pub async fn run_daemon_mode(&self, agent: &Agent) -> Result<()> {
        println!("🔄 Orion Agent 守护进程模式启动");
        
        // 设置信号处理
        self.setup_signal_handlers(agent).await
    }
    
    /// 设置信号处理
    async fn setup_signal_handlers(&self, agent: &Agent) -> Result<()> {
        #[cfg(unix)]
        {
            self.setup_unix_signals(agent).await
        }

        #[cfg(windows)]
        {
            self.setup_windows_signals(agent).await
        }
    }
    
    #[cfg(unix)]
    async fn setup_unix_signals(&self, agent: &Agent) -> Result<()> {
        use tokio::signal::unix::{signal, SignalKind};

        let mut sigterm = signal(SignalKind::terminate())
            .map_err(|e| CliError::InitializationError {
                error: format!("设置信号处理失败: {}", e),
            })?;

        let mut sigint = signal(SignalKind::interrupt())
            .map_err(|e| CliError::InitializationError {
                error: format!("设置信号处理失败: {}", e),
            })?;

        // 等待信号
        tokio::select! {
            _ = sigterm.recv() => {
                tracing::info!("收到 SIGTERM 信号，正在关闭...");
            }
            _ = sigint.recv() => {
                tracing::info!("收到 SIGINT 信号，正在关闭...");
            }
        }
        
        self.graceful_shutdown(agent).await
    }

    #[cfg(windows)]
    async fn setup_windows_signals(&self, agent: &Agent) -> Result<()> {
        use tokio::signal;

        // 等待 Ctrl+C 信号
        signal::ctrl_c().await.map_err(|e| CliError::InitializationError {
            error: format!("等待信号失败: {}", e),
        })?;

        tracing::info!("收到 Ctrl+C 信号，正在关闭...");
        
        self.graceful_shutdown(agent).await
    }
    
    /// 优雅关闭
    async fn graceful_shutdown(&self, agent: &Agent) -> Result<()> {
        agent.stop().await
            .map_err(|e| CliError::ExecutionError {
                error: format!("停止 Agent 失败: {}", e),
            })?;
        
        println!("👋 Orion Agent 已停止");
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_daemon_config() {
        let cmd = RunCommand {
            config: None,
            name: "daemon-agent".to_string(),
            interactive: false,
            verbose: false,
            log_level: "warn".to_string(),
            sandbox: true,
            max_concurrent_tasks: 20,
            workdir: Some("/var/orion".to_string()),
            command: None,
            file: None,
            output_format: "json".to_string(),
            stream: false,
            typing_speed: 0,
        };
        
        assert!(!cmd.interactive);
        assert_eq!(cmd.log_level, "warn");
        assert_eq!(cmd.max_concurrent_tasks, 20);
        assert_eq!(cmd.workdir, Some("/var/orion".to_string()));
    }
}