//! # 沙箱核心类型定义
//!
//! 定义沙箱系统中使用的所有核心数据结构和枚举类型。
//! 包括配置、执行结果、访问控制等基础类型。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::SystemTime;
use tempfile::TempDir;
use tokio::process::Child;
use uuid::Uuid;

/// 沙箱配置
/// 
/// 定义沙箱执行环境的各种限制和控制参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SandboxConfig {
    /// 最大执行时间（秒）
    pub timeout_seconds: u64,
    /// 最大内存使用（MB）
    pub max_memory_mb: u64,
    /// 最大 CPU 使用率（百分比）
    pub max_cpu_percent: f32,
    /// 工作目录
    pub working_directory: Option<PathBuf>,
    /// 环境变量
    pub environment: HashMap<String, String>,
    /// 网络访问控制
    pub network_access: NetworkAccess,
    /// 文件系统访问控制
    pub filesystem_access: FilesystemAccess,
    /// 是否允许子进程
    pub allow_subprocesses: bool,
    /// 临时目录清理
    pub cleanup_temp_files: bool,
}

impl Default for SandboxConfig {
    fn default() -> Self {
        Self {
            timeout_seconds: 30,
            max_memory_mb: 512,
            max_cpu_percent: 80.0,
            working_directory: None,
            environment: HashMap::new(),
            network_access: NetworkAccess::Restricted(vec![]),
            filesystem_access: FilesystemAccess::Restricted(vec![]),
            allow_subprocesses: false,
            cleanup_temp_files: true,
        }
    }
}

impl SandboxConfig {
    /// 创建新的沙箱配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置超时时间
    pub fn with_timeout(mut self, timeout_seconds: u64) -> Self {
        self.timeout_seconds = timeout_seconds;
        self
    }

    /// 设置内存限制
    pub fn with_memory_limit(mut self, max_memory_mb: u64) -> Self {
        self.max_memory_mb = max_memory_mb;
        self
    }

    /// 设置 CPU 限制
    pub fn with_cpu_limit(mut self, max_cpu_percent: f32) -> Self {
        self.max_cpu_percent = max_cpu_percent;
        self
    }

    /// 设置工作目录
    pub fn with_working_directory(mut self, working_directory: PathBuf) -> Self {
        self.working_directory = Some(working_directory);
        self
    }

    /// 添加环境变量
    pub fn with_environment(mut self, key: String, value: String) -> Self {
        self.environment.insert(key, value);
        self
    }

    /// 设置网络访问控制
    pub fn with_network_access(mut self, network_access: NetworkAccess) -> Self {
        self.network_access = network_access;
        self
    }

    /// 设置文件系统访问控制
    pub fn with_filesystem_access(mut self, filesystem_access: FilesystemAccess) -> Self {
        self.filesystem_access = filesystem_access;
        self
    }

    /// 允许子进程
    pub fn allow_subprocesses(mut self, allow: bool) -> Self {
        self.allow_subprocesses = allow;
        self
    }

    /// 设置临时文件清理
    pub fn cleanup_temp_files(mut self, cleanup: bool) -> Self {
        self.cleanup_temp_files = cleanup;
        self
    }
}

/// 网络访问控制
/// 
/// 定义沙箱环境中的网络访问权限
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NetworkAccess {
    /// 禁止所有网络访问
    Denied,
    /// 允许所有网络访问
    Full,
    /// 仅允许特定域名/IP
    Restricted(Vec<String>),
}

impl NetworkAccess {
    /// 检查是否允许访问指定地址
    pub fn is_allowed(&self, address: &str) -> bool {
        match self {
            NetworkAccess::Denied => false,
            NetworkAccess::Full => true,
            NetworkAccess::Restricted(allowed) => allowed.iter().any(|addr| address.contains(addr)),
        }
    }
}

/// 文件系统访问控制
/// 
/// 定义沙箱环境中的文件系统访问权限
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilesystemAccess {
    /// 只读访问
    ReadOnly(Vec<PathBuf>),
    /// 读写访问
    ReadWrite(Vec<PathBuf>),
    /// 限制访问特定路径
    Restricted(Vec<PathBuf>),
}

impl FilesystemAccess {
    /// 检查是否允许读取指定路径
    pub fn can_read(&self, path: &PathBuf) -> bool {
        match self {
            FilesystemAccess::ReadOnly(paths) | FilesystemAccess::ReadWrite(paths) => {
                paths.iter().any(|allowed_path| path.starts_with(allowed_path))
            }
            FilesystemAccess::Restricted(paths) => {
                paths.iter().any(|allowed_path| path.starts_with(allowed_path))
            }
        }
    }

    /// 检查是否允许写入指定路径
    pub fn can_write(&self, path: &PathBuf) -> bool {
        match self {
            FilesystemAccess::ReadOnly(_) => false,
            FilesystemAccess::ReadWrite(paths) => {
                paths.iter().any(|allowed_path| path.starts_with(allowed_path))
            }
            FilesystemAccess::Restricted(paths) => {
                paths.iter().any(|allowed_path| path.starts_with(allowed_path))
            }
        }
    }
}

/// 执行结果
/// 
/// 包含沙箱执行的完整结果信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionResult {
    /// 执行 ID
    pub id: Uuid,
    /// 退出码
    pub exit_code: Option<i32>,
    /// 标准输出
    pub stdout: String,
    /// 标准错误
    pub stderr: String,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 峰值内存使用（MB）
    pub peak_memory_mb: u64,
    /// CPU 使用率（百分比）
    pub cpu_usage_percent: f32,
    /// 是否超时
    pub timed_out: bool,
    /// 是否被强制终止
    pub killed: bool,
    /// 错误信息
    pub error: Option<String>,
    /// 执行时间戳
    pub timestamp: SystemTime,
}

impl ExecutionResult {
    /// 创建新的执行结果
    pub fn new(id: Uuid) -> Self {
        Self {
            id,
            exit_code: None,
            stdout: String::new(),
            stderr: String::new(),
            execution_time_ms: 0,
            peak_memory_mb: 0,
            cpu_usage_percent: 0.0,
            timed_out: false,
            killed: false,
            error: None,
            timestamp: SystemTime::now(),
        }
    }

    /// 检查执行是否成功
    pub fn is_success(&self) -> bool {
        !self.timed_out && !self.killed && self.error.is_none() && 
        self.exit_code.map_or(false, |code| code == 0)
    }

    /// 获取执行状态描述
    pub fn status_description(&self) -> String {
        if self.timed_out {
            "执行超时".to_string()
        } else if self.killed {
            "被强制终止".to_string()
        } else if let Some(ref error) = self.error {
            format!("执行错误: {}", error)
        } else if let Some(code) = self.exit_code {
            if code == 0 {
                "执行成功".to_string()
            } else {
                format!("执行失败，退出码: {}", code)
            }
        } else {
            "执行状态未知".to_string()
        }
    }
}

/// 脚本类型
/// 
/// 支持的脚本语言类型
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum ScriptType {
    /// Python 脚本
    Python,
    /// JavaScript 脚本
    JavaScript,
    /// Shell 脚本
    Shell,
    /// PowerShell 脚本
    PowerShell,
}

impl ScriptType {
    /// 获取脚本文件扩展名
    pub fn file_extension(&self) -> &'static str {
        match self {
            ScriptType::Python => "py",
            ScriptType::JavaScript => "js",
            ScriptType::Shell => "sh",
            ScriptType::PowerShell => "ps1",
        }
    }

    /// 获取解释器命令
    pub fn interpreter(&self) -> &'static str {
        match self {
            ScriptType::Python => "python",
            ScriptType::JavaScript => "node",
            ScriptType::Shell => "bash",
            ScriptType::PowerShell => "powershell",
        }
    }

    /// 获取解释器参数
    pub fn interpreter_args(&self) -> Vec<&'static str> {
        match self {
            ScriptType::Python => vec![],
            ScriptType::JavaScript => vec![],
            ScriptType::Shell => vec![],
            ScriptType::PowerShell => vec!["-ExecutionPolicy", "Bypass", "-File"],
        }
    }
}

/// 执行上下文
/// 
/// 内部使用的执行上下文，包含执行过程中的临时数据
#[derive(Debug)]
pub struct ExecutionContext {
    /// 执行 ID
    pub id: Uuid,
    /// 临时目录
    pub temp_dir: TempDir,
    /// 子进程
    pub child: Option<Child>,
    /// 开始时间
    pub start_time: SystemTime,
}

impl ExecutionContext {
    /// 创建新的执行上下文
    pub fn new(id: Uuid, temp_dir: TempDir) -> Self {
        Self {
            id,
            temp_dir,
            child: None,
            start_time: SystemTime::now(),
        }
    }

    /// 获取执行时长
    pub fn elapsed_time(&self) -> std::time::Duration {
        self.start_time.elapsed().unwrap_or_default()
    }
}

/// 资源监控器
/// 
/// 用于监控和记录资源使用情况
#[derive(Debug, Default, Clone)]
pub struct ResourceMonitor {
    /// 峰值内存使用（MB）
    pub peak_memory_mb: u64,
    /// CPU 使用率（百分比）
    pub cpu_usage_percent: f32,
}

impl ResourceMonitor {
    /// 创建新的资源监控器
    pub fn new() -> Self {
        Self::default()
    }

    /// 更新内存使用
    pub fn update_memory(&mut self, memory_mb: u64) {
        if memory_mb > self.peak_memory_mb {
            self.peak_memory_mb = memory_mb;
        }
    }

    /// 更新 CPU 使用率
    pub fn update_cpu(&mut self, cpu_percent: f32) {
        self.cpu_usage_percent = cpu_percent;
    }

    /// 重置监控数据
    pub fn reset(&mut self) {
        self.peak_memory_mb = 0;
        self.cpu_usage_percent = 0.0;
    }
}
