//! # 重置配置功能
//!
//! 实现配置文件的重置功能。

use crate::error::{CliError, Result};
use crate::commands::config::types::ResetConfig;
use orion_core::config::OrionConfig;

impl ResetConfig {
    /// 执行重置配置
    pub async fn execute(&self) -> Result<()> {
        // 确认操作
        if !self.yes {
            print!("确定要重置配置吗？这将覆盖现有配置。[y/N]: ");
            use std::io::{self, Write};
            io::stdout().flush().unwrap();
            
            let mut input = String::new();
            io::stdin().read_line(&mut input).unwrap();
            
            if !input.trim().to_lowercase().starts_with('y') {
                println!("操作已取消");
                return Ok(());
            }
        }
        
        // 创建默认配置
        let default_config = OrionConfig::default();
        
        // 如果指定了特定节，只重置该节
        if let Some(section) = &self.section {
            let mut current_config = if self.config.exists() {
                OrionConfig::from_file(&self.config)
                    .map_err(|e| CliError::ConfigError {
                        error: format!("加载配置文件失败: {}", e),
                    })?
            } else {
                OrionConfig::default()
            };
            
            // 重置指定节
            match section.as_str() {
                "api" => current_config.api = default_config.api,
                "security" => current_config.security = default_config.security,
                "workflows" => current_config.workflows = default_config.workflows,
                "logging" => current_config.logging = default_config.logging,
                "agents" => current_config.agents = default_config.agents,
                "tools" => current_config.tools = default_config.tools,
                _ => {
                    return Err(CliError::ConfigError {
                        error: format!("未知的配置节: {}", section),
                    });
                }
            }
            
            // 保存配置
            current_config.save_to_file(&self.config)
                .map_err(|e| CliError::ConfigError {
                    error: format!("保存配置文件失败: {}", e),
                })?;

            println!("✅ 配置节 '{}' 已重置为默认值", section);
        } else {
            // 重置整个配置
            default_config.save_to_file(&self.config)
                .map_err(|e| CliError::ConfigError {
                    error: format!("保存配置文件失败: {}", e),
                })?;
            
            println!("✅ 配置已重置为默认值");
        }
        
        Ok(())
    }
}