# Orion CLI

Orion AI Agent 系统的命令行界面工具，提供完整的 Agent 管理、工作流编排、工具集成等功能。

## 功能特性

### 🤖 Agent 管理
- **创建和配置** - 支持多种 Agent 模板和自定义配置
- **生命周期管理** - 启动、停止、重启、监控 Agent 状态
- **交互式聊天** - 直接与 Agent 进行对话和任务分配
- **统计和监控** - 查看 Agent 性能指标和运行状态

### 🔧 工具集成
- **工具注册表** - 管理和发现可用的工具和插件
- **工具执行** - 直接运行工具并查看结果
- **工具开发** - 创建和测试自定义工具
- **依赖管理** - 自动处理工具依赖和版本控制

### 📋 工作流编排
- **可视化设计** - 通过模板快速创建工作流
- **执行控制** - 启动、暂停、恢复、停止工作流
- **状态监控** - 实时查看工作流执行状态和进度
- **导入导出** - 工作流的备份和分享

### ⚙️ 配置管理
- **环境配置** - 管理不同环境的配置文件
- **安全设置** - API 密钥和认证信息管理
- **性能调优** - 系统性能参数配置

## 安装

### 从源码构建

```bash
# 克隆仓库
git clone https://github.com/orion-ai/orion.git
cd orion/orion-cli

# 构建项目
cargo build --release

# 安装到系统
cargo install --path .
```

### 使用预编译二进制文件

```bash
# 下载最新版本
wget https://github.com/orion-ai/orion/releases/latest/download/orion-cli-linux-x64.tar.gz

# 解压并安装
tar -xzf orion-cli-linux-x64.tar.gz
sudo mv orion /usr/local/bin/
```

## 快速开始

### 1. 初始化配置

```bash
# 创建默认配置文件
orion config init

# 查看当前配置
orion config show
```

### 2. 创建第一个 Agent

```bash
# 列出可用的 Agent 模板
orion agent list

# 创建一个简单的助手 Agent
orion agent create --name "my-assistant" --template "assistant" --model "gpt-4"

# 启动 Agent
orion agent start my-assistant
```

### 3. 与 Agent 交互

```bash
# 进入交互式聊天模式
orion agent chat my-assistant

# 或发送单个消息
orion agent chat my-assistant --message "你好，请帮我分析一下这个数据文件"
```

### 4. 管理工具

```bash
# 查看可用工具
orion tools list

# 搜索特定工具
orion tools search --query "数据分析"

# 运行工具
orion tools run data-analyzer --input data.csv
```

### 5. 创建工作流

```bash
# 列出工作流模板
orion workflow list

# 创建数据处理工作流
orion workflow create --name "data-pipeline" --template "data-processing"

# 运行工作流
orion workflow run data-pipeline --input input.json
```

## 命令参考

### Agent 命令

```bash
orion agent list                    # 列出所有 Agent
orion agent create [OPTIONS]       # 创建新 Agent
orion agent start <NAME>           # 启动 Agent
orion agent stop <NAME>            # 停止 Agent
orion agent restart <NAME>         # 重启 Agent
orion agent status <NAME>          # 查看 Agent 状态
orion agent stats <NAME>           # 查看 Agent 统计信息
orion agent chat <NAME>            # 与 Agent 聊天
orion agent config <NAME>          # 配置 Agent
orion agent delete <NAME>          # 删除 Agent
orion agent clone <SRC> <DST>      # 克隆 Agent
orion agent export <NAME>          # 导出 Agent 配置
orion agent import <FILE>          # 导入 Agent 配置
```

### 工具命令

```bash
orion tools list                    # 列出所有工具
orion tools search <QUERY>         # 搜索工具
orion tools show <NAME>            # 显示工具详情
orion tools install <NAME>         # 安装工具
orion tools uninstall <NAME>       # 卸载工具
orion tools update [NAME]          # 更新工具
orion tools run <NAME> [ARGS]      # 运行工具
orion tools test <NAME>            # 测试工具
orion tools create <NAME>          # 创建新工具
orion tools validate <NAME>        # 验证工具
orion tools config <NAME>          # 配置工具
```

### 工作流命令

```bash
orion workflow list                 # 列出所有工作流
orion workflow create [OPTIONS]    # 创建工作流
orion workflow run <NAME>          # 运行工作流
orion workflow show <NAME>         # 显示工作流详情
orion workflow stop <NAME>         # 停止工作流
orion workflow pause <NAME>        # 暂停工作流
orion workflow resume <NAME>       # 恢复工作流
orion workflow delete <NAME>       # 删除工作流
orion workflow import <FILE>       # 导入工作流
orion workflow export <NAME>       # 导出工作流
orion workflow validate <NAME>     # 验证工作流
```

### 配置命令

```bash
orion config show                   # 显示当前配置
orion config init                   # 初始化配置文件
orion config validate              # 验证配置文件
```

### 全局选项

```bash
--config <FILE>                     # 指定配置文件路径
--log-level <LEVEL>                 # 设置日志级别 (error, warn, info, debug, trace)
--verbose                           # 详细输出
--quiet                             # 静默模式
--output <FORMAT>                   # 输出格式 (json, yaml, table)
--no-color                          # 禁用颜色输出
--profile                           # 启用性能分析
--work-dir <DIR>                    # 设置工作目录
```

## 配置文件

默认配置文件位置：
- Linux/macOS: `~/.config/orion/config.toml`
- Windows: `%APPDATA%\orion\config.toml`

示例配置：

```toml
[general]
log_level = "info"
work_dir = "~/orion-workspace"
output_format = "table"

[api]
base_url = "https://api.orion-ai.com"
api_key = "your-api-key-here"
timeout = 30

[agents]
default_model = "gpt-4"
max_concurrent = 5
auto_start = false

[tools]
registry_url = "https://tools.orion-ai.com"
auto_update = true
cache_dir = "~/.cache/orion/tools"

[workflows]
template_dir = "~/.config/orion/templates"
max_execution_time = 3600
auto_save = true
```

## 开发

### 项目结构

```
orion-cli/
├── src/
│   ├── commands/           # 命令实现
│   │   ├── agent.rs       # Agent 管理命令
│   │   ├── tools.rs       # 工具管理命令
│   │   ├── workflow.rs    # 工作流管理命令
│   │   └── mod.rs         # 命令模块
│   ├── error.rs           # 错误处理
│   ├── lib.rs             # 库入口
│   └── main.rs            # 主程序入口
├── build.rs               # 构建脚本
├── Cargo.toml             # 项目配置
└── README.md              # 项目说明
```

### 运行测试

```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test commands::agent

# 运行集成测试
cargo test --test integration
```

### 代码格式化

```bash
# 格式化代码
cargo fmt

# 检查代码风格
cargo clippy
```

## 贡献

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](../CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](../LICENSE) 文件。

## 支持

- 📖 [文档](https://docs.orion-ai.com)
- 💬 [讨论区](https://github.com/orion-ai/orion/discussions)
- 🐛 [问题反馈](https://github.com/orion-ai/orion/issues)
- 📧 [邮件支持](mailto:<EMAIL>)