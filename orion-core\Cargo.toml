[package]
name = "orion-core"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
repository.workspace = true
description = "Orion AI Agent 核心库 - 高性能异步消息队列和工作流引擎"
keywords.workspace = true
categories.workspace = true

[dependencies]
# 异步运行时
tokio = { workspace = true }
futures = { workspace = true }
async-trait = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }
toml = { workspace = true }

# 错误处理
thiserror = { workspace = true }

# 日志系统
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

# HTTP 客户端
reqwest = { workspace = true }
url = { workspace = true }

# 数据库
rusqlite = { workspace = true }

# UUID 生成
uuid = { workspace = true }

# 时间处理
chrono = { workspace = true }

# 正则表达式
regex = { workspace = true }

# 文件系统操作
tempfile = { workspace = true }

# 目录操作
dirs = { workspace = true }

[dev-dependencies]
# 测试工具
mockall = { workspace = true }
tokio-test = "0.4"

[lib]
name = "orion_core"
path = "src/lib.rs"