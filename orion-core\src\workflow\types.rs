//! # 工作流类型定义模块
//!
//! 重新导出所有工作流相关的公共类型。
//! 提供统一的类型访问接口，简化外部模块的导入。

// 重新导出工作流定义相关类型
pub use super::definition::{
    WorkflowDefinition,
    WorkflowParameter,
    WorkflowStep,
    StepType,
    LoopType,
    WaitStrategy,
};

// 重新导出工作流配置相关类型
pub use super::config::{
    WorkflowConfig,
    StepConfig,
    ResourceLimits,
    RetryConfig,
    BackoffStrategy,
    ErrorHandlingStrategy,
    VariableScope,
};

// 重新导出工作流实例相关类型
pub use super::instance::{
    WorkflowInstance,
    WorkflowStatus,
    ExecutionContext,
    ExecutionStats,
    Checkpoint,
};

// 重新导出工作流执行相关类型
pub use super::execution::{
    WorkflowExecutor,
    StepExecutionResult,
    StepStatus,
};

// 重新导出工作流执行器类型
pub use super::executor::DefaultWorkflowExecutor;

// 重新导出工作流管理器类型
pub use super::manager::WorkflowManager;
