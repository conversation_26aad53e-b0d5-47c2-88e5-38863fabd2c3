//! # 沙箱配置管理
//!
//! 提供沙箱配置的构建、验证和预设管理功能。
//! 包含配置构建器和常用的预设配置。

use super::types::{SandboxConfig, NetworkAccess, FilesystemAccess};
use std::collections::HashMap;
use std::path::PathBuf;

/// 沙箱配置构建器
/// 
/// 提供链式调用的配置构建方式
#[derive(Debug, Default)]
pub struct SandboxConfigBuilder {
    config: SandboxConfig,
}

impl SandboxConfigBuilder {
    /// 创建新的配置构建器
    pub fn new() -> Self {
        Self {
            config: SandboxConfig::default(),
        }
    }

    /// 设置超时时间（秒）
    pub fn timeout_seconds(mut self, timeout: u64) -> Self {
        self.config.timeout_seconds = timeout;
        self
    }

    /// 设置内存限制（MB）
    pub fn max_memory_mb(mut self, memory: u64) -> Self {
        self.config.max_memory_mb = memory;
        self
    }

    /// 设置 CPU 使用率限制（百分比）
    pub fn max_cpu_percent(mut self, cpu: f32) -> Self {
        self.config.max_cpu_percent = cpu;
        self
    }

    /// 设置工作目录
    pub fn working_directory<P: Into<PathBuf>>(mut self, dir: P) -> Self {
        self.config.working_directory = Some(dir.into());
        self
    }

    /// 添加环境变量
    pub fn environment<K: Into<String>, V: Into<String>>(mut self, key: K, value: V) -> Self {
        self.config.environment.insert(key.into(), value.into());
        self
    }

    /// 批量添加环境变量
    pub fn environments(mut self, envs: HashMap<String, String>) -> Self {
        self.config.environment.extend(envs);
        self
    }

    /// 设置网络访问控制
    pub fn network_access(mut self, access: NetworkAccess) -> Self {
        self.config.network_access = access;
        self
    }

    /// 设置文件系统访问控制
    pub fn filesystem_access(mut self, access: FilesystemAccess) -> Self {
        self.config.filesystem_access = access;
        self
    }

    /// 允许子进程
    pub fn allow_subprocesses(mut self, allow: bool) -> Self {
        self.config.allow_subprocesses = allow;
        self
    }

    /// 设置临时文件清理
    pub fn cleanup_temp_files(mut self, cleanup: bool) -> Self {
        self.config.cleanup_temp_files = cleanup;
        self
    }

    /// 构建配置
    pub fn build(self) -> SandboxConfig {
        self.config
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), String> {
        if self.config.timeout_seconds == 0 {
            return Err("超时时间必须大于0".to_string());
        }

        if self.config.max_memory_mb == 0 {
            return Err("内存限制必须大于0".to_string());
        }

        if self.config.max_cpu_percent <= 0.0 || self.config.max_cpu_percent > 100.0 {
            return Err("CPU使用率限制必须在0-100之间".to_string());
        }

        Ok(())
    }

    /// 构建并验证配置
    pub fn build_validated(self) -> Result<SandboxConfig, String> {
        self.validate()?;
        Ok(self.build())
    }
}

/// 预设配置管理器
/// 
/// 提供常用的预设配置
pub struct ConfigPresets;

impl ConfigPresets {
    /// 默认配置
    pub fn default() -> SandboxConfig {
        SandboxConfig::default()
    }

    /// 严格安全配置
    /// 
    /// 适用于不受信任的代码执行
    pub fn strict_security() -> SandboxConfig {
        SandboxConfigBuilder::new()
            .timeout_seconds(10)
            .max_memory_mb(128)
            .max_cpu_percent(50.0)
            .network_access(NetworkAccess::Denied)
            .filesystem_access(FilesystemAccess::Restricted(vec![]))
            .allow_subprocesses(false)
            .cleanup_temp_files(true)
            .build()
    }

    /// 开发环境配置
    /// 
    /// 适用于开发和测试环境
    pub fn development() -> SandboxConfig {
        SandboxConfigBuilder::new()
            .timeout_seconds(60)
            .max_memory_mb(1024)
            .max_cpu_percent(80.0)
            .network_access(NetworkAccess::Full)
            .filesystem_access(FilesystemAccess::ReadWrite(vec![
                PathBuf::from("/tmp"),
                PathBuf::from("./workspace"),
            ]))
            .allow_subprocesses(true)
            .cleanup_temp_files(false)
            .build()
    }

    /// 生产环境配置
    /// 
    /// 适用于生产环境的代码执行
    pub fn production() -> SandboxConfig {
        SandboxConfigBuilder::new()
            .timeout_seconds(30)
            .max_memory_mb(512)
            .max_cpu_percent(70.0)
            .network_access(NetworkAccess::Restricted(vec![
                "api.example.com".to_string(),
                "cdn.example.com".to_string(),
            ]))
            .filesystem_access(FilesystemAccess::ReadOnly(vec![
                PathBuf::from("/app/data"),
            ]))
            .allow_subprocesses(false)
            .cleanup_temp_files(true)
            .build()
    }

    /// 测试环境配置
    /// 
    /// 适用于单元测试和集成测试
    pub fn testing() -> SandboxConfig {
        SandboxConfigBuilder::new()
            .timeout_seconds(5)
            .max_memory_mb(256)
            .max_cpu_percent(90.0)
            .network_access(NetworkAccess::Denied)
            .filesystem_access(FilesystemAccess::Restricted(vec![]))
            .allow_subprocesses(false)
            .cleanup_temp_files(true)
            .build()
    }

    /// 高性能配置
    /// 
    /// 适用于需要高性能的计算任务
    pub fn high_performance() -> SandboxConfig {
        SandboxConfigBuilder::new()
            .timeout_seconds(300)
            .max_memory_mb(2048)
            .max_cpu_percent(95.0)
            .network_access(NetworkAccess::Full)
            .filesystem_access(FilesystemAccess::ReadWrite(vec![
                PathBuf::from("/tmp"),
                PathBuf::from("./data"),
            ]))
            .allow_subprocesses(true)
            .cleanup_temp_files(false)
            .build()
    }

    /// 轻量级配置
    /// 
    /// 适用于简单的脚本执行
    pub fn lightweight() -> SandboxConfig {
        SandboxConfigBuilder::new()
            .timeout_seconds(15)
            .max_memory_mb(64)
            .max_cpu_percent(30.0)
            .network_access(NetworkAccess::Denied)
            .filesystem_access(FilesystemAccess::Restricted(vec![]))
            .allow_subprocesses(false)
            .cleanup_temp_files(true)
            .build()
    }

    /// 获取所有预设配置的名称
    pub fn list_presets() -> Vec<&'static str> {
        vec![
            "default",
            "strict_security",
            "development",
            "production",
            "testing",
            "high_performance",
            "lightweight",
        ]
    }

    /// 根据名称获取预设配置
    pub fn get_preset(name: &str) -> Option<SandboxConfig> {
        match name {
            "default" => Some(Self::default()),
            "strict_security" => Some(Self::strict_security()),
            "development" => Some(Self::development()),
            "production" => Some(Self::production()),
            "testing" => Some(Self::testing()),
            "high_performance" => Some(Self::high_performance()),
            "lightweight" => Some(Self::lightweight()),
            _ => None,
        }
    }
}

/// 配置验证器
/// 
/// 提供配置验证功能
pub struct ConfigValidator;

impl ConfigValidator {
    /// 验证配置的完整性
    pub fn validate(config: &SandboxConfig) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        // 验证超时时间
        if config.timeout_seconds == 0 {
            errors.push("超时时间必须大于0".to_string());
        }
        if config.timeout_seconds > 3600 {
            errors.push("超时时间不应超过1小时".to_string());
        }

        // 验证内存限制
        if config.max_memory_mb == 0 {
            errors.push("内存限制必须大于0".to_string());
        }
        if config.max_memory_mb > 8192 {
            errors.push("内存限制不应超过8GB".to_string());
        }

        // 验证 CPU 限制
        if config.max_cpu_percent <= 0.0 {
            errors.push("CPU使用率限制必须大于0".to_string());
        }
        if config.max_cpu_percent > 100.0 {
            errors.push("CPU使用率限制不能超过100%".to_string());
        }

        // 验证工作目录
        if let Some(ref dir) = config.working_directory {
            if !dir.is_absolute() {
                errors.push("工作目录必须是绝对路径".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// 验证配置的安全性
    pub fn validate_security(config: &SandboxConfig) -> Result<(), Vec<String>> {
        let mut warnings = Vec::new();

        // 检查网络访问
        if matches!(config.network_access, NetworkAccess::Full) {
            warnings.push("允许完全网络访问可能存在安全风险".to_string());
        }

        // 检查子进程
        if config.allow_subprocesses {
            warnings.push("允许子进程可能存在安全风险".to_string());
        }

        // 检查文件系统访问
        match &config.filesystem_access {
            FilesystemAccess::ReadWrite(paths) => {
                for path in paths {
                    if path == &PathBuf::from("/") {
                        warnings.push("允许根目录写入存在严重安全风险".to_string());
                    }
                }
            }
            _ => {}
        }

        // 检查资源限制
        if config.max_memory_mb > 1024 {
            warnings.push("内存限制过高可能影响系统稳定性".to_string());
        }

        if config.timeout_seconds > 300 {
            warnings.push("超时时间过长可能导致资源占用".to_string());
        }

        if warnings.is_empty() {
            Ok(())
        } else {
            Err(warnings)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_builder() {
        let config = SandboxConfigBuilder::new()
            .timeout_seconds(60)
            .max_memory_mb(1024)
            .max_cpu_percent(80.0)
            .build();

        assert_eq!(config.timeout_seconds, 60);
        assert_eq!(config.max_memory_mb, 1024);
        assert_eq!(config.max_cpu_percent, 80.0);
    }

    #[test]
    fn test_presets() {
        let strict = ConfigPresets::strict_security();
        assert_eq!(strict.timeout_seconds, 10);
        assert!(matches!(strict.network_access, NetworkAccess::Denied));

        let dev = ConfigPresets::development();
        assert_eq!(dev.timeout_seconds, 60);
        assert!(matches!(dev.network_access, NetworkAccess::Full));
    }

    #[test]
    fn test_validation() {
        let valid_config = SandboxConfigBuilder::new()
            .timeout_seconds(30)
            .max_memory_mb(512)
            .max_cpu_percent(80.0)
            .build();

        assert!(ConfigValidator::validate(&valid_config).is_ok());

        let invalid_config = SandboxConfigBuilder::new()
            .timeout_seconds(0)
            .max_memory_mb(0)
            .max_cpu_percent(150.0)
            .build();

        assert!(ConfigValidator::validate(&invalid_config).is_err());
    }
}
