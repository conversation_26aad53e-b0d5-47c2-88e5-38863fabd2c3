//! # 工作流执行模块
//!
//! 包含工作流执行相关的接口定义和数据结构。
//! 定义了执行器接口、步骤执行结果等核心概念。

use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::time::SystemTime;
use uuid::Uuid;

use crate::error::Result;
use super::definition::WorkflowDefinition;
use super::instance::{WorkflowInstance, ExecutionContext};

/// 工作流执行器接口
/// 
/// 定义工作流执行的核心接口。
/// 实现此接口的类型可以执行工作流和管理工作流生命周期。
#[async_trait]
pub trait WorkflowExecutor: Send + Sync {
    /// 执行完整的工作流
    /// 
    /// # 参数
    /// * `instance` - 要执行的工作流实例（可变引用，会更新状态）
    /// * `definition` - 工作流定义，包含执行逻辑
    /// 
    /// # 返回值
    /// * `Ok(())` - 工作流执行成功
    /// * `Err(OrionError)` - 工作流执行失败
    async fn execute_workflow(
        &self, 
        instance: &mut WorkflowInstance, 
        definition: &WorkflowDefinition
    ) -> Result<()>;
    
    /// 执行单个工作流步骤
    /// 
    /// # 参数
    /// * `step` - 要执行的步骤定义
    /// * `context` - 执行上下文（可变引用，会更新变量和结果）
    /// 
    /// # 返回值
    /// * `Ok(StepExecutionResult)` - 步骤执行结果
    /// * `Err(OrionError)` - 步骤执行失败
    async fn execute_step(
        &self, 
        step: &super::definition::WorkflowStep, 
        context: &mut ExecutionContext
    ) -> Result<StepExecutionResult>;
    
    /// 暂停工作流执行
    /// 
    /// # 参数
    /// * `instance_id` - 要暂停的工作流实例ID
    /// 
    /// # 返回值
    /// * `Ok(())` - 暂停成功
    /// * `Err(OrionError)` - 暂停失败（如实例不存在）
    async fn pause_workflow(&self, instance_id: Uuid) -> Result<()>;
    
    /// 恢复工作流执行
    /// 
    /// # 参数
    /// * `instance_id` - 要恢复的工作流实例ID
    /// 
    /// # 返回值
    /// * `Ok(())` - 恢复成功
    /// * `Err(OrionError)` - 恢复失败（如实例不存在或状态不正确）
    async fn resume_workflow(&self, instance_id: Uuid) -> Result<()>;
    
    /// 取消工作流执行
    /// 
    /// # 参数
    /// * `instance_id` - 要取消的工作流实例ID
    /// 
    /// # 返回值
    /// * `Ok(())` - 取消成功
    /// * `Err(OrionError)` - 取消失败（如实例不存在）
    async fn cancel_workflow(&self, instance_id: Uuid) -> Result<()>;
}

/// 步骤执行结果
/// 
/// 包含单个步骤执行的完整结果信息。
/// 用于记录执行状态、结果数据、错误信息等。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StepExecutionResult {
    /// 执行的步骤ID
    pub step_id: Uuid,
    
    /// 步骤执行状态
    pub status: StepStatus,
    
    /// 步骤执行结果数据（JSON格式）
    /// 成功时包含步骤的输出数据
    pub result: Option<serde_json::Value>,
    
    /// 错误信息
    /// 失败时包含详细的错误描述
    pub error: Option<String>,
    
    /// 步骤执行耗时（毫秒）
    pub execution_time_ms: u64,
    
    /// 重试次数
    /// 记录此步骤被重试的次数
    pub retry_count: u32,
    
    /// 步骤开始执行时间
    pub started_at: SystemTime,
    
    /// 步骤完成时间（可选）
    /// 只有在步骤完成或失败时才有值
    pub completed_at: Option<SystemTime>,
}

/// 步骤状态枚举
/// 
/// 定义单个步骤的执行状态。
/// 状态转换遵循特定的生命周期规则。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum StepStatus {
    /// 待执行状态
    /// 步骤已准备好但尚未开始执行
    Pending,
    
    /// 执行中状态
    /// 步骤正在执行
    Running,
    
    /// 已完成状态
    /// 步骤成功执行完成
    Completed,
    
    /// 失败状态
    /// 步骤执行失败
    Failed,
    
    /// 跳过状态
    /// 步骤因条件不满足而被跳过
    Skipped,
    
    /// 重试中状态
    /// 步骤正在重试执行
    Retrying,
}
