//! # 工作流命令类型定义
//!
//! 定义工作流模块中使用的所有命令结构和类型。

use clap::{Args, Subcommand};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 工作流命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct WorkflowCommand {
    #[command(subcommand)]
    pub action: WorkflowAction,
}

/// 工作流操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum WorkflowAction {
    /// 列出工作流
    List(ListWorkflows),
    /// 创建工作流
    Create(CreateWorkflow),
    /// 执行工作流
    Run(RunWorkflow),
    /// 显示工作流详情
    Show(ShowWorkflow),
    /// 停止工作流
    Stop(StopWorkflow),
    /// 暂停工作流
    Pause(PauseWorkflow),
    /// 恢复工作流
    Resume(ResumeWorkflow),
    /// 删除工作流
    Delete(DeleteWorkflow),
    /// 导入工作流
    Import(ImportWorkflow),
    /// 导出工作流
    Export(ExportWorkflow),
    /// 验证工作流
    Validate(ValidateWorkflow),
    /// 工作流模板
    Template(TemplateWorkflow),
}

/// 列出工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ListWorkflows {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 输出格式
    #[arg(short, long, default_value = "table", value_parser = ["table", "json", "yaml"])]
    pub format: String,

    /// 过滤状态
    #[arg(long)]
    pub status: Option<String>,

    /// 显示实例而不是定义
    #[arg(long)]
    pub instances: bool,

    /// 限制结果数量
    #[arg(short, long)]
    pub limit: Option<usize>,
}

/// 创建工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct CreateWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流名称
    #[arg(short, long)]
    pub name: String,

    /// 工作流描述
    #[arg(short, long)]
    pub description: Option<String>,

    /// 工作流文件路径
    #[arg(short, long)]
    pub file: Option<PathBuf>,

    /// 是否使用交互模式
    #[arg(short, long)]
    pub interactive: bool,

    /// 工作流模板
    #[arg(short, long, value_parser = ["simple", "llm-chain", "data-processing", "automation"])]
    pub template: Option<String>,
}

/// 执行工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct RunWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流ID 或名称
    pub workflow: String,

    /// 输入参数（JSON 格式）
    #[arg(short, long)]
    pub input: Option<String>,

    /// 输入参数文件
    #[arg(long)]
    pub input_file: Option<PathBuf>,

    /// 是否异步执行
    #[arg(short, long)]
    pub async_run: bool,

    /// 是否监控执行过程
    #[arg(short, long, default_value_t = true)]
    pub monitor: bool,

    /// 输出格式
    #[arg(long, default_value = "text", value_parser = ["text", "json", "yaml"])]
    pub format: String,

    /// 超时时间（秒）
    #[arg(long)]
    pub timeout: Option<u64>,
}

/// 显示工作流详情
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ShowWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流ID 或名称
    pub workflow: String,

    /// 输出格式
    #[arg(short, long, default_value = "yaml", value_parser = ["yaml", "json", "text"])]
    pub format: String,

    /// 是否显示实例详情
    #[arg(long)]
    pub instance: bool,

    /// 是否显示步骤详情
    #[arg(long, default_value_t = true)]
    pub steps: bool,
}

/// 停止工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct StopWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流实例ID
    pub instance_id: String,

    /// 是否强制停止
    #[arg(short, long)]
    pub force: bool,
}

/// 暂停工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct PauseWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流实例ID
    pub instance_id: String,
}

/// 恢复工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ResumeWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流实例ID
    pub instance_id: String,
}

/// 删除工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct DeleteWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流ID 或名称
    pub workflow: String,

    /// 是否确认删除
    #[arg(short, long)]
    pub yes: bool,

    /// 是否删除所有实例
    #[arg(long)]
    pub all_instances: bool,
}

/// 导入工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ImportWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流文件路径
    pub file: PathBuf,

    /// 是否覆盖已存在的工作流
    #[arg(short, long)]
    pub force: bool,

    /// 验证工作流
    #[arg(short, long, default_value_t = true)]
    pub validate: bool,
}

/// 导出工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ExportWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流ID 或名称
    pub workflow: String,

    /// 输出文件路径
    #[arg(short, long)]
    pub output: PathBuf,

    /// 输出格式
    #[arg(short, long, default_value = "yaml", value_parser = ["yaml", "json"])]
    pub format: String,
}

/// 验证工作流
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ValidateWorkflow {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,

    /// 工作流文件路径
    pub file: PathBuf,

    /// 是否显示详细信息
    #[arg(short, long)]
    pub verbose: bool,
}

/// 工作流模板
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct TemplateWorkflow {
    /// 模板类型
    #[arg(value_parser = ["simple", "llm-chain", "data-processing", "automation", "list"])]
    pub template_type: String,

    /// 输出文件路径
    #[arg(short, long)]
    pub output: Option<PathBuf>,

    /// 工作流名称
    #[arg(short, long)]
    pub name: Option<String>,
}