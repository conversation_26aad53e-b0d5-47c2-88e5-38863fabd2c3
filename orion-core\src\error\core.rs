//! # 核心错误类型模块
//!
//! 包含系统级别的核心错误类型，这些错误通常表示严重的系统问题。
//! 包括初始化错误、内部错误等关键系统错误。

use thiserror::Error;

/// 核心系统错误枚举
/// 
/// 这些错误表示系统级别的严重问题，通常需要立即处理。
/// 大多数核心错误都是不可恢复的，需要系统重启或重新初始化。
#[derive(Error, Debug, Clone)]
pub enum CoreError {
    /// 系统初始化错误
    /// 
    /// 当系统组件初始化失败时抛出此错误。
    /// 这通常是由于配置问题、依赖缺失或环境问题导致的。
    #[error("系统初始化失败: {message}")]
    InitializationError {
        /// 详细错误信息
        message: String,
    },

    /// 内部系统错误
    /// 
    /// 表示系统内部出现了不应该发生的错误状态。
    /// 这通常是程序逻辑错误或不一致状态导致的。
    #[error("内部系统错误: {message}")]
    InternalError {
        /// 错误描述
        message: String,
    },
}

impl CoreError {
    /// 创建初始化错误
    pub fn initialization_failed(message: impl Into<String>) -> Self {
        Self::InitializationError {
            message: message.into(),
        }
    }

    /// 创建内部错误
    pub fn internal_error(message: impl Into<String>) -> Self {
        Self::InternalError {
            message: message.into(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_core_error_creation() {
        let error = CoreError::initialization_failed("测试初始化失败");
        assert!(error.to_string().contains("系统初始化失败"));
        assert!(error.to_string().contains("测试初始化失败"));
    }

    #[test]
    fn test_internal_error() {
        let error = CoreError::internal_error("测试内部错误");
        assert!(error.to_string().contains("内部系统错误"));
        assert!(error.to_string().contains("测试内部错误"));
    }
}
