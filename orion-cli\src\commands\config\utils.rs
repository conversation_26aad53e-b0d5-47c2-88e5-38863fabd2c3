//! # 配置模块工具函数
//!
//! 提供配置管理中使用的共享工具函数。

/// 敏感信息处理工具
pub trait SecretKeyChecker {
    /// 判断是否为敏感配置键
    fn is_secret_key(&self, key: &str) -> bool {
        let secret_keys = [
            "api_key", "secret", "password", "token", 
            "private_key", "secret_key", "access_token",
            "refresh_token", "auth_token", "bearer_token"
        ];
        
        let key_lower = key.to_lowercase();
        // 使用更精确的匹配，避免误判
        secret_keys.iter().any(|&secret| {
            key_lower == secret || 
            key_lower.ends_with(&format!("_{}", secret)) ||
            key_lower.starts_with(&format!("{}_", secret)) ||
            (secret == "key" && (key_lower.ends_with("_key") || key_lower.starts_with("key_")))
        })
    }
    
    /// 隐藏 JSON 中的敏感信息
    fn hide_secrets(&self, value: &mut serde_json::Value) {
        match value {
            serde_json::Value::Object(map) => {
                for (key, val) in map.iter_mut() {
                    if self.is_secret_key(key) {
                        *val = serde_json::Value::String("***".to_string());
                    } else {
                        self.hide_secrets(val);
                    }
                }
            }
            serde_json::Value::Array(arr) => {
                for item in arr.iter_mut() {
                    self.hide_secrets(item);
                }
            }
            _ => {}
        }
    }
    
    /// 隐藏 YAML 中的敏感信息
    fn hide_secrets_yaml(&self, value: &mut serde_yaml::Value) {
        match value {
            serde_yaml::Value::Mapping(map) => {
                for (key, val) in map.iter_mut() {
                    if let serde_yaml::Value::String(key_str) = key {
                        if self.is_secret_key(key_str) {
                            *val = serde_yaml::Value::String("***".to_string());
                        } else {
                            self.hide_secrets_yaml(val);
                        }
                    }
                }
            }
            serde_yaml::Value::Sequence(seq) => {
                for item in seq.iter_mut() {
                    self.hide_secrets_yaml(item);
                }
            }
            _ => {}
        }
    }
    
    /// 隐藏 TOML 中的敏感信息
    fn hide_secrets_toml(&self, content: &str) -> String {
        let lines: Vec<&str> = content.lines().collect();
        let mut result = Vec::new();
        
        for line in lines {
            if let Some(eq_pos) = line.find('=') {
                let key = line[..eq_pos].trim();
                if self.is_secret_key(key) {
                    result.push(format!("{} = \"***\"", key));
                } else {
                    result.push(line.to_string());
                }
            } else {
                result.push(line.to_string());
            }
        }
        
        result.join("\n")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    struct TestChecker;
    impl SecretKeyChecker for TestChecker {}
    
    #[test]
    fn test_is_secret_key() {
        let checker = TestChecker;
        
        assert!(checker.is_secret_key("api_key"));
        assert!(checker.is_secret_key("secret_key"));
        assert!(checker.is_secret_key("password"));
        assert!(!checker.is_secret_key("normal_key"));
        assert!(!checker.is_secret_key("config_value"));
    }
}