#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Orion CLI UI 测试脚本
测试新的 UI 系统是否工作正常
"""

import subprocess
import sys
import os

def test_orion_ui():
    """测试 Orion CLI UI 系统"""
    
    print("🚀 开始测试 Orion CLI UI 系统...")
    print("-" * 50)
    
    # 测试帮助命令
    print("1. 测试帮助命令...")
    try:
        result = subprocess.run(
            ["cargo", "run", "--bin", "orion", "--", "--help"],
            cwd="E:/Orion",
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ 帮助命令测试成功")
            print("输出预览:")
            print(result.stdout[:200] + "..." if len(result.stdout) > 200 else result.stdout)
        else:
            print("❌ 帮助命令测试失败")
            print("错误:", result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ 帮助命令测试超时")
    except Exception as e:
        print(f"💥 测试过程中出错: {e}")
    
    print()
    
    # 测试版本命令
    print("2. 测试版本命令...")
    try:
        result = subprocess.run(
            ["cargo", "run", "--bin", "orion", "--", "version"],
            cwd="E:/Orion",
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ 版本命令测试成功")
            print("输出:", result.stdout.strip())
        else:
            print("❌ 版本命令测试失败")
            print("错误:", result.stderr)
    except subprocess.TimeoutExpired:
        print("⏰ 版本命令测试超时")
    except Exception as e:
        print(f"💥 测试过程中出错: {e}")
    
    print()
    print("-" * 50)
    print("🎯 UI 系统测试完成")

def main():
    """主函数"""
    print("Orion CLI UI 测试工具")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists("E:/Orion/Cargo.toml"):
        print("❌ 错误: 请确保在 Orion 项目根目录运行此脚本")
        sys.exit(1)
    
    test_orion_ui()

if __name__ == "__main__":
    main()