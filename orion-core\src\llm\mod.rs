//! # 可插拔 LLM 引擎
//!
//! 提供模型无关的 LLM 接口，支持多种 LLM 提供商和本地模型。
//! 实现智能上下文管理和流式响应处理。
//!
//! ## 模块结构
//!
//! - [`types`] - 核心数据类型定义
//! - [`provider`] - LLM 提供商接口定义
//! - [`providers`] - 各种 LLM 提供商的具体实现
//! - [`engine`] - LLM 引擎核心实现
//!
//! ## 使用示例
//!
//! ```rust,no_run
//! use orion_core::llm::{LlmEngine, ModelConfig, ModelParameters, LlmMessage, LlmRequest};
//! use orion_core::llm::providers::OpenAiProvider;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // 创建 LLM 引擎
//!     let engine = LlmEngine::new();
//!
//!     // 配置 OpenAI 提供商（需要真实的 API 密钥）
//!     let config = ModelConfig {
//!         name: "gpt-4".to_string(),
//!         provider: "openai".to_string(),
//!         endpoint: None,
//!         api_key: Some("your-actual-api-key".to_string()),
//!         parameters: ModelParameters::default(),
//!         timeout_seconds: 30,
//!         max_retries: 3,
//!         streaming: false,
//!     };
//!
//!     // 注册提供商
//!     let provider = Box::new(OpenAiProvider::new(config)?);
//!     engine.register_provider("openai".to_string(), provider).await?;
//!
//!     // 创建请求
//!     let messages = vec![
//!         LlmMessage::system("You are a helpful assistant."),
//!         LlmMessage::user("Hello, how are you?"),
//!     ];
//!     let request = LlmRequest::new(messages);
//!
//!     // 发送请求
//!     let response = engine.complete(request).await?;
//!     println!("Response: {}", response.content);
//!
//!     Ok(())
//! }
//! ```
//!
//! ## 基本类型使用示例
//!
//! ```rust
//! use orion_core::llm::{LlmMessage, LlmRequest, ModelConfig, ModelParameters};
//!
//! // 创建消息
//! let system_msg = LlmMessage::system("You are a helpful assistant.");
//! let user_msg = LlmMessage::user("What is Rust?");
//! let assistant_msg = LlmMessage::assistant("Rust is a systems programming language.");
//!
//! // 创建请求
//! let messages = vec![system_msg, user_msg];
//! let request = LlmRequest::new(messages);
//!
//! // 创建模型配置
//! let config = ModelConfig {
//!     name: "gpt-4".to_string(),
//!     provider: "openai".to_string(),
//!     endpoint: None,
//!     api_key: None,
//!     parameters: ModelParameters::default(),
//!     timeout_seconds: 30,
//!     max_retries: 3,
//!     streaming: false,
//! };
//!
//! assert_eq!(config.name, "gpt-4");
//! assert_eq!(config.provider, "openai");
//! ```

pub mod types;
pub mod provider;
pub mod providers;
pub mod engine;

// 重新导出核心类型，保持 API 兼容性
pub use types::{
    ModelConfig, ModelParameters, MessageRole, LlmMessage, LlmRequest, LlmResponse,
    ToolDefinition, ToolCall, TokenUsage, FinishReason, StreamChunk,
};

// 重新导出提供商接口
pub use provider::LlmProvider;

// 重新导出提供商实现
pub use providers::{OpenAiProvider, KimiProvider};

// 重新导出引擎
pub use engine::LlmEngine;

// 添加测试模块
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_message_creation() {
        let msg = LlmMessage::user("Hello, world!");
        assert_eq!(msg.role, MessageRole::User);
        assert_eq!(msg.content, "Hello, world!");
    }

    #[test]
    fn test_request_creation() {
        let messages = vec![
            LlmMessage::system("You are a helpful assistant."),
            LlmMessage::user("Hello!"),
        ];

        let request = LlmRequest::new(messages.clone())
            .with_parameters(ModelParameters::default())
            .with_streaming(true);

        assert_eq!(request.messages.len(), 2);
        assert!(request.parameters.is_some());
        assert_eq!(request.streaming, Some(true));
    }

    #[test]
    fn test_token_estimation() {
        let provider = OpenAiProvider::new(ModelConfig {
            name: "gpt-4".to_string(),
            provider: "openai".to_string(),
            endpoint: None,
            api_key: Some("test".to_string()),
            parameters: ModelParameters::default(),
            timeout_seconds: 30,
            max_retries: 3,
            streaming: false,
        }).unwrap();

        let tokens = provider.estimate_tokens("Hello, world!");
        assert!(tokens > 0);

        let chinese_tokens = provider.estimate_tokens("你好，世界！");
        assert!(chinese_tokens > 0);
    }

    #[tokio::test]
    async fn test_llm_engine() {
        let engine = LlmEngine::new();

        // 测试空引擎
        assert!(engine.list_providers().await.is_empty());
        assert!(engine.get_default_provider().await.is_none());

        // 注册提供商
        let config = ModelConfig {
            name: "gpt-4".to_string(),
            provider: "openai".to_string(),
            endpoint: None,
            api_key: Some("test".to_string()),
            parameters: ModelParameters::default(),
            timeout_seconds: 30,
            max_retries: 3,
            streaming: false,
        };

        let provider = Box::new(OpenAiProvider::new(config).unwrap());
        engine.register_provider("openai".to_string(), provider).await.unwrap();

        assert_eq!(engine.list_providers().await, vec!["openai"]);
        assert_eq!(engine.get_default_provider().await, Some("openai".to_string()));
    }
}