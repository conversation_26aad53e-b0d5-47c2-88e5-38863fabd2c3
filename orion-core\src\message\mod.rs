//! # 消息系统模块
//!
//! 本模块实现了 h2A 启发的异步消息队列系统，提供高性能的消息传递、路由和处理功能。
//! 
//! ## 模块结构
//! 
//! - `types` - 核心类型定义（Message、MessagePayload、MessagePriority 等）
//! - `stats` - 统计信息管理（MessageStats、StatsManager）
//! - `handler` - 消息处理器接口和实现（MessageHandler trait）
//! - `bus` - 消息总线（MessageBus）
//! - `event_loop` - 事件循环（EventLoop）
//! 
//! ## 主要特性
//! 
//! - **异步消息传递** - 基于 Tokio 的高性能异步消息队列
//! - **优先级调度** - 支持消息优先级，实现实时 Steering 机制
//! - **Agent 管理** - 支持 Agent 注册、注销和直接路由
//! - **消息处理器** - 灵活的消息处理器系统，支持链式处理
//! - **统计监控** - 完整的性能统计和监控功能
//! - **错误恢复** - 健壮的错误处理和恢复机制
//! 
//! ## 使用示例
//! 
//! ```rust
//! use orion_core::message::{
//!     MessageBus, EventLoop, Message, MessagePayload, MessagePriority,
//!     EchoHandler, LoggingHandler
//! };
//! use std::sync::Arc;
//! 
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     // 创建消息总线
//!     let bus = Arc::new(MessageBus::new());
//!     bus.start().await?;
//!     
//!     // 创建事件循环并注册处理器
//!     let event_loop = EventLoop::new(bus.clone());
//!     event_loop.register_handler(Box::new(EchoHandler::new("echo".to_string()))).await;
//!     event_loop.register_handler(Box::new(LoggingHandler::new("log".to_string(), "info".to_string()))).await;
//!     event_loop.start().await?;
//!     
//!     // 发送消息
//!     let message = Message::new(
//!         "sender".to_string(),
//!         "receiver".to_string(),
//!         MessagePayload::Text("Hello, World!".to_string()),
//!     );
//!     bus.send_message(message).await?;
//!     
//!     // 运行事件循环（在实际应用中通常在单独的任务中运行）
//!     // event_loop.run().await?;
//!     
//!     Ok(())
//! }
//! ```

// 导入必要的标准库类型
use std::sync::Arc;

// 子模块声明
pub mod types;
pub mod stats;
pub mod handler;
pub mod bus;
pub mod event_loop;

// 重新导出核心类型，保持 API 兼容性
pub use types::{
    AgentId,
    Message,
    MessagePayload,
    MessagePriority,
};

// 重新导出统计相关类型
pub use stats::{
    MessageStats,
    StatsManager,
    PerformanceMeasurement,
};

// 重新导出处理器相关类型
pub use handler::{
    MessageHandler,
    EchoHandler,
    LoggingHandler,
    RoutingHandler,
    FilterHandler,
};

// 重新导出消息总线
pub use bus::MessageBus;

// 重新导出事件循环
pub use event_loop::{
    EventLoop,
    EventLoopBuilder,
};

// 重新导出测试模块（仅在测试时可用）
#[cfg(test)]
pub mod tests;

/// 消息系统的便捷构建器
/// 
/// 提供一站式的消息系统创建和配置功能
pub struct MessageSystemBuilder {
    stats_manager: Option<Arc<StatsManager>>,
    handlers: Vec<Box<dyn MessageHandler>>,
    receive_timeout: std::time::Duration,
}

impl MessageSystemBuilder {
    /// 创建新的消息系统构建器
    /// 
    /// # 返回值
    /// 
    /// 返回新的构建器实例
    pub fn new() -> Self {
        Self {
            stats_manager: None,
            handlers: Vec::new(),
            receive_timeout: std::time::Duration::from_millis(100),
        }
    }
    
    /// 设置自定义统计管理器
    /// 
    /// # 参数
    /// 
    /// * `stats_manager` - 统计管理器
    /// 
    /// # 返回值
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn with_stats_manager(mut self, stats_manager: Arc<StatsManager>) -> Self {
        self.stats_manager = Some(stats_manager);
        self
    }
    
    /// 添加消息处理器
    /// 
    /// # 参数
    /// 
    /// * `handler` - 要添加的处理器
    /// 
    /// # 返回值
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn with_handler(mut self, handler: Box<dyn MessageHandler>) -> Self {
        self.handlers.push(handler);
        self
    }
    
    /// 设置事件循环接收超时时间
    /// 
    /// # 参数
    /// 
    /// * `timeout` - 超时时间
    /// 
    /// # 返回值
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn with_receive_timeout(mut self, timeout: std::time::Duration) -> Self {
        self.receive_timeout = timeout;
        self
    }
    
    /// 构建完整的消息系统
    /// 
    /// # 返回值
    /// 
    /// 返回包含消息总线和事件循环的元组
    pub async fn build(self) -> (Arc<MessageBus>, EventLoop) {
        // 创建消息总线
        let bus = if let Some(stats_manager) = self.stats_manager {
            Arc::new(MessageBus::with_stats_manager(stats_manager))
        } else {
            Arc::new(MessageBus::new())
        };
        
        // 创建事件循环
        let mut event_loop_builder = EventLoopBuilder::new(bus.clone())
            .with_receive_timeout(self.receive_timeout);
        
        // 添加所有处理器
        for handler in self.handlers {
            event_loop_builder = event_loop_builder.with_handler(handler);
        }
        
        let event_loop = event_loop_builder.build().await;
        
        (bus, event_loop)
    }
}

impl Default for MessageSystemBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 创建默认的消息系统
/// 
/// 这是一个便捷函数，创建包含基本处理器的消息系统
/// 
/// # 返回值
/// 
/// 返回包含消息总线和事件循环的元组
pub async fn create_default_message_system() -> (Arc<MessageBus>, EventLoop) {
    MessageSystemBuilder::new()
        .with_handler(Box::new(LoggingHandler::new(
            "default_logger".to_string(),
            "info".to_string(),
        )))
        .build()
        .await
}

/// 创建用于测试的消息系统
/// 
/// 包含回声处理器和日志处理器，适用于测试和调试
/// 
/// # 返回值
/// 
/// 返回包含消息总线和事件循环的元组
pub async fn create_test_message_system() -> (Arc<MessageBus>, EventLoop) {
    MessageSystemBuilder::new()
        .with_handler(Box::new(EchoHandler::new("test_echo".to_string())))
        .with_handler(Box::new(LoggingHandler::new(
            "test_logger".to_string(),
            "debug".to_string(),
        )))
        .with_receive_timeout(std::time::Duration::from_millis(10))
        .build()
        .await
}

/// 消息系统版本信息
pub const MESSAGE_SYSTEM_VERSION: &str = "1.0.0";

/// 消息系统功能特性标志
pub mod features {
    /// 是否启用统计功能
    pub const STATS_ENABLED: bool = true;
    
    /// 是否启用性能测量
    pub const PERFORMANCE_MEASUREMENT_ENABLED: bool = true;
    
    /// 是否启用调试日志
    pub const DEBUG_LOGGING_ENABLED: bool = cfg!(debug_assertions);
    
    /// 默认的最大延迟样本数
    pub const DEFAULT_MAX_LATENCY_SAMPLES: usize = 1000;
    
    /// 默认的消息接收超时时间（毫秒）
    pub const DEFAULT_RECEIVE_TIMEOUT_MS: u64 = 100;
}

/// 消息系统配置常量
pub mod config {
    use std::time::Duration;
    
    /// 默认的消息接收超时时间
    pub const DEFAULT_RECEIVE_TIMEOUT: Duration = Duration::from_millis(100);
    
    /// 默认的消息过期时间
    pub const DEFAULT_MESSAGE_EXPIRY: Duration = Duration::from_secs(300); // 5 分钟
    
    /// 高优先级消息的默认过期时间
    pub const HIGH_PRIORITY_MESSAGE_EXPIRY: Duration = Duration::from_secs(60); // 1 分钟
    
    /// 低优先级消息的默认过期时间
    pub const LOW_PRIORITY_MESSAGE_EXPIRY: Duration = Duration::from_secs(600); // 10 分钟
}
