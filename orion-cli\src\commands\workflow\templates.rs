//! # 工作流模板功能
//!
//! 实现工作流模板的创建和管理功能。

use crate::error::{CliError, Result};
use crate::commands::workflow::types::TemplateWorkflow;
use orion_core::workflow::{
    WorkflowDefinition, WorkflowStep, WorkflowParameter, WorkflowConfig,
    StepType, StepConfig
};
use std::collections::HashMap;
use uuid::Uuid;

impl TemplateWorkflow {
    /// 执行模板命令
    pub async fn execute(&self) -> Result<()> {
        match self.template_type.as_str() {
            "list" => {
                self.list_templates();
                Ok(())
            }
            _ => {
                let workflow_def = self.create_workflow_definition()?;
                
                if let Some(output_path) = &self.output {
                    // 保存到文件
                    let content = serde_yaml::to_string(&workflow_def)
                        .map_err(|e| CliError::SerializationError {
                            error: format!("序列化工作流定义失败: {}", e),
                        })?;

                    tokio::fs::write(output_path, content).await
                        .map_err(|e| CliError::IoError {
                            error: format!("写入文件失败: {}", e),
                        })?;

                    println!("✅ 工作流模板已保存到: {}", output_path.display());
                } else {
                    // 输出到控制台
                    let yaml = serde_yaml::to_string(&workflow_def)
                        .map_err(|e| CliError::SerializationError {
                            error: format!("序列化工作流定义失败: {}", e),
                        })?;
                    println!("{}", yaml);
                }
                
                Ok(())
            }
        }
    }
    
    /// 列出可用模板
    fn list_templates(&self) {
        println!("📋 可用的工作流模板:");
        println!();
        println!("🔧 simple        - 简单的单步骤工作流");
        println!("🤖 llm-chain     - LLM 调用链工作流");
        println!("📊 data-processing - 数据处理工作流");
        println!("⚙️  automation    - 自动化任务工作流");
        println!();
        println!("使用示例:");
        println!("  orion workflow template simple --output my-workflow.yaml");
        println!("  orion workflow template llm-chain --name \"我的LLM链\" --output llm-workflow.yaml");
    }

    /// 创建工作流定义
    pub fn create_workflow_definition(&self) -> Result<WorkflowDefinition> {
        let name = self.name.as_deref().unwrap_or("示例工作流");

        match self.template_type.as_str() {
            "simple" => self.create_simple_template(name),
            "llm-chain" => self.create_llm_chain_template(name),
            "data-processing" => self.create_data_processing_template(name),
            "automation" => self.create_automation_template(name),
            _ => Err(CliError::InvalidArgument {
                error: format!("未知的模板类型: {}", self.template_type),
            }),
        }
    }
    
    /// 创建简单模板
    fn create_simple_template(&self, name: &str) -> Result<WorkflowDefinition> {
        let step_id = Uuid::new_v4();

        Ok(WorkflowDefinition {
            id: Uuid::new_v4(),
            name: name.to_string(),
            version: "1.0.0".to_string(),
            description: "简单的单步骤工作流示例".to_string(),
            tags: vec!["simple".to_string(), "example".to_string()],
            input_parameters: vec![
                WorkflowParameter {
                    name: "input_text".to_string(),
                    param_type: "String".to_string(),
                    description: "输入文本".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            output_parameters: vec![
                WorkflowParameter {
                    name: "result".to_string(),
                    param_type: "String".to_string(),
                    description: "处理结果".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            steps: vec![
                WorkflowStep {
                    id: step_id.clone(),
                    name: "处理步骤".to_string(),
                    step_type: StepType::ToolCall { tool_name: "default_tool".to_string() },
                    description: "处理输入文本".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("text".to_string(), "${input_text}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("result".to_string(), "${output}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(30),
                    next_steps: HashMap::new(),
                    error_handler: None,
                }
            ],
            initial_step_id: step_id,
            config: WorkflowConfig::default(),
            created_at: std::time::SystemTime::now(),
            updated_at: std::time::SystemTime::now(),
        })
    }
    
    /// 创建 LLM 链模板
    fn create_llm_chain_template(&self, name: &str) -> Result<WorkflowDefinition> {
        let step1_id = Uuid::new_v4();
        let step2_id = Uuid::new_v4();
        let step3_id = Uuid::new_v4();

        Ok(WorkflowDefinition {
            id: Uuid::new_v4(),
            name: name.to_string(),
            version: "1.0.0".to_string(),
            description: "LLM 调用链工作流，包含分析、处理和总结步骤".to_string(),
            tags: vec!["llm".to_string(), "chain".to_string(), "ai".to_string()],
            input_parameters: vec![
                WorkflowParameter {
                    name: "user_query".to_string(),
                    param_type: "String".to_string(),
                    description: "用户查询".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            output_parameters: vec![
                WorkflowParameter {
                    name: "final_response".to_string(),
                    param_type: "String".to_string(),
                    description: "最终响应".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            steps: vec![
                WorkflowStep {
                    id: step1_id.clone(),
                    name: "分析查询".to_string(),
                    step_type: StepType::LlmCall {
                        model_name: "gpt-4".to_string(),
                        prompt_template: "分析以下用户查询的意图: ${user_query}".to_string()
                    },
                    description: "分析用户查询的意图和要求".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("prompt".to_string(), "分析以下用户查询的意图: ${user_query}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("analysis".to_string(), "${response}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(60),
                    next_steps: {
                        let mut map = HashMap::new();
                        map.insert("default".to_string(), step2_id.clone());
                        map
                    },
                    error_handler: None,
                },
                WorkflowStep {
                    id: step2_id.clone(),
                    name: "生成回答".to_string(),
                    step_type: StepType::LlmCall {
                        model_name: "gpt-4".to_string(),
                        prompt_template: "基于分析结果生成详细回答: ${analysis}".to_string()
                    },
                    description: "基于分析结果生成详细回答".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("prompt".to_string(), "基于分析结果 '${analysis}' 为用户查询 '${user_query}' 生成详细回答".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("detailed_answer".to_string(), "${response}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(60),
                    next_steps: {
                        let mut map = HashMap::new();
                        map.insert("default".to_string(), step3_id.clone());
                        map
                    },
                    error_handler: None,
                },
                WorkflowStep {
                    id: step3_id.clone(),
                    name: "总结优化".to_string(),
                    step_type: StepType::LlmCall {
                        model_name: "gpt-4".to_string(),
                        prompt_template: "总结优化回答: ${response}".to_string()
                    },
                    description: "总结和优化最终回答".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("prompt".to_string(), "总结和优化以下回答，使其更加简洁明确: ${detailed_answer}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("final_response".to_string(), "${response}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(60),
                    next_steps: HashMap::new(),
                    error_handler: None,
                }
            ],
            initial_step_id: step1_id,
            config: WorkflowConfig::default(),
            created_at: std::time::SystemTime::now(),
            updated_at: std::time::SystemTime::now(),
        })
    }
    
    /// 创建数据处理模板
    fn create_data_processing_template(&self, name: &str) -> Result<WorkflowDefinition> {
        let step1_id = Uuid::new_v4();
        let step2_id = Uuid::new_v4();
        let step3_id = Uuid::new_v4();
        
        Ok(WorkflowDefinition {
            id: Uuid::new_v4(),
            name: name.to_string(),
            version: "1.0.0".to_string(),
            description: "数据处理工作流，包含读取、转换和保存步骤".to_string(),
            tags: vec!["data".to_string(), "processing".to_string(), "etl".to_string()],
            input_parameters: vec![
                WorkflowParameter {
                    name: "input_file".to_string(),
                    param_type: "String".to_string(),
                    description: "输入文件路径".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                WorkflowParameter {
                    name: "output_file".to_string(),
                    param_type: "String".to_string(),
                    description: "输出文件路径".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            output_parameters: vec![
                WorkflowParameter {
                    name: "processed_records".to_string(),
                    param_type: "Integer".to_string(),
                    description: "处理的记录数".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            steps: vec![
                WorkflowStep {
                    id: step1_id.clone(),
                    name: "读取数据".to_string(),
                    step_type: StepType::ToolCall { tool_name: "default_tool".to_string() },
                    description: "从输入文件读取数据".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("file_path".to_string(), "${input_file}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("raw_data".to_string(), "${content}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(120),
                    next_steps: HashMap::new(),
                    error_handler: None,
                },
                WorkflowStep {
                    id: step2_id.clone(),
                    name: "转换数据".to_string(),
                    step_type: StepType::Transform {
                        script: "// 数据转换脚本".to_string(),
                        language: "javascript".to_string(),
                    },
                    description: "转换和清理数据".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("data".to_string(), "${raw_data}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("transformed_data".to_string(), "${result}".to_string());
                        map.insert("record_count".to_string(), "${count}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(300),
                    next_steps: HashMap::new(),
                    error_handler: None,
                },
                WorkflowStep {
                    id: step3_id.clone(),
                    name: "保存数据".to_string(),
                    step_type: StepType::ToolCall { tool_name: "default_tool".to_string() },
                    description: "将处理后的数据保存到输出文件".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("file_path".to_string(), "${output_file}".to_string());
                        map.insert("content".to_string(), "${transformed_data}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("processed_records".to_string(), "${record_count}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(120),
                    next_steps: HashMap::new(),
                    error_handler: None,
                }
            ],
            initial_step_id: step1_id,
            config: WorkflowConfig::default(),
            created_at: std::time::SystemTime::now(),
            updated_at: std::time::SystemTime::now(),
        })
    }
    
    /// 创建自动化模版
    fn create_automation_template(&self, name: &str) -> Result<WorkflowDefinition> {
        let step1_id = Uuid::new_v4();
        let step2_id = Uuid::new_v4();
        let step3_id = Uuid::new_v4();
        let step4_id = Uuid::new_v4();
        
        Ok(WorkflowDefinition {
            id: Uuid::new_v4(),
            name: name.to_string(),
            version: "1.0.0".to_string(),
            description: "自动化任务工作流，包含条件判断和循环处理".to_string(),
            tags: vec!["automation".to_string(), "task".to_string(), "scheduled".to_string()],
            input_parameters: vec![
                WorkflowParameter {
                    name: "task_list".to_string(),
                    param_type: "Array".to_string(),
                    description: "任务列表".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                WorkflowParameter {
                    name: "max_retries".to_string(),
                    param_type: "Integer".to_string(),
                    description: "最大重试次数".to_string(),
                    required: false,
                    default_value: None,
                    constraints: None,
                }
            ],
            output_parameters: vec![
                WorkflowParameter {
                    name: "completed_tasks".to_string(),
                    param_type: "Array".to_string(),
                    description: "已完成的任务".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                WorkflowParameter {
                    name: "failed_tasks".to_string(),
                    param_type: "Array".to_string(),
                    description: "失败的任务".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                }
            ],
            steps: vec![
                WorkflowStep {
                    id: step1_id.clone(),
                    name: "初始化".to_string(),
                    step_type: StepType::Transform {
                        script: "// 初始化脚本".to_string(),
                        language: "javascript".to_string(),
                    },
                    description: "初始化任务处理环境".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("tasks".to_string(), "${task_list}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("current_task_index".to_string(), "0".to_string());
                        map.insert("completed_tasks".to_string(), "[]".to_string());
                        map.insert("failed_tasks".to_string(), "[]".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(10),
                    next_steps: HashMap::new(),
                    error_handler: None,
                },
                WorkflowStep {
                    id: step2_id.clone(),
                    name: "检查任务".to_string(),
                    step_type: StepType::Condition {
                        expression: "${current_task_index} < ${task_list.length}".to_string(),
                    },
                    description: "检查是否还有待处理的任务".to_string(),
                    config: StepConfig::default(),
                    input_mapping: HashMap::new(),
                    output_mapping: HashMap::new(),
                    condition: Some("${current_task_index} < ${task_list.length}".to_string()),
                    retry_config: None,
                    timeout_seconds: Some(5),
                    next_steps: HashMap::new(),
                    error_handler: None,
                },
                WorkflowStep {
                    id: step3_id.clone(),
                    name: "处理任务".to_string(),
                    step_type: StepType::ToolCall { tool_name: "default_tool".to_string() },
                    description: "处理当前任务".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("task".to_string(), "${task_list[current_task_index]}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("task_result".to_string(), "${result}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(300),
                    next_steps: HashMap::new(),
                    error_handler: None,
                },
                WorkflowStep {
                    id: step4_id.clone(),
                    name: "更新状态".to_string(),
                    step_type: StepType::Transform {
                        script: "// 状态更新脚本".to_string(),
                        language: "javascript".to_string(),
                    },
                    description: "更新任务处理状态".to_string(),
                    config: StepConfig::default(),
                    input_mapping: {
                        let mut map = HashMap::new();
                        map.insert("task_result".to_string(), "${task_result}".to_string());
                        map.insert("current_index".to_string(), "${current_task_index}".to_string());
                        map
                    },
                    output_mapping: {
                        let mut map = HashMap::new();
                        map.insert("current_task_index".to_string(), "${current_index + 1}".to_string());
                        map.insert("completed_tasks".to_string(), "${completed_tasks.push(task_result)}".to_string());
                        map
                    },
                    condition: None,
                    retry_config: None,
                    timeout_seconds: Some(10),
                    next_steps: HashMap::new(),
                    error_handler: None,
                }
            ],
            initial_step_id: step1_id,
            config: WorkflowConfig::default(),
            created_at: std::time::SystemTime::now(),
            updated_at: std::time::SystemTime::now(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    

    #[test]
    fn test_template_creation() {
        let template = TemplateWorkflow {
            template_type: "simple".to_string(),
            output: None,
            name: Some("测试工作流".to_string()),
        };

        let workflow_def = template.create_workflow_definition().unwrap();
        assert_eq!(workflow_def.name, "测试工作流");
        assert!(!workflow_def.steps.is_empty());
    }

    #[test]
    fn test_llm_chain_template() {
        let template = TemplateWorkflow {
            template_type: "llm-chain".to_string(),
            output: None,
            name: Some("LLM链测试".to_string()),
        };

        let workflow_def = template.create_workflow_definition().unwrap();
        assert_eq!(workflow_def.name, "LLM链测试");
        assert_eq!(workflow_def.steps.len(), 3);

        // 检查步骤类型
        for step in &workflow_def.steps {
            match &step.step_type {
                StepType::LlmCall { .. } => {
                    // 这是预期的类型
                }
                _ => panic!("期望的步骤类型是 LlmCall"),
            }
        }
    }
    
    #[test]
    fn test_data_processing_template() {
        let template = TemplateWorkflow {
            template_type: "data-processing".to_string(),
            output: None,
            name: Some("数据处理测试".to_string()),
        };
        
        let workflow_def = template.create_workflow_definition().unwrap();
        assert_eq!(workflow_def.name, "数据处理测试");
        assert_eq!(workflow_def.steps.len(), 3);
        
        // 检查输入参数
        assert_eq!(workflow_def.input_parameters.len(), 2);
        assert_eq!(workflow_def.input_parameters[0].name, "input_file");
        assert_eq!(workflow_def.input_parameters[1].name, "output_file");
    }
    
    #[test]
    fn test_automation_template() {
        let template = TemplateWorkflow {
            template_type: "automation".to_string(),
            output: None,
            name: Some("自动化测试".to_string()),
        };
        
        let workflow_def = template.create_workflow_definition().unwrap();
        assert_eq!(workflow_def.name, "自动化测试");
        assert_eq!(workflow_def.steps.len(), 4);
        
        // 检查条件步骤
        let condition_step = workflow_def.steps.iter()
            .find(|s| matches!(s.step_type, StepType::Condition { .. }))
            .unwrap();
        assert!(condition_step.condition.is_some());
    }
    
    #[test]
    fn test_invalid_template() {
        let template = TemplateWorkflow {
            template_type: "invalid".to_string(),
            output: None,
            name: Some("无效模板".to_string()),
        };
        
        assert!(template.create_workflow_definition().is_err());
    }
}