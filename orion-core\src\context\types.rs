//! # 上下文核心类型定义
//!
//! 定义上下文管理系统中的核心数据结构，包括上下文条目、条目类型、
//! 重要性级别等基础类型。这些类型是整个上下文管理系统的基础。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use uuid::Uuid;

/// 上下文条目
/// 
/// 表示上下文管理系统中的一个条目，包含完整的元数据信息。
/// 每个条目都有唯一的ID，并且可以构建层次化的上下文树结构。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextEntry {
    /// 条目唯一标识符
    pub id: Uuid,
    /// 所属会话ID
    pub session_id: String,
    /// 条目类型，决定了条目的处理方式
    pub entry_type: ContextEntryType,
    /// 条目内容，存储实际的数据
    pub content: String,
    /// 元数据，存储额外的结构化信息
    pub metadata: HashMap<String, serde_json::Value>,
    /// 相关性评分（0.0-1.0），用于检索排序
    pub relevance_score: f64,
    /// 重要性级别，影响压缩和保留策略
    pub importance: ImportanceLevel,
    /// 创建时间戳
    pub created_at: SystemTime,
    /// 最后访问时间，用于LRU策略
    pub last_accessed: SystemTime,
    /// 访问次数，用于热度计算
    pub access_count: u64,
    /// 标签列表，用于分类和检索
    pub tags: Vec<String>,
    /// 父条目ID，用于构建层次结构
    pub parent_id: Option<Uuid>,
    /// 子条目ID列表，维护层次关系
    pub children_ids: Vec<Uuid>,
}

impl ContextEntry {
    /// 创建新的上下文条目
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话ID
    /// * `entry_type` - 条目类型
    /// * `content` - 条目内容
    /// 
    /// # 返回
    /// 
    /// 返回新创建的上下文条目，自动设置ID和时间戳
    pub fn new(session_id: String, entry_type: ContextEntryType, content: String) -> Self {
        let now = SystemTime::now();
        Self {
            id: Uuid::new_v4(),
            session_id,
            entry_type,
            content,
            metadata: HashMap::new(),
            relevance_score: 0.5, // 默认相关性评分
            importance: ImportanceLevel::Normal,
            created_at: now,
            last_accessed: now,
            access_count: 0,
            tags: Vec::new(),
            parent_id: None,
            children_ids: Vec::new(),
        }
    }

    /// 添加标签
    /// 
    /// # 参数
    /// 
    /// * `tag` - 要添加的标签
    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
        }
    }

    /// 移除标签
    /// 
    /// # 参数
    /// 
    /// * `tag` - 要移除的标签
    pub fn remove_tag(&mut self, tag: &str) {
        self.tags.retain(|t| t != tag);
    }

    /// 添加子条目
    /// 
    /// # 参数
    /// 
    /// * `child_id` - 子条目ID
    pub fn add_child(&mut self, child_id: Uuid) {
        if !self.children_ids.contains(&child_id) {
            self.children_ids.push(child_id);
        }
    }

    /// 移除子条目
    /// 
    /// # 参数
    /// 
    /// * `child_id` - 要移除的子条目ID
    pub fn remove_child(&mut self, child_id: Uuid) {
        self.children_ids.retain(|&id| id != child_id);
    }

    /// 更新访问信息
    /// 
    /// 更新最后访问时间和访问次数，用于热度计算
    pub fn update_access(&mut self) {
        self.last_accessed = SystemTime::now();
        self.access_count += 1;
    }

    /// 设置元数据
    /// 
    /// # 参数
    /// 
    /// * `key` - 元数据键
    /// * `value` - 元数据值
    pub fn set_metadata(&mut self, key: String, value: serde_json::Value) {
        self.metadata.insert(key, value);
    }

    /// 获取元数据
    /// 
    /// # 参数
    /// 
    /// * `key` - 元数据键
    /// 
    /// # 返回
    /// 
    /// 返回对应的元数据值
    pub fn get_metadata(&self, key: &str) -> Option<&serde_json::Value> {
        self.metadata.get(key)
    }
}

/// 上下文条目类型
/// 
/// 定义不同类型的上下文条目，每种类型有不同的处理逻辑和重要性权重。
/// 支持扩展的自定义类型。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ContextEntryType {
    /// 用户输入，通常具有较高的重要性
    UserInput,
    /// Agent响应，包含AI生成的内容
    AgentResponse,
    /// 系统消息，用于状态通知和控制
    SystemMessage,
    /// 工具调用，记录工具的使用情况
    ToolCall,
    /// 工具结果，存储工具执行的输出
    ToolResult,
    /// 错误信息，用于问题诊断和调试
    Error,
    /// 状态更新，记录系统状态变化
    StateUpdate,
    /// 文档片段，来自外部文档的内容
    DocumentFragment,
    /// 代码片段，包含代码相关的内容
    CodeFragment,
    /// 配置信息，存储系统配置数据
    Configuration,
    /// 自定义类型，支持扩展
    Custom(String),
}

impl ContextEntryType {
    /// 获取类型的默认重要性权重
    /// 
    /// # 返回
    /// 
    /// 返回该类型的默认重要性权重（0.0-1.0）
    pub fn default_importance_weight(&self) -> f64 {
        match self {
            ContextEntryType::UserInput => 0.9,
            ContextEntryType::AgentResponse => 0.8,
            ContextEntryType::Error => 0.9,
            ContextEntryType::ToolCall => 0.7,
            ContextEntryType::ToolResult => 0.6,
            ContextEntryType::SystemMessage => 0.5,
            ContextEntryType::StateUpdate => 0.4,
            ContextEntryType::DocumentFragment => 0.6,
            ContextEntryType::CodeFragment => 0.7,
            ContextEntryType::Configuration => 0.3,
            ContextEntryType::Custom(_) => 0.5,
        }
    }

    /// 检查是否为用户生成的内容
    /// 
    /// # 返回
    /// 
    /// 如果是用户生成的内容返回true
    pub fn is_user_generated(&self) -> bool {
        matches!(self, ContextEntryType::UserInput)
    }

    /// 检查是否为系统生成的内容
    /// 
    /// # 返回
    /// 
    /// 如果是系统生成的内容返回true
    pub fn is_system_generated(&self) -> bool {
        matches!(
            self,
            ContextEntryType::AgentResponse
                | ContextEntryType::SystemMessage
                | ContextEntryType::ToolCall
                | ContextEntryType::ToolResult
                | ContextEntryType::StateUpdate
        )
    }
}

/// 重要性级别
/// 
/// 定义上下文条目的重要性级别，影响压缩策略和保留优先级。
/// 级别越高，越不容易被压缩删除。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, PartialOrd)]
pub enum ImportanceLevel {
    /// 低重要性 - 可以优先压缩
    Low = 1,
    /// 普通重要性 - 标准处理
    Normal = 2,
    /// 高重要性 - 优先保留
    High = 3,
    /// 关键重要性 - 尽量不删除
    Critical = 4,
}

impl Default for ImportanceLevel {
    fn default() -> Self {
        ImportanceLevel::Normal
    }
}

impl ImportanceLevel {
    /// 获取重要性级别的数值权重
    /// 
    /// # 返回
    /// 
    /// 返回重要性级别对应的数值权重
    pub fn weight(&self) -> f64 {
        match self {
            ImportanceLevel::Low => 0.25,
            ImportanceLevel::Normal => 0.5,
            ImportanceLevel::High => 0.75,
            ImportanceLevel::Critical => 1.0,
        }
    }

    /// 从字符串解析重要性级别
    /// 
    /// # 参数
    /// 
    /// * `s` - 重要性级别字符串
    /// 
    /// # 返回
    /// 
    /// 返回解析的重要性级别，如果解析失败返回Normal
    pub fn from_str(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "low" | "低" => ImportanceLevel::Low,
            "normal" | "普通" => ImportanceLevel::Normal,
            "high" | "高" => ImportanceLevel::High,
            "critical" | "关键" => ImportanceLevel::Critical,
            _ => ImportanceLevel::Normal,
        }
    }

    /// 转换为字符串表示
    /// 
    /// # 返回
    /// 
    /// 返回重要性级别的字符串表示
    pub fn to_string(&self) -> String {
        match self {
            ImportanceLevel::Low => "Low".to_string(),
            ImportanceLevel::Normal => "Normal".to_string(),
            ImportanceLevel::High => "High".to_string(),
            ImportanceLevel::Critical => "Critical".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_context_entry_creation() {
        let entry = ContextEntry::new(
            "test_session".to_string(),
            ContextEntryType::UserInput,
            "测试内容".to_string(),
        );

        assert_eq!(entry.session_id, "test_session");
        assert_eq!(entry.entry_type, ContextEntryType::UserInput);
        assert_eq!(entry.content, "测试内容");
        assert_eq!(entry.importance, ImportanceLevel::Normal);
        assert_eq!(entry.access_count, 0);
    }

    #[test]
    fn test_context_entry_tags() {
        let mut entry = ContextEntry::new(
            "test_session".to_string(),
            ContextEntryType::UserInput,
            "测试内容".to_string(),
        );

        entry.add_tag("tag1".to_string());
        entry.add_tag("tag2".to_string());
        entry.add_tag("tag1".to_string()); // 重复添加

        assert_eq!(entry.tags.len(), 2);
        assert!(entry.tags.contains(&"tag1".to_string()));
        assert!(entry.tags.contains(&"tag2".to_string()));

        entry.remove_tag("tag1");
        assert_eq!(entry.tags.len(), 1);
        assert!(!entry.tags.contains(&"tag1".to_string()));
    }

    #[test]
    fn test_importance_levels() {
        assert!(ImportanceLevel::Critical > ImportanceLevel::High);
        assert!(ImportanceLevel::High > ImportanceLevel::Normal);
        assert!(ImportanceLevel::Normal > ImportanceLevel::Low);

        assert_eq!(ImportanceLevel::Critical.weight(), 1.0);
        assert_eq!(ImportanceLevel::Low.weight(), 0.25);
    }

    #[test]
    fn test_entry_type_properties() {
        assert!(ContextEntryType::UserInput.is_user_generated());
        assert!(!ContextEntryType::AgentResponse.is_user_generated());
        assert!(ContextEntryType::AgentResponse.is_system_generated());
        assert!(!ContextEntryType::UserInput.is_system_generated());

        assert_eq!(ContextEntryType::UserInput.default_importance_weight(), 0.9);
        assert_eq!(ContextEntryType::Error.default_importance_weight(), 0.9);
    }
}
