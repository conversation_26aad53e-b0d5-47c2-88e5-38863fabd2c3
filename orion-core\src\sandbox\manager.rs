//! # 沙箱管理器
//!
//! 提供沙箱的核心管理功能，整合所有子模块的功能。
//! 包括单个沙箱管理器和多沙箱管理器。

use super::types::{SandboxConfig, ExecutionResult, ScriptType};
use super::executor::SandboxExecutor;
use super::monitor::{ResourceMonitor, SystemResourceOverview};
use super::security::SecurityManager;
use crate::error::{OrionError, Result};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use tracing;

/// 沙箱实例
/// 
/// 单个沙箱的管理器，提供隔离的执行环境
pub struct Sandbox {
    /// 默认配置
    default_config: Arc<RwLock<SandboxConfig>>,
    /// 执行器
    executor: Arc<SandboxExecutor>,
    /// 安全管理器
    security_manager: Arc<SecurityManager>,
    /// 资源监控器
    resource_monitor: Arc<ResourceMonitor>,
    /// 执行历史
    execution_history: Arc<RwLock<Vec<ExecutionResult>>>,
    /// 统计信息
    stats: Arc<RwLock<SandboxStats>>,
}

impl Sandbox {
    /// 创建新的沙箱实例
    pub fn new() -> Self {
        Self {
            default_config: Arc::new(RwLock::new(SandboxConfig::default())),
            executor: Arc::new(SandboxExecutor::new()),
            security_manager: Arc::new(SecurityManager::new()),
            resource_monitor: Arc::new(ResourceMonitor::new()),
            execution_history: Arc::new(RwLock::new(Vec::new())),
            stats: Arc::new(RwLock::new(SandboxStats::new())),
        }
    }

    /// 使用指定配置创建沙箱
    pub fn with_config(config: SandboxConfig) -> Self {
        let sandbox = Self::new();
        {
            let default_config = sandbox.default_config.clone();
            tokio::spawn(async move {
                let mut cfg = default_config.write().await;
                *cfg = config;
            });
        }
        sandbox
    }

    /// 设置默认配置
    pub async fn set_default_config(&self, config: SandboxConfig) {
        let mut default_config = self.default_config.write().await;
        *default_config = config;
        tracing::info!("沙箱默认配置已更新");
    }

    /// 获取默认配置
    pub async fn get_default_config(&self) -> SandboxConfig {
        self.default_config.read().await.clone()
    }

    /// 执行命令
    pub async fn execute_command(
        &self,
        command: &str,
        args: &[&str],
        config: Option<SandboxConfig>,
    ) -> Result<ExecutionResult> {
        let config = match config {
            Some(cfg) => cfg,
            None => self.get_default_config().await,
        };

        // 检查命令是否被允许
        if !self.security_manager.is_command_allowed(command, &config) {
            return Err(OrionError::SecurityError(
                format!("命令被安全策略阻止: {}", command)
            ));
        }

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.total_executions += 1;
            stats.last_execution_time = Some(SystemTime::now());
        }

        // 执行命令
        let result = self.executor.execute_command(command, args, &config).await?;

        // 记录执行历史
        self.add_to_history(result.clone()).await;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            if result.is_success() {
                stats.successful_executions += 1;
            } else {
                stats.failed_executions += 1;
            }
            stats.total_execution_time_ms += result.execution_time_ms;
        }

        Ok(result)
    }

    /// 执行脚本文件
    pub async fn execute_script(
        &self,
        script_content: &str,
        script_type: ScriptType,
        config: Option<SandboxConfig>,
    ) -> Result<ExecutionResult> {
        let config = match config {
            Some(cfg) => cfg,
            None => self.get_default_config().await,
        };

        // 验证脚本安全性
        self.security_manager.validate_script_security(script_content, script_type)?;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            stats.total_script_executions += 1;
        }

        // 执行脚本
        let result = self.executor.execute_script(script_content, script_type, &config).await?;

        // 记录执行历史
        self.add_to_history(result.clone()).await;

        // 更新统计信息
        {
            let mut stats = self.stats.write().await;
            if result.is_success() {
                stats.successful_script_executions += 1;
            } else {
                stats.failed_script_executions += 1;
            }
        }

        Ok(result)
    }

    /// 获取执行历史
    pub async fn get_execution_history(&self, limit: Option<usize>) -> Vec<ExecutionResult> {
        let history = self.execution_history.read().await;
        let limit = limit.unwrap_or(100);

        if history.len() <= limit {
            history.clone()
        } else {
            history[history.len() - limit..].to_vec()
        }
    }

    /// 清理执行历史
    pub async fn clear_execution_history(&self) {
        let mut history = self.execution_history.write().await;
        history.clear();
        tracing::info!("执行历史已清理");
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> SandboxStats {
        self.stats.read().await.clone()
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = SandboxStats::new();
        tracing::info!("统计信息已重置");
    }

    /// 获取系统资源概览
    pub async fn get_system_overview(&self) -> SystemResourceOverview {
        // 使用资源监控器获取系统概览
        let _ = &self.resource_monitor; // 标记字段已使用
        super::monitor::ResourceMonitor::get_system_overview().await
    }

    /// 添加到执行历史
    async fn add_to_history(&self, result: ExecutionResult) {
        let mut history = self.execution_history.write().await;
        history.push(result);

        // 限制历史记录数量
        if history.len() > 1000 {
            history.drain(0..100);
        }
    }
}

impl Default for Sandbox {
    fn default() -> Self {
        Self::new()
    }
}

/// 沙箱统计信息
/// 
/// 记录沙箱的使用统计数据
#[derive(Debug, Clone)]
pub struct SandboxStats {
    /// 总执行次数
    pub total_executions: u64,
    /// 成功执行次数
    pub successful_executions: u64,
    /// 失败执行次数
    pub failed_executions: u64,
    /// 总脚本执行次数
    pub total_script_executions: u64,
    /// 成功脚本执行次数
    pub successful_script_executions: u64,
    /// 失败脚本执行次数
    pub failed_script_executions: u64,
    /// 总执行时间（毫秒）
    pub total_execution_time_ms: u64,
    /// 最后执行时间
    pub last_execution_time: Option<SystemTime>,
    /// 创建时间
    pub created_at: SystemTime,
}

impl SandboxStats {
    /// 创建新的统计信息
    pub fn new() -> Self {
        Self {
            total_executions: 0,
            successful_executions: 0,
            failed_executions: 0,
            total_script_executions: 0,
            successful_script_executions: 0,
            failed_script_executions: 0,
            total_execution_time_ms: 0,
            last_execution_time: None,
            created_at: SystemTime::now(),
        }
    }

    /// 获取成功率
    pub fn success_rate(&self) -> f32 {
        if self.total_executions > 0 {
            (self.successful_executions as f32 / self.total_executions as f32) * 100.0
        } else {
            0.0
        }
    }

    /// 获取脚本成功率
    pub fn script_success_rate(&self) -> f32 {
        if self.total_script_executions > 0 {
            (self.successful_script_executions as f32 / self.total_script_executions as f32) * 100.0
        } else {
            0.0
        }
    }

    /// 获取平均执行时间
    pub fn average_execution_time_ms(&self) -> f32 {
        if self.total_executions > 0 {
            self.total_execution_time_ms as f32 / self.total_executions as f32
        } else {
            0.0
        }
    }
}

impl Default for SandboxStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 沙箱管理器 - 管理多个沙箱实例
/// 
/// 提供多沙箱的创建、管理和协调功能
pub struct SandboxManager {
    /// 沙箱实例
    sandboxes: Arc<RwLock<HashMap<String, Arc<Sandbox>>>>,
    /// 默认沙箱
    default_sandbox: Arc<RwLock<Option<String>>>,
    /// 管理器统计信息
    manager_stats: Arc<RwLock<ManagerStats>>,
}

impl SandboxManager {
    /// 创建新的沙箱管理器
    pub fn new() -> Self {
        Self {
            sandboxes: Arc::new(RwLock::new(HashMap::new())),
            default_sandbox: Arc::new(RwLock::new(None)),
            manager_stats: Arc::new(RwLock::new(ManagerStats::new())),
        }
    }

    /// 创建沙箱
    pub async fn create_sandbox(&self, name: String, config: Option<SandboxConfig>) -> Result<()> {
        let sandbox = match config {
            Some(cfg) => Arc::new(Sandbox::with_config(cfg)),
            None => Arc::new(Sandbox::new()),
        };

        let mut sandboxes = self.sandboxes.write().await;
        sandboxes.insert(name.clone(), sandbox);

        // 如果这是第一个沙箱，设为默认
        let mut default = self.default_sandbox.write().await;
        if default.is_none() {
            *default = Some(name.clone());
        }

        // 更新统计信息
        {
            let mut stats = self.manager_stats.write().await;
            stats.total_sandboxes += 1;
        }

        tracing::info!("沙箱 '{}' 已创建", name);
        Ok(())
    }

    /// 获取沙箱
    pub async fn get_sandbox(&self, name: &str) -> Result<Arc<Sandbox>> {
        let sandboxes = self.sandboxes.read().await;
        sandboxes.get(name)
            .cloned()
            .ok_or_else(|| OrionError::SecurityError(format!("沙箱 '{}' 不存在", name)))
    }

    /// 获取默认沙箱
    pub async fn get_default_sandbox(&self) -> Result<Arc<Sandbox>> {
        let default = self.default_sandbox.read().await;
        let name = default.as_ref()
            .ok_or_else(|| OrionError::SecurityError("未设置默认沙箱".to_string()))?;

        self.get_sandbox(name).await
    }

    /// 设置默认沙箱
    pub async fn set_default_sandbox(&self, name: String) -> Result<()> {
        let sandboxes = self.sandboxes.read().await;
        if !sandboxes.contains_key(&name) {
            return Err(OrionError::SecurityError(format!("沙箱 '{}' 不存在", name)));
        }

        let mut default = self.default_sandbox.write().await;
        *default = Some(name.clone());

        tracing::info!("默认沙箱设置为 '{}'", name);
        Ok(())
    }

    /// 删除沙箱
    pub async fn remove_sandbox(&self, name: &str) -> Result<()> {
        let mut sandboxes = self.sandboxes.write().await;
        sandboxes.remove(name)
            .ok_or_else(|| OrionError::SecurityError(format!("沙箱 '{}' 不存在", name)))?;

        // 如果删除的是默认沙箱，清除默认设置
        let mut default = self.default_sandbox.write().await;
        if default.as_ref() == Some(&name.to_string()) {
            *default = None;
        }

        // 更新统计信息
        {
            let mut stats = self.manager_stats.write().await;
            stats.total_sandboxes = stats.total_sandboxes.saturating_sub(1);
        }

        tracing::info!("沙箱 '{}' 已删除", name);
        Ok(())
    }

    /// 列出所有沙箱
    pub async fn list_sandboxes(&self) -> Vec<String> {
        let sandboxes = self.sandboxes.read().await;
        sandboxes.keys().cloned().collect()
    }

    /// 获取管理器统计信息
    pub async fn get_manager_stats(&self) -> ManagerStats {
        self.manager_stats.read().await.clone()
    }

    /// 获取所有沙箱的统计信息
    pub async fn get_all_sandbox_stats(&self) -> HashMap<String, SandboxStats> {
        let sandboxes = self.sandboxes.read().await;
        let mut all_stats = HashMap::new();

        for (name, sandbox) in sandboxes.iter() {
            let stats = sandbox.get_stats().await;
            all_stats.insert(name.clone(), stats);
        }

        all_stats
    }
}

impl Default for SandboxManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 管理器统计信息
/// 
/// 记录沙箱管理器的统计数据
#[derive(Debug, Clone)]
pub struct ManagerStats {
    /// 总沙箱数量
    pub total_sandboxes: u64,
    /// 创建时间
    pub created_at: SystemTime,
}

impl ManagerStats {
    /// 创建新的管理器统计信息
    pub fn new() -> Self {
        Self {
            total_sandboxes: 0,
            created_at: SystemTime::now(),
        }
    }
}

impl Default for ManagerStats {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;


    #[tokio::test]
    async fn test_sandbox_creation() {
        let sandbox = Sandbox::new();
        let config = sandbox.get_default_config().await;

        assert_eq!(config.timeout_seconds, 30);
        assert_eq!(config.max_memory_mb, 512);
    }

    #[tokio::test]
    async fn test_sandbox_manager() {
        let manager = SandboxManager::new();

        // 创建沙箱
        manager.create_sandbox("test".to_string(), None).await.unwrap();

        // 验证沙箱存在
        assert!(manager.get_sandbox("test").await.is_ok());
        assert_eq!(manager.list_sandboxes().await, vec!["test"]);

        // 删除沙箱
        manager.remove_sandbox("test").await.unwrap();
        assert!(manager.get_sandbox("test").await.is_err());
    }

    #[tokio::test]
    async fn test_sandbox_stats() {
        let sandbox = Sandbox::new();
        let stats = sandbox.get_stats().await;

        assert_eq!(stats.total_executions, 0);
        assert_eq!(stats.success_rate(), 0.0);
    }
}
