//! # 工作流执行器实现模块
//!
//! 包含默认工作流执行器的具体实现。
//! 提供工作流的执行逻辑、步骤调度、错误处理等功能。

use async_trait::async_trait;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::{RwLock, Semaphore};
use tokio::time::timeout;
use uuid::Uuid;

use crate::error::{OrionError, Result, WorkflowError};
use crate::message::{Message, MessageBus, MessagePayload};
use crate::tools::{ToolRequest, ToolRegistry};

use super::config::{BackoffStrategy, ErrorHandlingStrategy};
use super::definition::{StepType, WorkflowDefinition, WorkflowStep};
use super::execution::{StepExecutionResult, StepStatus, WorkflowExecutor};
use super::instance::{ExecutionContext, WorkflowInstance, WorkflowStatus};

/// 默认工作流执行器
/// 
/// 提供完整的工作流执行功能，包括：
/// - 步骤调度和执行
/// - 并发控制
/// - 错误处理和重试
/// - 状态管理
/// - 消息通知
pub struct DefaultWorkflowExecutor {
    /// 工具注册表，用于执行工具调用步骤
    tool_registry: Arc<ToolRegistry>,
    
    /// 消息总线，用于发送执行状态通知
    message_bus: Arc<MessageBus>,
    
    /// 并发限制信号量，控制同时执行的工作流数量
    concurrency_limiter: Arc<Semaphore>,
    
    /// 活跃工作流实例存储
    /// 键为实例ID，值为实例的线程安全引用
    active_instances: Arc<RwLock<HashMap<Uuid, Arc<RwLock<WorkflowInstance>>>>>,
}

impl DefaultWorkflowExecutor {
    /// 创建新的默认工作流执行器
    /// 
    /// # 参数
    /// * `tool_registry` - 工具注册表的共享引用
    /// * `message_bus` - 消息总线的共享引用
    /// * `max_concurrency` - 最大并发执行数量
    /// 
    /// # 返回值
    /// 返回新创建的执行器实例
    pub fn new(
        tool_registry: Arc<ToolRegistry>,
        message_bus: Arc<MessageBus>,
        max_concurrency: usize,
    ) -> Self {
        Self {
            tool_registry,
            message_bus,
            concurrency_limiter: Arc::new(Semaphore::new(max_concurrency)),
            active_instances: Arc::new(RwLock::new(HashMap::new())),
        }
    }
    
    /// 计算下一个要执行的步骤ID
    /// 
    /// 根据当前步骤的类型和执行结果，确定工作流的下一个执行步骤。
    /// 支持条件分支、错误处理等逻辑。
    /// 
    /// # 参数
    /// * `current_step` - 当前执行的步骤
    /// * `result` - 当前步骤的执行结果
    /// * `_context` - 执行上下文（暂未使用，预留扩展）
    /// 
    /// # 返回值
    /// * `Some(Uuid)` - 下一个步骤的ID
    /// * `None` - 没有下一个步骤，工作流结束
    async fn calculate_next_step(
        &self,
        current_step: &WorkflowStep,
        result: &StepExecutionResult,
        _context: &ExecutionContext,
    ) -> Option<Uuid> {
        match result.status {
            StepStatus::Completed => {
                // 根据步骤类型和结果确定下一步
                match &current_step.step_type {
                    StepType::Condition { .. } => {
                        // 条件步骤根据结果选择分支
                        if let Some(result_value) = &result.result {
                            if let Some(condition_result) = result_value.as_bool() {
                                let branch_key = if condition_result { "true" } else { "false" };
                                return current_step.next_steps.get(branch_key).copied();
                            }
                        }
                        current_step.next_steps.get("default").copied()
                    }
                    _ => {
                        // 其他步骤使用默认下一步
                        current_step.next_steps.get("default").copied()
                    }
                }
            }
            StepStatus::Failed => {
                // 失败时执行错误处理步骤
                current_step.error_handler
            }
            StepStatus::Skipped => {
                // 跳过时执行下一步
                current_step.next_steps.get("default").copied()
            }
            _ => None,
        }
    }
    
    /// 判断步骤是否应该重试
    /// 
    /// 根据步骤的重试配置和执行结果，判断是否需要重试执行。
    /// 
    /// # 参数
    /// * `step` - 要检查的步骤
    /// * `result` - 步骤的执行结果
    /// 
    /// # 返回值
    /// * `true` - 应该重试
    /// * `false` - 不应该重试
    async fn should_retry(
        &self,
        step: &WorkflowStep,
        result: &StepExecutionResult,
    ) -> bool {
        if let Some(retry_config) = &step.retry_config {
            if result.retry_count < retry_config.max_attempts {
                // 检查错误是否可重试
                if let Some(error) = &result.error {
                    return retry_config.retryable_errors.iter()
                        .any(|pattern| error.contains(pattern));
                }
            }
        }
        false
    }
    
    /// 计算重试延迟时间
    ///
    /// 根据重试配置和当前重试次数，计算下次重试的延迟时间。
    /// 支持多种退避策略。
    ///
    /// # 参数
    /// * `retry_config` - 重试配置
    /// * `attempt` - 当前重试次数（从1开始）
    ///
    /// # 返回值
    /// 返回延迟时间的Duration
    fn calculate_retry_delay(
        &self,
        retry_config: &super::config::RetryConfig,
        attempt: u32,
    ) -> Duration {
        let base_delay = Duration::from_secs(retry_config.retry_interval_seconds);

        match &retry_config.backoff_strategy {
            BackoffStrategy::Fixed => base_delay,
            BackoffStrategy::Linear => base_delay * attempt,
            BackoffStrategy::Exponential => base_delay * (2_u32.pow(attempt - 1)),
            BackoffStrategy::Custom(delays) => {
                let index = (attempt - 1) as usize;
                if index < delays.len() {
                    Duration::from_secs(delays[index])
                } else {
                    base_delay
                }
            }
        }
    }

    /// 执行工具调用步骤
    ///
    /// 调用指定的工具并处理输入输出映射。
    ///
    /// # 参数
    /// * `tool_name` - 要调用的工具名称
    /// * `step` - 步骤定义，包含输入映射等配置
    /// * `context` - 执行上下文，包含变量和状态
    ///
    /// # 返回值
    /// * `Ok(serde_json::Value)` - 工具执行结果
    /// * `Err(OrionError)` - 工具执行失败
    async fn execute_tool_call_step(
        &self,
        tool_name: &str,
        step: &WorkflowStep,
        context: &mut ExecutionContext,
    ) -> Result<serde_json::Value> {
        // 准备工具请求参数
        let mut parameters = HashMap::new();
        for (param_name, variable_path) in &step.input_mapping {
            if let Some(value) = context.variables.get(variable_path) {
                parameters.insert(param_name.clone(), value.clone());
            }
        }

        // 创建工具请求
        let tool_request = ToolRequest {
            id: Uuid::new_v4(),
            tool_name: tool_name.to_string(),
            parameters,
            context: crate::tools::ToolContext::default(),
            timestamp: SystemTime::now(),
        };

        // 执行工具
        let tool_result = self.tool_registry.execute_tool(tool_request).await?;

        if tool_result.success {
            Ok(tool_result.data)
        } else {
            Err(OrionError::Workflow(WorkflowError::execution_error(
                tool_result.error.unwrap_or_else(|| "工具执行失败".to_string())
            )))
        }
    }

    /// 执行条件步骤
    ///
    /// 评估条件表达式并返回布尔结果。
    ///
    /// # 参数
    /// * `expression` - 条件表达式字符串
    /// * `context` - 执行上下文，包含变量值
    ///
    /// # 返回值
    /// * `Ok(serde_json::Value)` - 条件评估结果（布尔值）
    /// * `Err(OrionError)` - 表达式评估失败
    async fn execute_condition_step(
        &self,
        expression: &str,
        context: &ExecutionContext,
    ) -> Result<serde_json::Value> {
        // 简单的条件表达式求值（实际实现中可能需要更复杂的表达式引擎）
        let result = self.evaluate_expression(expression, context)?;
        Ok(serde_json::Value::Bool(result))
    }

    /// 求值表达式
    ///
    /// 解析和计算条件表达式。
    /// 这是一个简化的实现，实际项目中可能需要更强大的表达式引擎。
    ///
    /// # 参数
    /// * `expression` - 要评估的表达式
    /// * `context` - 执行上下文，提供变量值
    ///
    /// # 返回值
    /// * `Ok(bool)` - 表达式评估结果
    /// * `Err(OrionError)` - 评估失败
    fn evaluate_expression(
        &self,
        expression: &str,
        context: &ExecutionContext,
    ) -> Result<bool> {
        // 这里是一个简化的实现，实际中可能需要使用专门的表达式引擎
        // 支持基本的比较操作和逻辑操作

        // 替换变量
        let mut expr = expression.to_string();
        for (var_name, var_value) in &context.variables {
            let placeholder = format!("${{{}}}", var_name);
            if expr.contains(&placeholder) {
                let value_str = match var_value {
                    serde_json::Value::String(s) => format!("\"{}\"", s),
                    serde_json::Value::Number(n) => n.to_string(),
                    serde_json::Value::Bool(b) => b.to_string(),
                    _ => var_value.to_string(),
                };
                expr = expr.replace(&placeholder, &value_str);
            }
        }

        // 简单的布尔表达式求值
        // 这里只是示例，实际实现需要更完善的解析器
        if expr.contains("==") {
            let parts: Vec<&str> = expr.split("==").collect();
            if parts.len() == 2 {
                let left = parts[0].trim();
                let right = parts[1].trim();
                return Ok(left == right);
            }
        }

        // 默认返回 true
        Ok(true)
    }
}

#[async_trait]
impl WorkflowExecutor for DefaultWorkflowExecutor {
    /// 执行完整的工作流
    async fn execute_workflow(
        &self,
        instance: &mut WorkflowInstance,
        definition: &WorkflowDefinition,
    ) -> Result<()> {
        // 获取并发许可
        let _permit = self.concurrency_limiter.acquire().await
            .map_err(|e| OrionError::Workflow(WorkflowError::execution_error(
                format!("获取并发许可失败: {}", e)
            )))?;

        // 注册活跃实例
        {
            let mut active_instances = self.active_instances.write().await;
            active_instances.insert(instance.id, Arc::new(RwLock::new(instance.clone())));
        }

        // 设置执行状态
        instance.status = WorkflowStatus::Running;
        instance.started_at = Some(SystemTime::now());

        // 发送工作流开始消息
        let mut parameters = HashMap::new();
        parameters.insert("workflow_name".to_string(), serde_json::Value::String(definition.name.clone()));
        parameters.insert("input_parameters".to_string(), serde_json::to_value(&instance.input_parameters).unwrap_or_default());
        parameters.insert("instance_id".to_string(), serde_json::Value::String(instance.id.to_string()));

        let start_message = Message::new(
            "workflow_manager".to_string(),
            "system".to_string(),
            MessagePayload::WorkflowControl {
                action: "start".to_string(),
                workflow_id: instance.workflow_id.to_string(),
                parameters,
            },
        );

        if let Err(e) = self.message_bus.send_message(start_message).await {
            tracing::warn!("发送工作流开始消息失败: {}", e);
        }

        // 执行工作流
        let execution_result = self.execute_workflow_steps(instance, definition).await;

        // 更新最终状态
        match &execution_result {
            Ok(_) => {
                instance.status = WorkflowStatus::Completed;
                instance.completed_at = Some(SystemTime::now());
            }
            Err(e) => {
                instance.status = WorkflowStatus::Failed;
                instance.error = Some(e.to_string());
                instance.completed_at = Some(SystemTime::now());
            }
        }

        // 发送工作流完成消息
        let mut complete_parameters = HashMap::new();
        complete_parameters.insert("status".to_string(), serde_json::to_value(&instance.status).unwrap_or_default());
        complete_parameters.insert("output_parameters".to_string(), serde_json::to_value(&instance.output_parameters).unwrap_or_default());
        complete_parameters.insert("execution_stats".to_string(), serde_json::to_value(&instance.execution_context.execution_stats).unwrap_or_default());
        complete_parameters.insert("instance_id".to_string(), serde_json::Value::String(instance.id.to_string()));

        let complete_message = Message::new(
            "workflow_manager".to_string(),
            "system".to_string(),
            MessagePayload::WorkflowControl {
                action: "complete".to_string(),
                workflow_id: instance.workflow_id.to_string(),
                parameters: complete_parameters,
            },
        );

        if let Err(e) = self.message_bus.send_message(complete_message).await {
            tracing::warn!("发送工作流完成消息失败: {}", e);
        }

        // 移除活跃实例
        {
            let mut active_instances = self.active_instances.write().await;
            active_instances.remove(&instance.id);
        }

        execution_result
    }

    /// 执行单个步骤
    async fn execute_step(
        &self,
        step: &WorkflowStep,
        context: &mut ExecutionContext,
    ) -> Result<StepExecutionResult> {
        let start_time = std::time::Instant::now();
        let started_at = SystemTime::now();

        // 应用超时
        let execution_future = self.execute_step_internal(step, context);
        let result = if let Some(timeout_seconds) = step.timeout_seconds {
            match timeout(Duration::from_secs(timeout_seconds), execution_future).await {
                Ok(result) => result,
                Err(_) => Err(OrionError::Workflow(WorkflowError::step_execution_failed(
                    &step.name, "执行超时"
                ))),
            }
        } else {
            execution_future.await
        };

        let execution_time_ms = start_time.elapsed().as_millis() as u64;

        match result {
            Ok(data) => {
                // 应用输出映射
                for (output_name, variable_path) in &step.output_mapping {
                    if let Some(output_value) = data.get(output_name) {
                        context.variables.insert(variable_path.clone(), output_value.clone());
                    }
                }

                // 存储步骤结果
                context.step_results.insert(step.id, data.clone());

                Ok(StepExecutionResult {
                    step_id: step.id,
                    status: StepStatus::Completed,
                    result: Some(data),
                    error: None,
                    execution_time_ms,
                    retry_count: 0,
                    started_at,
                    completed_at: Some(SystemTime::now()),
                })
            }
            Err(e) => {
                Ok(StepExecutionResult {
                    step_id: step.id,
                    status: StepStatus::Failed,
                    result: None,
                    error: Some(e.to_string()),
                    execution_time_ms,
                    retry_count: 0,
                    started_at,
                    completed_at: Some(SystemTime::now()),
                })
            }
        }
    }

    /// 暂停工作流
    async fn pause_workflow(&self, instance_id: Uuid) -> Result<()> {
        let active_instances = self.active_instances.read().await;
        if let Some(instance_arc) = active_instances.get(&instance_id) {
            let mut instance = instance_arc.write().await;
            instance.status = WorkflowStatus::Paused;
            tracing::info!("工作流实例 {} 已暂停", instance_id);
            Ok(())
        } else {
            Err(OrionError::Workflow(WorkflowError::execution_error(
                format!("工作流实例不存在: {}", instance_id)
            )))
        }
    }

    /// 恢复工作流
    async fn resume_workflow(&self, instance_id: Uuid) -> Result<()> {
        let active_instances = self.active_instances.read().await;
        if let Some(instance_arc) = active_instances.get(&instance_id) {
            let mut instance = instance_arc.write().await;
            if instance.status == WorkflowStatus::Paused {
                instance.status = WorkflowStatus::Running;
                tracing::info!("工作流实例 {} 已恢复", instance_id);
                Ok(())
            } else {
                Err(OrionError::Workflow(WorkflowError::invalid_state(
                    format!("{:?}", instance.status), "可暂停状态"
                )))
            }
        } else {
            Err(OrionError::Workflow(WorkflowError::execution_error(
                format!("工作流实例不存在: {}", instance_id)
            )))
        }
    }

    /// 取消工作流
    async fn cancel_workflow(&self, instance_id: Uuid) -> Result<()> {
        let active_instances = self.active_instances.read().await;
        if let Some(instance_arc) = active_instances.get(&instance_id) {
            let mut instance = instance_arc.write().await;
            instance.status = WorkflowStatus::Cancelled;
            instance.completed_at = Some(SystemTime::now());
            tracing::info!("工作流实例 {} 已取消", instance_id);
            Ok(())
        } else {
            Err(OrionError::Workflow(WorkflowError::execution_error(
                format!("工作流实例不存在: {}", instance_id)
            )))
        }
    }
}

impl DefaultWorkflowExecutor {
    /// 执行工作流步骤序列
    ///
    /// 按照工作流定义的逻辑顺序执行所有步骤。
    /// 处理步骤间的依赖关系、条件分支、错误处理等。
    async fn execute_workflow_steps(
        &self,
        instance: &mut WorkflowInstance,
        definition: &WorkflowDefinition,
    ) -> Result<()> {
        let mut current_step_id = Some(definition.initial_step_id);

        while let Some(step_id) = current_step_id {
            // 检查工作流状态
            if instance.status != WorkflowStatus::Running {
                break;
            }

            // 查找步骤定义
            let step = definition.steps.iter()
                .find(|s| s.id == step_id)
                .ok_or_else(|| OrionError::Workflow(WorkflowError::definition_error(
                    format!("步骤不存在: {}", step_id)
                )))?;

            // 更新当前步骤
            instance.current_step_id = Some(step_id);

            // 执行步骤
            let mut step_result = self.execute_step(step, &mut instance.execution_context).await?;

            // 处理重试
            while step_result.status == StepStatus::Failed && self.should_retry(step, &step_result).await {
                if let Some(retry_config) = &step.retry_config {
                    let delay = self.calculate_retry_delay(retry_config, step_result.retry_count + 1);
                    tokio::time::sleep(delay).await;

                    step_result.retry_count += 1;
                    step_result = self.execute_step(step, &mut instance.execution_context).await?;
                }
            }

            // 更新实例状态
            match step_result.status {
                StepStatus::Completed => {
                    instance.completed_steps.push(step_id);
                    instance.execution_context.execution_stats.completed_steps += 1;
                }
                StepStatus::Failed => {
                    instance.failed_steps.push(step_id);
                    instance.execution_context.execution_stats.failed_steps += 1;

                    // 根据错误处理策略决定是否继续
                    match definition.config.error_handling_strategy {
                        ErrorHandlingStrategy::StopImmediately => {
                            return Err(OrionError::Workflow(WorkflowError::step_execution_failed(
                                &step.name,
                                step_result.error.unwrap_or_else(|| "未知错误".to_string())
                            )));
                        }
                        ErrorHandlingStrategy::ContinueOnError => {
                            // 继续执行下一步
                        }
                        ErrorHandlingStrategy::ExecuteErrorHandler => {
                            // 执行错误处理步骤
                            if let Some(error_handler_id) = step.error_handler {
                                current_step_id = Some(error_handler_id);
                                continue;
                            }
                        }
                        ErrorHandlingStrategy::RollbackToCheckpoint => {
                            // 回滚到最近的检查点
                            // 这里需要实现检查点回滚逻辑
                            return Err(OrionError::Workflow(WorkflowError::execution_error(
                                "检查点回滚功能尚未实现"
                            )));
                        }
                    }
                }
                StepStatus::Skipped => {
                    instance.skipped_steps.push(step_id);
                    instance.execution_context.execution_stats.skipped_steps += 1;
                }
                _ => {}
            }

            // 更新执行统计
            instance.execution_context.execution_stats.total_execution_time_ms += step_result.execution_time_ms;

            // 计算下一步骤
            current_step_id = self.calculate_next_step(step, &step_result, &instance.execution_context).await;
        }

        Ok(())
    }

    /// 执行步骤内部逻辑
    ///
    /// 根据步骤类型执行相应的逻辑。
    /// 这是步骤执行的核心分发方法。
    async fn execute_step_internal(
        &self,
        step: &WorkflowStep,
        context: &mut ExecutionContext,
    ) -> Result<serde_json::Value> {
        match &step.step_type {
            StepType::ToolCall { tool_name } => {
                self.execute_tool_call_step(tool_name, step, context).await
            }
            StepType::Condition { expression } => {
                self.execute_condition_step(expression, context).await
            }
            StepType::Delay { duration_seconds } => {
                tokio::time::sleep(Duration::from_secs(*duration_seconds)).await;
                Ok(serde_json::json!({ "delayed": true }))
            }
            StepType::Transform { script, language: _ } => {
                // 简单的数据转换（实际实现中可能需要脚本引擎）
                // 这里只是示例
                Ok(serde_json::json!({ "transformed": script }))
            }
            _ => {
                Err(OrionError::Workflow(WorkflowError::definition_error(
                    format!("不支持的步骤类型: {:?}", step.step_type)
                )))
            }
        }
    }
}
