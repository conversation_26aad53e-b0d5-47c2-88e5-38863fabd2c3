//! # Agent 交互功能
//!
//! 实现与 Agent 聊天交互的功能。

use crate::error::Result;
use crate::commands::agent::{types::ChatWithAgent, utils::*};
use orion_core::agent::*;
use std::collections::HashMap;
use std::time::SystemTime;
use uuid::Uuid;

impl ChatWithAgent {
    /// 执行与 Agent 交互
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        if self.interactive {
            self.start_interactive_chat().await
        } else {
            self.send_single_message().await
        }
    }
    
    /// 发送单条消息
    async fn send_single_message(&self) -> Result<()> {
        let message = self.get_message().await?;
        let task_request = self.create_task_request(&message)?;
        
        println!("💬 发送消息给 Agent '{}'...", self.agent_name);
        
        let response = self.simulate_agent_response(&task_request).await?;
        self.print_response(&response)?;
        
        Ok(())
    }
    
    /// 开始交互式聊天
    async fn start_interactive_chat(&self) -> Result<()> {
        println!("🤖 开始与 Agent '{}' 交互", self.agent_name);
        println!("输入 'exit' 或 'quit' 退出，输入 'help' 查看帮助");
        println!();
        
        loop {
            print!("You: ");
            use std::io::{self, Write};
            io::stdout().flush().unwrap();
            
            let mut input = String::new();
            io::stdin().read_line(&mut input).unwrap();
            let input = input.trim();
            
            match input {
                "exit" | "quit" => {
                    println!("👋 再见！");
                    break;
                }
                "help" => {
                    self.print_help();
                    continue;
                }
                "" => continue,
                _ => {
                    let task_request = self.create_task_request(input)?;
                    let response = self.simulate_agent_response(&task_request).await?;
                    
                    println!("Agent: {}", response.content);
                    println!();
                }
            }
        }
        
        Ok(())
    }
    
    /// 获取消息内容
    async fn get_message(&self) -> Result<String> {
        if let Some(msg) = &self.message {
            Ok(msg.clone())
        } else if let Some(file) = &self.file {
            tokio::fs::read_to_string(file).await
                .map_err(|e| crate::error::CliError::IoError {
                    error: format!("读取文件失败: {}", e),
                })
        } else {
            Err(crate::error::CliError::InvalidArgument {
                error: "必须提供消息内容或文件路径".to_string(),
            })
        }
    }
    
    /// 创建任务请求
    fn create_task_request(&self, message: &str) -> Result<TaskRequest> {
        let task_type = match self.task_type.as_deref() {
            Some("qa") => TaskType::Question,
            Some("code_generation") => TaskType::CodeGeneration,
            Some("document_analysis") => TaskType::DocumentAnalysis,
            Some("data_processing") => TaskType::DataProcessing,
            Some("workflow_execution") => TaskType::WorkflowExecution,
            Some("tool_call") => TaskType::ToolInvocation,
            Some("custom") => TaskType::Custom("custom".to_string()),
            _ => TaskType::Question,
        };
        
        let priority = match self.priority.as_deref() {
            Some("low") => TaskPriority::Low,
            Some("normal") => TaskPriority::Normal,
            Some("high") => TaskPriority::High,
            Some("urgent") => TaskPriority::Urgent,
            _ => TaskPriority::Normal,
        };
        
        Ok(TaskRequest {
            id: Uuid::new_v4(),
            content: message.to_string(),
            task_type,
            session_id: self.session_id.clone(),
            user_id: Some("cli-user".to_string()),
            priority,
            parameters: HashMap::new(),
            context: HashMap::new(),
            created_at: SystemTime::now(),
            deadline: None,
        })
    }
    
    /// 模拟 Agent 响应
    async fn simulate_agent_response(&self, request: &TaskRequest) -> Result<TaskResponse> {
        simulate_processing_delay(500).await;
        
        let response_content = match request.task_type {
            TaskType::Question => format!("这是对问题 '{}' 的回答。", request.content),
            TaskType::CodeGeneration => "```rust\nfn hello_world() {\n    println!(\"Hello, World!\");\n}\n```".to_string(),
            TaskType::DocumentAnalysis => format!("已分析文档内容: {}", request.content),
            _ => format!("已处理任务: {}", request.content),
        };
        
        Ok(TaskResponse {
            task_id: request.id.clone(),
            content: response_content,
            response_type: ResponseType::Text,
            success: true,
            error: None,
            tool_results: Vec::new(),
            generated_files: Vec::new(),
            metadata: HashMap::new(),
            processing_time_ms: 500,
            created_at: SystemTime::now(),
            token_usage: None,
        })
    }
    
    /// 打印响应
    fn print_response(&self, response: &TaskResponse) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(response, &self.format)?;
            }
            "text" | _ => {
                if response.success {
                    println!("✅ Agent 响应:");
                    println!("{}", response.content);
                    
                    if !response.generated_files.is_empty() {
                        println!();
                        println!("📁 生成的文件:");
                        for file in &response.generated_files {
                            println!("  • {}", file);
                        }
                    }
                    
                    println!();
                    println!("⏱️  处理时间: {:.2}秒", response.processing_time_ms as f64 / 1000.0);
                } else {
                    println!("❌ Agent 响应失败");
                    if let Some(error) = &response.error {
                        println!("错误: {}", error);
                    }
                }
            }
        }
        
        Ok(())
    }
    
    /// 打印帮助信息
    fn print_help(&self) {
        println!("💡 可用命令:");
        println!("  exit, quit - 退出交互模式");
        println!("  help       - 显示此帮助信息");
        println!("  其他输入   - 发送消息给 Agent");
        println!();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_chat_with_agent() {
        let cmd = ChatWithAgent {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            message: Some("Hello".to_string()),
            file: None,
            interactive: false,
            session_id: None,
            task_type: Some("qa".to_string()),
            priority: Some("normal".to_string()),
            timeout: Some(30),
            format: "text".to_string(),
        };

        assert_eq!(cmd.agent_name, "test-agent");
        assert_eq!(cmd.message, Some("Hello".to_string()));
        assert_eq!(cmd.task_type, Some("qa".to_string()));
    }

    #[test]
    fn test_create_task_request() {
        let cmd = ChatWithAgent {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            message: Some("Hello".to_string()),
            file: None,
            interactive: false,
            session_id: Some("session-123".to_string()),
            task_type: Some("qa".to_string()),
            priority: Some("high".to_string()),
            timeout: Some(30),
            format: "text".to_string(),
        };

        let request = cmd.create_task_request("test message").unwrap();
        assert_eq!(request.content, "test message");
        assert_eq!(request.session_id, Some("session-123".to_string()));
        assert!(matches!(request.task_type, TaskType::Question));
        assert!(matches!(request.priority, TaskPriority::High));
    }
}