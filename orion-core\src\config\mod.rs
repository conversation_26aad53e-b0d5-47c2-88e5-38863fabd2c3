//! # 配置管理模块
//!
//! 提供 Orion 系统的配置管理功能，包括配置文件加载、保存和基础验证。

mod types;
mod defaults;

// 重新导出类型
pub use types::*;

use crate::error::{OrionError, Result, ConfigError};
use std::path::Path;

impl OrionConfig {
    /// 从文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path_buf = path.as_ref().to_path_buf();
        let content = std::fs::read_to_string(&path_buf)
            .map_err(|e| OrionError::Config(ConfigError::file_read_error(&path_buf, format!("读取配置文件失败: {}", e))))?;
        
        toml::from_str(&content)
            .map_err(|e| OrionError::Config(ConfigError::parse_error(&path_buf, format!("解析配置文件失败: {}", e))))
    }
    
    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| OrionError::SerializationError(format!("序列化配置失败: {}", e)))?;
        
        // 确保目录存在
        if let Some(parent) = path.as_ref().parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(parent, format!("创建配置目录失败: {}", e))))?;
        }
        
        let path_buf = path.as_ref().to_path_buf();
        std::fs::write(&path_buf, content)
            .map_err(|e| OrionError::Config(ConfigError::file_read_error(&path_buf, format!("写入配置文件失败: {}", e))))?;
        
        Ok(())
    }
    
    /// 验证配置（基础验证，确保必要目录存在）
    pub fn validate(&self) -> Result<()> {
        // 验证工作目录
        if !self.general.work_dir.exists() {
            std::fs::create_dir_all(&self.general.work_dir)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(&self.general.work_dir, format!("创建工作目录失败: {}", e))))?;
        }
        
        // 验证缓存目录
        if !self.tools.cache_dir.exists() {
            std::fs::create_dir_all(&self.tools.cache_dir)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(&self.tools.cache_dir, format!("创建缓存目录失败: {}", e))))?;
        }
        
        // 验证模板目录
        if !self.workflows.template_dir.exists() {
            std::fs::create_dir_all(&self.workflows.template_dir)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(&self.workflows.template_dir, format!("创建模板目录失败: {}", e))))?;
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_config_default() {
        let config = OrionConfig::default();
        assert_eq!(config.general.log_level, "info");
        assert_eq!(config.api.base_url, "https://api.orion-ai.com");
        assert_eq!(config.agents.default_model, "gpt-4");
    }

    #[test]
    fn test_config_file_operations() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let config_path = temp_dir.path().join("test_config.toml");

        // 创建并保存配置
        let config = OrionConfig::default();
        config.save_to_file(&config_path).expect("保存配置失败");
        assert!(config_path.exists());

        // 加载配置
        let loaded_config = OrionConfig::from_file(&config_path).expect("加载配置失败");
        assert_eq!(config.general.log_level, loaded_config.general.log_level);
        assert_eq!(config.api.base_url, loaded_config.api.base_url);
    }

    #[test]
    fn test_config_validation() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let mut config = OrionConfig::default();
        
        // 设置测试目录
        config.general.work_dir = temp_dir.path().join("work");
        config.tools.cache_dir = temp_dir.path().join("cache");
        config.workflows.template_dir = temp_dir.path().join("templates");

        // 验证应该成功并创建目录
        config.validate().expect("配置验证失败");
        
        assert!(config.general.work_dir.exists());
        assert!(config.tools.cache_dir.exists());
        assert!(config.workflows.template_dir.exists());
    }

    #[test]
    fn test_invalid_config_file() {
        let temp_dir = TempDir::new().expect("创建临时目录失败");
        let config_path = temp_dir.path().join("invalid_config.toml");

        // 写入无效的 TOML 内容
        std::fs::write(&config_path, "invalid toml content [[[").expect("写入无效配置失败");

        // 尝试加载应该失败
        let result = OrionConfig::from_file(&config_path);
        assert!(result.is_err());
    }

    #[test]
    fn test_nonexistent_config_file() {
        let result = OrionConfig::from_file("/nonexistent/path/config.toml");
        assert!(result.is_err());
    }
}