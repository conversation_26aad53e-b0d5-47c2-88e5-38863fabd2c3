//! # 消息系统统计信息模块
//!
//! 本模块负责收集、管理和报告消息系统的各种统计信息，
//! 包括消息发送接收数量、处理延迟、队列状态等关键指标。
//! 这些统计信息对于系统监控、性能优化和故障诊断非常重要。

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

/// 消息统计信息
/// 
/// 记录消息系统运行过程中的各种关键指标，
/// 用于性能监控、容量规划和问题诊断。
#[derive(Debug, Clone, Default)]
pub struct MessageStats {
    /// 发送的消息总数
    /// 
    /// 记录系统启动以来发送的所有消息数量
    pub messages_sent: u64,
    
    /// 接收的消息总数
    /// 
    /// 记录系统启动以来接收的所有消息数量
    pub messages_received: u64,
    
    /// 丢弃的消息数（过期或队列满）
    /// 
    /// 记录因各种原因被丢弃的消息数量，用于监控系统健康状态
    pub messages_dropped: u64,
    
    /// 当前队列长度
    /// 
    /// 当前待处理消息的数量，用于监控系统负载
    pub queue_length: usize,
    
    /// 平均处理延迟（毫秒）
    /// 
    /// 消息从发送到处理完成的平均时间，用于性能监控
    pub avg_processing_latency_ms: f64,
}

impl MessageStats {
    /// 创建新的统计信息实例
    /// 
    /// # 返回值
    /// 
    /// 返回初始化的统计信息实例
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 计算消息处理成功率
    /// 
    /// # 返回值
    /// 
    /// 返回成功处理的消息占总消息的百分比
    pub fn success_rate(&self) -> f64 {
        let total = self.messages_sent;
        if total == 0 {
            return 100.0;
        }
        
        let successful = total - self.messages_dropped;
        (successful as f64 / total as f64) * 100.0
    }
    
    /// 计算消息丢失率
    /// 
    /// # 返回值
    /// 
    /// 返回丢失消息占总消息的百分比
    pub fn drop_rate(&self) -> f64 {
        let total = self.messages_sent;
        if total == 0 {
            return 0.0;
        }
        
        (self.messages_dropped as f64 / total as f64) * 100.0
    }
    
    /// 获取吞吐量信息摘要
    /// 
    /// # 返回值
    /// 
    /// 返回格式化的吞吐量信息字符串
    pub fn throughput_summary(&self) -> String {
        format!(
            "发送: {}, 接收: {}, 丢弃: {}, 成功率: {:.2}%",
            self.messages_sent,
            self.messages_received,
            self.messages_dropped,
            self.success_rate()
        )
    }
}

/// 统计信息管理器
/// 
/// 负责统计信息的收集、更新和查询，
/// 提供线程安全的统计信息管理功能。
pub struct StatsManager {
    /// 统计数据存储
    stats: Arc<RwLock<MessageStats>>,
    
    /// 延迟测量相关数据
    latency_samples: Arc<RwLock<Vec<Duration>>>,
    
    /// 最大保留的延迟样本数
    max_latency_samples: usize,
}

impl StatsManager {
    /// 创建新的统计管理器
    /// 
    /// # 参数
    /// 
    /// * `max_latency_samples` - 最大保留的延迟样本数，用于计算平均延迟
    /// 
    /// # 返回值
    /// 
    /// 返回新的统计管理器实例
    pub fn new(max_latency_samples: usize) -> Self {
        Self {
            stats: Arc::new(RwLock::new(MessageStats::new())),
            latency_samples: Arc::new(RwLock::new(Vec::new())),
            max_latency_samples,
        }
    }
    
    /// 记录消息发送
    /// 
    /// 增加发送消息计数
    pub async fn record_message_sent(&self) {
        let mut stats = self.stats.write().await;
        stats.messages_sent += 1;
    }
    
    /// 记录消息接收
    /// 
    /// 增加接收消息计数
    pub async fn record_message_received(&self) {
        let mut stats = self.stats.write().await;
        stats.messages_received += 1;
    }
    
    /// 记录消息丢弃
    /// 
    /// 增加丢弃消息计数
    pub async fn record_message_dropped(&self) {
        let mut stats = self.stats.write().await;
        stats.messages_dropped += 1;
    }
    
    /// 更新队列长度
    /// 
    /// # 参数
    /// 
    /// * `length` - 当前队列长度
    pub async fn update_queue_length(&self, length: usize) {
        let mut stats = self.stats.write().await;
        stats.queue_length = length;
    }
    
    /// 记录处理延迟
    /// 
    /// 添加新的延迟样本并更新平均延迟
    /// 
    /// # 参数
    /// 
    /// * `latency` - 处理延迟时间
    pub async fn record_processing_latency(&self, latency: Duration) {
        // 更新延迟样本
        {
            let mut samples = self.latency_samples.write().await;
            samples.push(latency);
            
            // 保持样本数量在限制范围内
            if samples.len() > self.max_latency_samples {
                samples.remove(0);
            }
        }
        
        // 重新计算平均延迟
        self.update_average_latency().await;
    }
    
    /// 更新平均延迟
    /// 
    /// 基于当前的延迟样本重新计算平均延迟
    async fn update_average_latency(&self) {
        let samples = self.latency_samples.read().await;
        
        if samples.is_empty() {
            return;
        }
        
        let total_ms: f64 = samples
            .iter()
            .map(|d| d.as_millis() as f64)
            .sum();
        
        let avg_ms = total_ms / samples.len() as f64;
        
        let mut stats = self.stats.write().await;
        stats.avg_processing_latency_ms = avg_ms;
    }
    
    /// 获取当前统计信息
    /// 
    /// # 返回值
    /// 
    /// 返回当前统计信息的克隆
    pub async fn get_stats(&self) -> MessageStats {
        let stats = self.stats.read().await;
        stats.clone()
    }
    
    /// 重置统计信息
    /// 
    /// 清空所有统计数据，重新开始计数
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = MessageStats::new();
        
        let mut samples = self.latency_samples.write().await;
        samples.clear();
    }
    
    /// 获取详细的统计报告
    /// 
    /// # 返回值
    /// 
    /// 返回格式化的详细统计报告
    pub async fn get_detailed_report(&self) -> String {
        let stats = self.get_stats().await;
        let samples = self.latency_samples.read().await;
        
        format!(
            "=== 消息系统统计报告 ===\n\
             发送消息数: {}\n\
             接收消息数: {}\n\
             丢弃消息数: {}\n\
             当前队列长度: {}\n\
             平均处理延迟: {:.2} ms\n\
             消息成功率: {:.2}%\n\
             消息丢失率: {:.2}%\n\
             延迟样本数: {}",
            stats.messages_sent,
            stats.messages_received,
            stats.messages_dropped,
            stats.queue_length,
            stats.avg_processing_latency_ms,
            stats.success_rate(),
            stats.drop_rate(),
            samples.len()
        )
    }
}

impl Default for StatsManager {
    fn default() -> Self {
        Self::new(1000) // 默认保留 1000 个延迟样本
    }
}

/// 性能测量辅助结构
/// 
/// 用于测量操作的执行时间，自动记录到统计管理器中
pub struct PerformanceMeasurement {
    start_time: Instant,
    stats_manager: Arc<StatsManager>,
}

impl PerformanceMeasurement {
    /// 开始性能测量
    /// 
    /// # 参数
    /// 
    /// * `stats_manager` - 统计管理器引用
    /// 
    /// # 返回值
    /// 
    /// 返回性能测量实例
    pub fn start(stats_manager: Arc<StatsManager>) -> Self {
        Self {
            start_time: Instant::now(),
            stats_manager,
        }
    }
    
    /// 结束性能测量并记录结果
    /// 
    /// 计算从开始到现在的时间差，并记录到统计管理器中
    pub async fn finish(self) {
        let latency = self.start_time.elapsed();
        self.stats_manager.record_processing_latency(latency).await;
    }
}
