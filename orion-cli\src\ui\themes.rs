//! # 主题系统
//!
//! 提供多种终端主题，支持深色和浅色模式。

use crossterm::style::Color;

/// 主题类型
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ThemeType {
    Dark,
    Light,
}

/// 主题定义
#[derive(Debug, Clone)]
pub struct Theme {
    pub name: String,
    pub theme_type: ThemeType,
    pub foreground: Color,
    pub background: Color,
    pub light_blue: Color,
    pub accent_blue: Color,
    pub accent_purple: Color,
    pub accent_cyan: Color,
    pub accent_green: Color,
    pub accent_yellow: Color,
    pub accent_red: Color,
    pub diff_added: Color,
    pub diff_removed: Color,
    pub comment: Color,
    pub gray: Color,
    pub gradient_colors: Option<Vec<Color>>,
}

impl Default for Theme {
    fn default() -> Self {
        default_dark_theme()
    }
}

/// 默认深色主题
pub fn default_dark_theme() -> Theme {
    Theme {
        name: "默认深色".to_string(),
        theme_type: ThemeType::Dark,
        foreground: Color::Rgb { r: 220, g: 220, b: 220 },
        background: Color::Rgb { r: 30, g: 30, b: 30 },
        light_blue: Color::Rgb { r: 135, g: 206, b: 250 },
        accent_blue: Color::Rgb { r: 100, g: 149, b: 237 },
        accent_purple: Color::Rgb { r: 147, g: 112, b: 219 },
        accent_cyan: Color::Rgb { r: 64, g: 224, b: 208 },
        accent_green: Color::Rgb { r: 144, g: 238, b: 144 },
        accent_yellow: Color::Rgb { r: 255, g: 215, b: 0 },
        accent_red: Color::Rgb { r: 255, g: 99, b: 71 },
        diff_added: Color::Rgb { r: 50, g: 205, b: 50 },
        diff_removed: Color::Rgb { r: 255, g: 69, b: 0 },
        comment: Color::Rgb { r: 128, g: 128, b: 128 },
        gray: Color::Rgb { r: 169, g: 169, b: 169 },
        gradient_colors: Some(vec![
            Color::Rgb { r: 100, g: 149, b: 237 },
            Color::Rgb { r: 147, g: 112, b: 219 },
            Color::Rgb { r: 64, g: 224, b: 208 },
        ]),
    }
}

/// 默认浅色主题
pub fn default_light_theme() -> Theme {
    Theme {
        name: "默认浅色".to_string(),
        theme_type: ThemeType::Light,
        foreground: Color::Rgb { r: 40, g: 40, b: 40 },
        background: Color::Rgb { r: 248, g: 248, b: 248 },
        light_blue: Color::Rgb { r: 70, g: 130, b: 180 },
        accent_blue: Color::Rgb { r: 30, g: 100, b: 200 },
        accent_purple: Color::Rgb { r: 120, g: 80, b: 200 },
        accent_cyan: Color::Rgb { r: 20, g: 150, b: 180 },
        accent_green: Color::Rgb { r: 50, g: 150, b: 50 },
        accent_yellow: Color::Rgb { r: 200, g: 150, b: 0 },
        accent_red: Color::Rgb { r: 200, g: 50, b: 50 },
        diff_added: Color::Rgb { r: 0, g: 128, b: 0 },
        diff_removed: Color::Rgb { r: 200, g: 0, b: 0 },
        comment: Color::Rgb { r: 100, g: 100, b: 100 },
        gray: Color::Rgb { r: 120, g: 120, b: 120 },
        gradient_colors: Some(vec![
            Color::Rgb { r: 30, g: 100, b: 200 },
            Color::Rgb { r: 120, g: 80, b: 200 },
            Color::Rgb { r: 20, g: 150, b: 180 },
        ]),
    }
}

/// Dracula 主题
pub fn dracula_theme() -> Theme {
    Theme {
        name: "Dracula".to_string(),
        theme_type: ThemeType::Dark,
        foreground: Color::Rgb { r: 248, g: 248, b: 242 },
        background: Color::Rgb { r: 40, g: 42, b: 54 },
        light_blue: Color::Rgb { r: 139, g: 233, b: 253 },
        accent_blue: Color::Rgb { r: 98, g: 114, b: 164 },
        accent_purple: Color::Rgb { r: 189, g: 147, b: 249 },
        accent_cyan: Color::Rgb { r: 139, g: 233, b: 253 },
        accent_green: Color::Rgb { r: 80, g: 250, b: 123 },
        accent_yellow: Color::Rgb { r: 241, g: 250, b: 140 },
        accent_red: Color::Rgb { r: 255, g: 85, b: 85 },
        diff_added: Color::Rgb { r: 80, g: 250, b: 123 },
        diff_removed: Color::Rgb { r: 255, g: 85, b: 85 },
        comment: Color::Rgb { r: 98, g: 114, b: 164 },
        gray: Color::Rgb { r: 68, g: 71, b: 90 },
        gradient_colors: Some(vec![
            Color::Rgb { r: 189, g: 147, b: 249 },
            Color::Rgb { r: 255, g: 121, b: 198 },
            Color::Rgb { r: 139, g: 233, b: 253 },
        ]),
    }
}

/// GitHub 深色主题
pub fn github_dark_theme() -> Theme {
    Theme {
        name: "GitHub 深色".to_string(),
        theme_type: ThemeType::Dark,
        foreground: Color::Rgb { r: 230, g: 237, b: 243 },
        background: Color::Rgb { r: 13, g: 17, b: 23 },
        light_blue: Color::Rgb { r: 88, g: 166, b: 255 },
        accent_blue: Color::Rgb { r: 64, g: 120, b: 242 },
        accent_purple: Color::Rgb { r: 218, g: 54, b: 51 },
        accent_cyan: Color::Rgb { r: 86, g: 182, b: 194 },
        accent_green: Color::Rgb { r: 63, g: 185, b: 80 },
        accent_yellow: Color::Rgb { r: 255, g: 191, b: 0 },
        accent_red: Color::Rgb { r: 248, g: 81, b: 73 },
        diff_added: Color::Rgb { r: 63, g: 185, b: 80 },
        diff_removed: Color::Rgb { r: 248, g: 81, b: 73 },
        comment: Color::Rgb { r: 139, g: 148, b: 158 },
        gray: Color::Rgb { r: 110, g: 118, b: 129 },
        gradient_colors: Some(vec![
            Color::Rgb { r: 64, g: 120, b: 242 },
            Color::Rgb { r: 218, g: 54, b: 51 },
        ]),
    }
}

/// Monokai 主题
pub fn monokai_theme() -> Theme {
    Theme {
        name: "Monokai".to_string(),
        theme_type: ThemeType::Dark,
        foreground: Color::Rgb { r: 248, g: 248, b: 242 },
        background: Color::Rgb { r: 39, g: 40, b: 34 },
        light_blue: Color::Rgb { r: 102, g: 217, b: 239 },
        accent_blue: Color::Rgb { r: 102, g: 217, b: 239 },
        accent_purple: Color::Rgb { r: 174, g: 129, b: 255 },
        accent_cyan: Color::Rgb { r: 161, g: 239, b: 228 },
        accent_green: Color::Rgb { r: 166, g: 226, b: 46 },
        accent_yellow: Color::Rgb { r: 230, g: 219, b: 116 },
        accent_red: Color::Rgb { r: 249, g: 38, b: 114 },
        diff_added: Color::Rgb { r: 166, g: 226, b: 46 },
        diff_removed: Color::Rgb { r: 249, g: 38, b: 114 },
        comment: Color::Rgb { r: 117, g: 113, b: 94 },
        gray: Color::Rgb { r: 117, g: 113, b: 94 },
        gradient_colors: Some(vec![
            Color::Rgb { r: 249, g: 38, b: 114 },
            Color::Rgb { r: 230, g: 219, b: 116 },
            Color::Rgb { r: 166, g: 226, b: 46 },
        ]),
    }
}

/// 主题管理器
pub struct ThemeManager {
    themes: Vec<Theme>,
    active_theme_index: usize,
}

impl ThemeManager {
    /// 创建新的主题管理器
    pub fn new() -> Self {
        let themes = vec![
            default_dark_theme(),
            default_light_theme(),
            dracula_theme(),
            github_dark_theme(),
            monokai_theme(),
        ];

        Self {
            themes,
            active_theme_index: 0,
        }
    }

    /// 获取当前活动主题
    pub fn get_active_theme(&self) -> &Theme {
        &self.themes[self.active_theme_index]
    }

    /// 设置活动主题
    pub fn set_active_theme(&mut self, index: usize) -> Result<(), String> {
        if index >= self.themes.len() {
            return Err(format!("主题索引 {} 超出范围", index));
        }
        self.active_theme_index = index;
        Ok(())
    }

    /// 根据名称设置主题
    pub fn set_theme_by_name(&mut self, name: &str) -> Result<(), String> {
        for (i, theme) in self.themes.iter().enumerate() {
            if theme.name == name {
                self.active_theme_index = i;
                return Ok(());
            }
        }
        Err(format!("未找到名为 '{}' 的主题", name))
    }

    /// 获取所有主题
    pub fn get_themes(&self) -> &[Theme] {
        &self.themes
    }

    /// 添加自定义主题
    pub fn add_theme(&mut self, theme: Theme) {
        self.themes.push(theme);
    }

    /// 切换到下一个主题
    pub fn next_theme(&mut self) {
        self.active_theme_index = (self.active_theme_index + 1) % self.themes.len();
    }

    /// 切换到上一个主题
    pub fn previous_theme(&mut self) {
        if self.active_theme_index == 0 {
            self.active_theme_index = self.themes.len() - 1;
        } else {
            self.active_theme_index -= 1;
        }
    }

    /// 自动选择主题（基于终端背景）
    pub fn auto_select_theme(&mut self) {
        // 简单的启发式方法：根据环境变量或时间选择
        if let Ok(term_program) = std::env::var("TERM_PROGRAM") {
            match term_program.as_str() {
                "vscode" => {
                    let _ = self.set_theme_by_name("GitHub 深色");
                }
                _ => {
                    let _ = self.set_theme_by_name("默认深色");
                }
            }
        }
    }

    /// 获取主题列表字符串
    pub fn list_themes(&self) -> String {
        let mut result = String::new();
        for (i, theme) in self.themes.iter().enumerate() {
            let marker = if i == self.active_theme_index { "* " } else { "  " };
            result.push_str(&format!("{}{} ({:?})\n", marker, theme.name, theme.theme_type));
        }
        result
    }
}

impl Default for ThemeManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_theme_manager_creation() {
        let manager = ThemeManager::new();
        assert!(!manager.themes.is_empty());
        assert_eq!(manager.active_theme_index, 0);
    }

    #[test]
    fn test_theme_switching() {
        let mut manager = ThemeManager::new();
        let initial_theme = manager.get_active_theme().name.clone();
        
        manager.next_theme();
        let second_theme = manager.get_active_theme().name.clone();
        
        assert_ne!(initial_theme, second_theme);
    }

    #[test]
    fn test_theme_by_name() {
        let mut manager = ThemeManager::new();
        assert!(manager.set_theme_by_name("Dracula").is_ok());
        assert_eq!(manager.get_active_theme().name, "Dracula");
    }

    #[test]
    fn test_invalid_theme_name() {
        let mut manager = ThemeManager::new();
        assert!(manager.set_theme_by_name("NonExistent").is_err());
    }
}