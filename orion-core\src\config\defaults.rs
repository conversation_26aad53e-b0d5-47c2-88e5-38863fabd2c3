//! # 默认配置实现
//!
//! 为配置结构体提供合理的默认值。

use crate::config::types::*;
use std::path::PathBuf;

impl Default for OrionConfig {
    fn default() -> Self {
        Self {
            general: GeneralConfig::default(),
            api: ApiConfig::default(),
            agents: AgentConfig::default(),
            tools: ToolsConfig::default(),
            workflows: WorkflowsConfig::default(),
            logging: LoggingConfig::default(),
            security: SecurityConfig::default(),
        }
    }
}

impl Default for GeneralConfig {
    fn default() -> Self {
        Self {
            log_level: "info".to_string(),
            work_dir: dirs::home_dir()
                .unwrap_or_else(|| PathBuf::from("."))
                .join("orion-workspace"),
            output_format: "table".to_string(),
            enable_color: true,
            enable_profiling: false,
        }
    }
}

impl Default for ApiConfig {
    fn default() -> Self {
        Self {
            base_url: "https://api.orion-ai.com".to_string(),
            api_key: None,
            timeout: 30,
            max_retries: 3,
            retry_interval: 1,
        }
    }
}

impl Default for AgentConfig {
    fn default() -> Self {
        Self {
            default_model: "gpt-4".to_string(),
            max_concurrent: 5,
            auto_start: false,
            default_timeout: 300,
            max_context_length: 8192,
        }
    }
}

impl Default for ToolsConfig {
    fn default() -> Self {
        Self {
            registry_url: "https://tools.orion-ai.com".to_string(),
            auto_update: true,
            cache_dir: dirs::cache_dir()
                .unwrap_or_else(|| PathBuf::from(".cache"))
                .join("orion")
                .join("tools"),
            execution_timeout: 60,
            max_concurrent_executions: 10,
        }
    }
}

impl Default for WorkflowsConfig {
    fn default() -> Self {
        Self {
            template_dir: dirs::config_dir()
                .unwrap_or_else(|| PathBuf::from(".config"))
                .join("orion")
                .join("templates"),
            max_execution_time: 3600,
            auto_save: true,
            max_concurrent_workflows: 3,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: "pretty".to_string(),
            file_path: None,
            enable_console: true,
            enable_file: false,
            rotation_size: 100,
            retention_count: 10,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enable_sandbox: true,
            sandbox: SandboxConfig::default(),
            allowed_domains: vec![
                "api.openai.com".to_string(),
                "api.anthropic.com".to_string(),
            ],
            blocked_commands: vec![
                "rm".to_string(),
                "del".to_string(),
                "format".to_string(),
            ],
        }
    }
}

impl Default for SandboxConfig {
    fn default() -> Self {
        Self {
            memory_limit: 512,
            cpu_limit: 50,
            timeout: 300,
            temp_dir: std::env::temp_dir().join("orion-sandbox"),
        }
    }
}