//! # Agent 模块
//!
//! 提供智能Agent的完整实现，包括任务处理、状态管理、思考过程、
//! 统计信息收集等功能。本模块采用模块化设计，将不同功能分离到
//! 独立的子模块中，提高代码的可维护性和可读性。
//!
//! ## 模块结构
//!
//! - `types` - 核心类型定义，包括状态、任务、响应等类型
//! - `config` - Agent配置管理，包括各种策略和参数设置
//! - `state` - 状态管理，负责Agent状态转换和生命周期管理
//! - `thinking` - 思考过程管理，记录和分析Agent的决策过程
//! - `stats` - 统计信息管理，收集和分析Agent的性能数据
//! - `task` - 任务处理，实现不同类型任务的处理逻辑
//! - `core` - 核心实现，整合所有子模块提供统一接口

// 子模块声明
pub mod types;
pub mod config;
pub mod state;
pub mod thinking;
pub mod stats;
pub mod task;
pub mod core;

// 重新导出核心类型和结构体，保持向后兼容性
pub use self::core::Agent;
pub use self::config::{AgentConfig, ToolUseStrategy, AgentErrorHandlingStrategy};
pub use self::types::{
    AgentState, AgentStats, TaskRequest, TaskResponse, TaskType, TaskPriority,
    ResponseType, ThinkingProcess, ThinkingStep, ThinkingStepType,
};
pub use self::state::StateManager;
pub use self::thinking::ThinkingManager;
pub use self::stats::StatsManager;
pub use self::task::TaskProcessor;

// 为了保持与原始代码的兼容性，我们需要实现MessageHandler trait
use crate::message::{Message, MessageHandler, MessagePayload};
use crate::error::Result;
use async_trait::async_trait;
use std::collections::HashMap;
use uuid::Uuid;

#[async_trait]
impl MessageHandler for Agent {
    /// 处理消息
    /// 
    /// 实现MessageHandler trait，使Agent能够处理来自消息总线的消息
    async fn handle_message(&self, message: Message) -> Result<Option<Message>> {
        match message.payload {
            MessagePayload::TaskRequest { task_id, description, parameters } => {
                // 解析任务类型
                let task_type_str = parameters.get("task_type")
                    .and_then(|v| v.as_str())
                    .unwrap_or("question");

                // 创建任务请求
                let task = TaskRequest {
                    id: Uuid::parse_str(&task_id).unwrap_or_else(|_| Uuid::new_v4()),
                    content: description,
                    task_type: match task_type_str {
                        "question" => TaskType::Question,
                        "code_generation" => TaskType::CodeGeneration,
                        "document_analysis" => TaskType::DocumentAnalysis,
                        "data_processing" => TaskType::DataProcessing,
                        "workflow_execution" => TaskType::WorkflowExecution,
                        "tool_invocation" => TaskType::ToolInvocation,
                        custom => TaskType::Custom(custom.to_string()),
                    },
                    session_id: message.session_id,
                    user_id: None,
                    priority: TaskPriority::Normal,
                    parameters,
                    context: HashMap::new(),
                    created_at: message.timestamp,
                    deadline: None,
                };

                // 异步处理任务
                let agent = self.clone();
                let message_from = message.from.clone();
                let message_bus = self.message_bus().clone();
                
                tokio::spawn(async move {
                    match agent.process_task(task).await {
                        Ok(response) => {
                            // 发送响应消息
                            let response_message = Message::new(
                                "agent".to_string(),
                                message_from,
                                MessagePayload::TaskResponse {
                                    task_id: response.task_id.to_string(),
                                    success: response.success,
                                    result: Some(serde_json::json!({
                                        "content": response.content,
                                        "metadata": response.metadata
                                    })),
                                    error: response.error,
                                },
                            );

                            if let Err(e) = message_bus.send_message(response_message).await {
                                tracing::error!("发送响应消息失败: {}", e);
                            }
                        }
                        Err(e) => {
                            tracing::error!("处理任务失败: {}", e);
                        }
                    }
                });
            }
            MessagePayload::SteeringCommand { command, target: _, parameters: _ } => {
                // 处理实时Steering命令
                match command.as_str() {
                    "pause" => {
                        if let Err(e) = self.pause().await {
                            tracing::error!("暂停Agent失败: {}", e);
                        }
                    }
                    "resume" => {
                        if let Err(e) = self.resume().await {
                            tracing::error!("恢复Agent失败: {}", e);
                        }
                    }
                    "stop" => {
                        if let Err(e) = self.stop().await {
                            tracing::error!("停止Agent失败: {}", e);
                        }
                    }
                    _ => {
                        tracing::warn!("未知的Steering命令: {}", command);
                    }
                }
            }
            _ => {
                // 忽略其他类型的消息
            }
        }

        Ok(None)
    }

    /// 获取处理器ID
    fn handler_id(&self) -> String {
        self.get_config().id.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::llm::LlmEngine;
    use crate::tools::ToolRegistry;
    use crate::context::ContextManager;
    use crate::workflow::WorkflowManager;
    use crate::message::MessageBus;
    use std::sync::Arc;

    /// 测试Agent创建
    #[tokio::test]
    async fn test_agent_creation() {
        let config = AgentConfig::default();
        let llm_engine = Arc::new(LlmEngine::new());
        let tool_registry = Arc::new(ToolRegistry::new());
        let context_manager = Arc::new(ContextManager::default());
        let message_bus = Arc::new(MessageBus::new());
        let workflow_manager = Arc::new(WorkflowManager::new(
            Arc::new(crate::workflow::DefaultWorkflowExecutor::new(
                tool_registry.clone(),
                message_bus.clone(),
                10,
            ))
        ));

        let agent = Agent::new(
            config.clone(),
            llm_engine,
            tool_registry,
            context_manager,
            workflow_manager,
            message_bus,
        ).unwrap();

        assert_eq!(agent.get_config().name, config.name);
        assert_eq!(agent.get_state().await, AgentState::Idle);
    }

    /// 测试Agent状态管理
    #[tokio::test]
    async fn test_agent_state_management() {
        let config = AgentConfig::default();
        let llm_engine = Arc::new(LlmEngine::new());
        let tool_registry = Arc::new(ToolRegistry::new());
        let context_manager = Arc::new(ContextManager::default());
        let message_bus = Arc::new(MessageBus::new());
        let workflow_manager = Arc::new(WorkflowManager::new(
            Arc::new(crate::workflow::DefaultWorkflowExecutor::new(
                tool_registry.clone(),
                message_bus.clone(),
                10,
            ))
        ));

        let agent = Agent::new(
            config,
            llm_engine,
            tool_registry,
            context_manager,
            workflow_manager,
            message_bus,
        ).unwrap();

        // 测试暂停和恢复
        agent.pause().await.unwrap();
        assert_eq!(agent.get_state().await, AgentState::Paused);

        agent.resume().await.unwrap();
        assert_eq!(agent.get_state().await, AgentState::Idle);

        // 测试停止
        agent.stop().await.unwrap();
        assert_eq!(agent.get_state().await, AgentState::Stopped);
    }

    /// 测试任务请求创建
    #[tokio::test]
    async fn test_task_request_creation() {
        let task = TaskRequest {
            id: Uuid::new_v4(),
            content: "测试任务".to_string(),
            task_type: TaskType::Question,
            session_id: Some("test_session".to_string()),
            user_id: Some("test_user".to_string()),
            priority: TaskPriority::Normal,
            parameters: HashMap::new(),
            context: HashMap::new(),
            created_at: std::time::SystemTime::now(),
            deadline: None,
        };

        assert_eq!(task.content, "测试任务");
        assert_eq!(task.task_type, TaskType::Question);
        assert_eq!(task.priority, TaskPriority::Normal);
    }
}
