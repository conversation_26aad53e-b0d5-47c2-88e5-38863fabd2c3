技术栈与架构文档 - 项目: Orion
1. 概述
本文档定义了 Orion 项目的技术选型、核心架构和开发规范。所有技术选择都旨在最大化性能、安全性、可靠性和开发者体验。

2. 核心语言与运行时
语言: Rust (2021 Edition)

理由: 内存安全、零成本抽象、顶级的并发性能和强大的类型系统。这些特性是构建一个高性能、高可靠性 Agent 运行时的不二之选。

异步运行时: Tokio

理由: 事实上的行业标准，拥有最成熟的生态、最高的性能和最丰富的社区支持。非常适合构建 Orion 的高并发消息总线和网络请求。

3. 核心架构：模块化与解耦
Orion 的架构被严格分为两个层次：

orion-core (核心库): 一个独立的 Rust 库 (crate)，包含所有核心业务逻辑，如 Agent 调度、LLM 通信、插件管理、安全沙箱等。它不包含任何 UI 代码，并暴露一组稳定的公共 API。

orion-cli (表现层): 一个轻量级的二进制应用，它依赖于 orion-core。它的唯一职责是处理命令行输入、调用 orion-core 的 API，并将结果呈现给用户。

理由: 这种解耦设计是项目的基石。它确保了核心逻辑的可重用性，为未来构建 IDE 插件、Web UI 等其他表现层铺平了道路。

4. 技术栈详情
模块/领域

主要技术/库 (Crate)

理由与说明

命令行界面 (CLI)

clap

功能最强大、最灵活的命令行参数解析库。



ratatui

构建复杂、高性能的终端用户界面（TUI）的首选，用于实现“Agent 控制塔”。



rustyline

提供强大的 REPL (Read-Eval-Print Loop) 功能，用于构建交互式 Shell。

日志与追踪

tracing

一个结构化、可配置的日志框架，与 tokio 深度集成，便于观测异步任务。

配置管理

serde + toml

serde 是 Rust 中序列化/反序列化的标准。TOML 格式因其清晰易读，被选为主要的配置文件格式。

错误处理

thiserror + anyhow

thiserror 用于在 orion-core 库中定义具体的、结构化的错误类型。anyhow 用于在 orion-cli 应用层中简化错误处理链。

HTTP 客户端

reqwest

一个功能丰富、符合人体工程学的高级 HTTP 客户端，用于与 LLM API 和其他 Web 服务通信。

插件系统架构

WASM (WebAssembly) + wasmtime

我们选择 WASM 作为插件的格式。理由: 1. 安全性: WASM 运行在一个完全隔离的沙箱中，插件无法访问主机系统的任何资源，除非被明确授权。2. 跨语言: 社区可以用任何能编译到 WASM 的语言（如 Go, C++, TypeScript）来编写插件。3. 性能: wasmtime 提供近乎原生的执行速度。

数据库 (本地)

rusqlite

用于在本地存储任务历史、长期记忆等。SQLite 是一个轻量级、无服务器、单文件的数据库，非常适合嵌入式应用。

5. 开发与 CI/CD
代码格式化: rustfmt (强制执行)

代码质量检查: clippy (强制执行, a.k.a. "Clippy-Driven Development")

版本控制: Git

持续集成: GitHub Actions

工作流:

Check: 在每个 PR 上运行 cargo check, cargo fmt --check, cargo clippy -- -D warnings。

Test: 运行 cargo test --all-features。

Build & Release: 当创建新的 Git Tag 时，自动构建跨平台（Linux, macOS, Windows）的二进制文件，并创建 GitHub Release。