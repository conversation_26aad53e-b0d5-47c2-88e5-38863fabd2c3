//! # 显示配置功能
//!
//! 实现配置文件的显示和格式化功能。

use crate::error::{CliError, Result};
use crate::commands::config::{types::ShowConfig, utils::<PERSON><PERSON>eyChe<PERSON>};
use orion_core::config::OrionConfig;
use std::fs;

impl SecretKeyChecker for ShowConfig {}

impl ShowConfig {
    /// 执行显示配置
    pub async fn execute(&self) -> Result<()> {
        // 加载配置
        let config = OrionConfig::from_file(&self.config)
            .map_err(|e| CliError::ConfigError {
                error: format!("加载配置文件失败: {}", e),
            })?;
        
        // 根据格式输出
        let output = match self.format.as_str() {
            "json" => {
                let mut config_value = serde_json::to_value(&config)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化配置失败: {}", e),
                    })?;
                
                // 隐藏敏感信息
                if !self.show_secrets {
                    self.hide_secrets(&mut config_value);
                }
                
                serde_json::to_string_pretty(&config_value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化配置失败: {}", e),
                    })?
            }
            "yaml" => {
                let mut config_value = serde_yaml::to_value(&config)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化配置失败: {}", e),
                    })?;
                
                // 隐藏敏感信息
                if !self.show_secrets {
                    self.hide_secrets_yaml(&mut config_value);
                }
                
                serde_yaml::to_string(&config_value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化配置失败: {}", e),
                    })?
            }
            "toml" | _ => {
                let content = fs::read_to_string(&self.config)
                    .map_err(|e| CliError::IoError {
                        error: format!("读取配置文件失败: {}", e),
                    })?;
                
                if self.show_secrets {
                    content
                } else {
                    self.hide_secrets_toml(&content)
                }
            }
        };
        
        println!("{}", output);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    
    #[tokio::test]
    async fn test_show_config() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("test.toml");
        
        // 先创建配置文件
        let config = OrionConfig::default();
        config.save_to_file(&config_path).unwrap();
        
        let cmd = ShowConfig {
            config: config_path,
            format: "toml".to_string(),
            show_secrets: false,
            section: None,
        };
        
        assert!(cmd.execute().await.is_ok());
    }
}