//! # 工作流命令工具函数
//!
//! 提供工作流模块共用的工具函数和辅助功能。

use crate::error::{CliError, Result};
use orion_core::{
    config::OrionConfig,
    workflow::{WorkflowManager, DefaultWorkflowExecutor},
    tools::ToolRegistry,
    message::MessageBus,
};
use std::path::Path;
use std::sync::Arc;

/// 创建工作流管理器
pub async fn create_workflow_manager<P: AsRef<Path>>(config_path: P) -> Result<WorkflowManager> {
    let _config = OrionConfig::from_file(config_path)
        .map_err(|e| CliError::ConfigError {
            error: format!("加载配置失败: {}", e),
        })?;
    
    let message_bus = Arc::new(MessageBus::new());
    let tool_registry = Arc::new(ToolRegistry::new());
    
    let executor = Arc::new(DefaultWorkflowExecutor::new(
        tool_registry,
        message_bus,
        10, // max_concurrent_steps
    ));
    
    Ok(WorkflowManager::new(executor))
}

/// 格式化时间戳
pub fn format_timestamp(timestamp: u64) -> String {
    use chrono::DateTime;
    
    if let Some(datetime) = DateTime::from_timestamp(timestamp as i64, 0) {
        datetime.format("%Y-%m-%d %H:%M:%S").to_string()
    } else {
        "无效时间".to_string()
    }
}

/// 打印输出（支持多种格式）
pub fn print_output<T: serde::Serialize + ?Sized>(data: &T, format: &str) -> Result<()> {
    match format {
        "json" => {
            let json = serde_json::to_string_pretty(data)
                .map_err(|e| CliError::SerializationError {
                    error: format!("序列化失败: {}", e),
                })?;
            println!("{}", json);
        }
        "yaml" => {
            let yaml = serde_yaml::to_string(data)
                .map_err(|e| CliError::SerializationError {
                    error: format!("序列化失败: {}", e),
                })?;
            println!("{}", yaml);
        }
        _ => {
            // 默认格式由调用方处理
        }
    }
    Ok(())
}

/// 解析文件内容根据扩展名
pub async fn parse_file_content<T: for<'de> serde::Deserialize<'de>>(
    file_path: &std::path::Path,
    content: &str,
) -> Result<T> {
    let format = file_path.extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("yaml");

    match format {
        "json" => {
            serde_json::from_str(content)
                .map_err(|e| CliError::SerializationError {
                    error: format!("解析 JSON 文件失败: {}", e),
                })
        }
        "yaml" | "yml" => {
            serde_yaml::from_str(content)
                .map_err(|e| CliError::SerializationError {
                    error: format!("解析 YAML 文件失败: {}", e),
                })
        }
        "toml" => {
            toml::from_str(content)
                .map_err(|e| CliError::SerializationError {
                    error: format!("解析 TOML 文件失败: {}", e),
                })
        }
        _ => {
            Err(CliError::InvalidArgument {
                error: format!("不支持的文件格式: {}", format),
            })
        }
    }
}

/// 验证工作流ID格式
pub fn validate_workflow_id(id: &str) -> Result<uuid::Uuid> {
    uuid::Uuid::parse_str(id)
        .map_err(|e| CliError::ExecutionError {
            error: format!("无效的工作流ID: {}", e),
        })
}

/// 模拟处理延迟
pub async fn simulate_processing_delay(ms: u64) {
    tokio::time::sleep(tokio::time::Duration::from_millis(ms)).await;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_timestamp() {
        let timestamp = 1640995200; // 2022-01-01 00:00:00 UTC
        let formatted = format_timestamp(timestamp);
        assert_eq!(formatted, "2022-01-01 00:00:00");
    }

    #[test]
    fn test_validate_workflow_id() {
        let valid_id = "550e8400-e29b-41d4-a716-************";
        assert!(validate_workflow_id(valid_id).is_ok());
        
        let invalid_id = "invalid-id";
        assert!(validate_workflow_id(invalid_id).is_err());
    }
}