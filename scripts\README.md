# Orion 开发环境快速设置指南

## 🚀 立即开始（推荐）

### Windows 用户
```cmd
# 1. 使用开发脚本（推荐）
scripts\orion-dev.bat --help

# 2. 设置开发别名
doskey orion=E:\Orion\scripts\orion-dev.bat $*
```

### Linux/macOS 用户
```bash
# 1. 设置权限
chmod +x scripts/*.sh

# 2. 添加到环境变量
echo 'alias orion="E:/Orion/scripts/orion-dev.sh"' >> ~/.bashrc
source ~/.bashrc

# 3. 或者使用完整开发环境
source scripts/setup-dev-env.sh
```

## 🔄 热重载开发模式

### 方案一：智能缓存编译
```bash
# 只在代码变更时重新编译
orion --help          # 首次编译
orion run             # 直接运行（无需重编译）
# 修改代码...
orion run             # 自动检测变更并重编译
```

### 方案二：实时监控编译
```bash
# 启动开发服务器（另一个终端）
./scripts/dev-server.sh

# 在项目终端正常使用
target/debug/orion --help
target/debug/orion run
```

## ⚡ 性能对比

| 方案 | 首次编译 | 无变更调用 | 有变更调用 | 适用场景 |
|------|----------|------------|------------|----------|
| 传统 cargo run | ~30s | ~30s | ~30s | 偶尔调试 |
| 智能缓存脚本 | ~30s | <1s | ~30s | 频繁使用 |
| 热重载服务器 | ~30s | <1s | <5s | 密集开发 |
| cargo install | ~30s | <1s | 需重装 | 生产环境 |

## 🛠️ 开发工作流建议

```bash
# 1. 设置开发环境（一次性）
source scripts/setup-dev-env.sh

# 2. 日常开发
orion config init          # 自动编译+运行
orion run                  # 直接运行
orion knowledge init .     # 新功能测试

# 3. 密集开发时
orion-dev                  # 启动热重载服务器
# 在另一个终端编码和测试
```

## 🎯 解决的问题

✅ **无需频繁重装** - 智能检测代码变更
✅ **快速启动** - 无变更时秒级启动  
✅ **热重载** - 代码变更自动编译
✅ **全局可用** - 任意目录都能调用
✅ **开发友好** - 专为开发阶段优化