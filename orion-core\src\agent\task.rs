//! # Agent 任务处理模块
//!
//! 提供Agent任务处理的核心功能，包括不同类型任务的处理逻辑、
//! 工具调用、LLM交互等功能。

use crate::agent::config::AgentConfig;
use crate::agent::thinking::ThinkingManager;
use crate::agent::types::{TaskRequest, TaskType, ResponseType, ThinkingStepType};
use crate::context::{ContextManager, ContextQuery};
use crate::error::{OrionError, Result};
use crate::llm::{LlmEngine, LlmRequest, LlmMessage, MessageRole, ToolDefinition, ModelParameters};
use crate::tools::{ToolRegistry, ToolRequest, ToolResult, ToolContext};
use crate::workflow::WorkflowManager;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::SystemTime;
use uuid::Uuid;

/// 任务处理器
/// 
/// 负责处理不同类型的任务，协调各种资源和服务
pub struct TaskProcessor {
    /// Agent配置
    config: AgentConfig,
    /// LLM引擎
    llm_engine: Arc<LlmEngine>,
    /// 工具注册表
    tool_registry: Arc<ToolRegistry>,
    /// 上下文管理器
    context_manager: Arc<ContextManager>,
    /// 工作流管理器
    workflow_manager: Arc<WorkflowManager>,
    /// 思考过程管理器
    thinking_manager: Arc<ThinkingManager>,
}

impl TaskProcessor {
    /// 创建新的任务处理器
    /// 
    /// # 参数
    /// * `config` - Agent配置
    /// * `llm_engine` - LLM引擎
    /// * `tool_registry` - 工具注册表
    /// * `context_manager` - 上下文管理器
    /// * `workflow_manager` - 工作流管理器
    /// * `thinking_manager` - 思考过程管理器
    /// 
    /// # 返回
    /// 返回新创建的TaskProcessor实例
    pub fn new(
        config: AgentConfig,
        llm_engine: Arc<LlmEngine>,
        tool_registry: Arc<ToolRegistry>,
        context_manager: Arc<ContextManager>,
        workflow_manager: Arc<WorkflowManager>,
        thinking_manager: Arc<ThinkingManager>,
    ) -> Self {
        Self {
            config,
            llm_engine,
            tool_registry,
            context_manager,
            workflow_manager,
            thinking_manager,
        }
    }

    /// 处理任务的主入口
    /// 
    /// 根据任务类型分发到相应的处理方法
    /// 
    /// # 参数
    /// * `task` - 要处理的任务
    /// 
    /// # 返回
    /// 返回处理结果的元组：(内容, 响应类型, 工具结果, 生成文件, 元数据, 令牌使用)
    pub async fn process_task(
        &self,
        task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // 添加问题分析思考步骤
        self.thinking_manager.add_problem_analysis(
            format!("分析任务: {}", task.content),
            0.9,
        ).await;

        // 根据任务类型分发处理
        match task.task_type {
            TaskType::Question => self.handle_question_task(task).await,
            TaskType::CodeGeneration => self.handle_code_generation_task(task).await,
            TaskType::DocumentAnalysis => self.handle_document_analysis_task(task).await,
            TaskType::DataProcessing => self.handle_data_processing_task(task).await,
            TaskType::WorkflowExecution => self.handle_workflow_execution_task(task).await,
            TaskType::ToolInvocation => self.handle_tool_invocation_task(task).await,
            TaskType::Custom(ref custom_type) => self.handle_custom_task(task, custom_type).await,
        }
    }

    /// 处理问答任务
    /// 
    /// 处理用户的问题，可能涉及上下文检索和工具调用
    async fn handle_question_task(
        &self,
        task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // 添加策略规划思考步骤
        self.thinking_manager.add_strategy_planning(
            "规划问答策略".to_string(),
            0.8,
        ).await;

        // 获取相关上下文 - 优化对话历史检索
        let context_entries = if self.config.enable_memory {
            // 首先获取最近的对话历史（按时间顺序）
            let mut recent_conversation = if let Some(session_id) = &task.session_id {
                let query = ContextQuery {
                    session_id: Some(session_id.clone()),
                    query_text: None, // 不根据内容过滤，获取完整对话
                    entry_types: None,
                    tags: None,
                    min_relevance: None,
                    min_importance: None,
                    time_range: None,
                    limit: Some(20), // 增加对话历史数量
                    include_children: false,
                };
                let mut entries = self.context_manager.query_entries(query).await?;
                // 按时间顺序排序（从早到晚）
                entries.sort_by(|a, b| a.created_at.cmp(&b.created_at));
                entries
            } else {
                Vec::new()
            };

            // 如果对话历史不足，再根据内容相关性补充
            if recent_conversation.len() < 5 {
                let relevance_query = ContextQuery {
                    session_id: task.session_id.clone(),
                    query_text: Some(task.content.clone()),
                    entry_types: None,
                    tags: None,
                    min_relevance: Some(0.4), // 提高相关性阈值
                    min_importance: None,
                    time_range: None,
                    limit: Some(10),
                    include_children: false,
                };
                let relevant_entries = self.context_manager.query_entries(relevance_query).await?;
                
                // 合并并去重
                for entry in relevant_entries {
                    if !recent_conversation.iter().any(|e| e.id == entry.id) {
                        recent_conversation.push(entry);
                    }
                }
            }

            recent_conversation
        } else {
            Vec::new()
        };

        // 构建LLM请求
        let mut messages = vec![
            LlmMessage {
                role: MessageRole::System,
                content: self.config.system_prompt.clone(),
                metadata: HashMap::new(),
                timestamp: SystemTime::now(),
            },
        ];

        // 添加上下文信息 - 优化对话格式
        if !context_entries.is_empty() {
            // 构建结构化的对话历史
            let mut conversation_history = String::new();
            conversation_history.push_str("=== 对话历史 ===\n");
            
            for entry in &context_entries {
                match entry.entry_type {
                    crate::context::ContextEntryType::UserInput => {
                        conversation_history.push_str(&format!("用户: {}\n", entry.content));
                    },
                    crate::context::ContextEntryType::AgentResponse => {
                        conversation_history.push_str(&format!("助手: {}\n", entry.content));
                    },
                    _ => {
                        conversation_history.push_str(&format!("[系统] {}\n", entry.content));
                    }
                }
            }
            conversation_history.push_str("=== 对话历史结束 ===\n");
            
            // 添加对话上下文指导
            let context_instruction = format!(
                "{}{}",
                conversation_history,
                "请基于以上对话历史来理解当前用户的问题。如果这是一个游戏或有特定规则的对话，请严格按照之前建立的规则和模式来回应。保持对话的连贯性和一致性。"
            );

            messages.push(LlmMessage {
                role: MessageRole::System,
                content: context_instruction,
                metadata: HashMap::new(),
                timestamp: SystemTime::now(),
            });
        }

        // 添加用户问题
        messages.push(LlmMessage {
            role: MessageRole::User,
            content: task.content.clone(),
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        });

        // 获取可用工具
        let available_tools = self.get_available_tools().await;

        let llm_request = LlmRequest {
            id: Uuid::new_v4(),
            messages,
            parameters: Some(ModelParameters {
                temperature: self.config.temperature,
                max_tokens: self.config.max_tokens.unwrap_or(4096),
                top_p: None,
                top_k: None,
                frequency_penalty: None,
                presence_penalty: None,
                stop_sequences: vec![],
            }),
            tools: available_tools,
            streaming: Some(false),
            metadata: HashMap::new(),
        };

        // 调用LLM
        let llm_response = self.llm_engine.complete(llm_request).await?;

        // 处理工具调用
        let mut tool_results = Vec::new();
        if !llm_response.tool_calls.is_empty() {
            for tool_call in &llm_response.tool_calls {
                self.thinking_manager.add_tool_selection(
                    format!("选择工具: {}", tool_call.name),
                    0.9,
                ).await;

                let tool_result = self.execute_tool_call(tool_call).await?;
                tool_results.push(tool_result);
            }
        }

        let mut metadata = HashMap::new();
        metadata.insert("model".to_string(), serde_json::Value::String(self.config.default_model.clone()));
        metadata.insert("tool_calls_count".to_string(), serde_json::Value::Number(tool_results.len().into()));

        Ok((
            llm_response.content,
            ResponseType::Text,
            tool_results,
            Vec::new(),
            metadata,
            Some(llm_response.usage),
        ))
    }

    /// 处理代码生成任务
    /// 
    /// 生成代码片段或完整程序
    async fn handle_code_generation_task(
        &self,
        task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // 添加策略规划思考步骤
        self.thinking_manager.add_strategy_planning(
            "规划代码生成策略".to_string(),
            0.8,
        ).await;

        // 构建专门的代码生成提示
        let system_prompt = format!(
            "{} 你是一个专业的代码生成助手，请根据用户需求生成高质量的代码。",
            self.config.system_prompt
        );

        let messages = vec![
            LlmMessage {
                role: MessageRole::System,
                content: system_prompt,
                metadata: HashMap::new(),
                timestamp: SystemTime::now(),
            },
            LlmMessage {
                role: MessageRole::User,
                content: task.content.clone(),
                metadata: HashMap::new(),
                timestamp: SystemTime::now(),
            },
        ];

        let llm_request = LlmRequest {
            id: Uuid::new_v4(),
            messages,
            parameters: Some(ModelParameters {
                temperature: 0.3, // 降低温度以获得更确定的代码
                max_tokens: self.config.max_tokens.unwrap_or(4096),
                top_p: None,
                top_k: None,
                frequency_penalty: None,
                presence_penalty: None,
                stop_sequences: vec![],
            }),
            tools: vec![], // 代码生成通常不需要工具调用
            streaming: Some(false),
            metadata: HashMap::new(),
        };

        let llm_response = self.llm_engine.complete(llm_request).await?;

        let mut metadata = HashMap::new();
        metadata.insert("task_type".to_string(), serde_json::Value::String("code_generation".to_string()));
        metadata.insert("model".to_string(), serde_json::Value::String(self.config.default_model.clone()));

        Ok((
            llm_response.content,
            ResponseType::Code,
            Vec::new(),
            Vec::new(),
            metadata,
            Some(llm_response.usage),
        ))
    }

    /// 处理文档分析任务
    async fn handle_document_analysis_task(
        &self,
        _task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // TODO: 实现文档分析逻辑
        Err(OrionError::AgentError(
            "文档分析功能尚未实现".to_string()
        ))
    }

    /// 处理数据处理任务
    async fn handle_data_processing_task(
        &self,
        _task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // TODO: 实现数据处理逻辑
        Err(OrionError::AgentError(
            "数据处理功能尚未实现".to_string()
        ))
    }

    /// 处理工作流执行任务
    ///
    /// 执行预定义的工作流程
    async fn handle_workflow_execution_task(
        &self,
        task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // 从任务参数中获取工作流ID
        let workflow_id = task.parameters.get("workflow_id")
            .and_then(|v| v.as_str())
            .and_then(|s| Uuid::parse_str(s).ok())
            .ok_or_else(|| OrionError::AgentError(
                "缺少有效的工作流ID".to_string()
            ))?;

        // 获取输入参数
        let input_parameters = task.parameters.get("input_parameters")
            .and_then(|v| v.as_object())
            .map(|obj| {
                obj.iter()
                    .map(|(k, v)| (k.clone(), v.clone()))
                    .collect::<HashMap<String, serde_json::Value>>()
            })
            .unwrap_or_default();

        // 创建工作流实例
        let instance_id = self.workflow_manager.create_instance(
            workflow_id,
            format!("Agent任务执行-{}", task.id),
            input_parameters,
        ).await?;

        // 执行工作流
        self.workflow_manager.execute_instance(instance_id).await?;

        // 获取执行结果
        let instance = self.workflow_manager.get_instance(instance_id).await?;

        let mut metadata = HashMap::new();
        metadata.insert("workflow_id".to_string(), serde_json::Value::String(workflow_id.to_string()));
        metadata.insert("instance_id".to_string(), serde_json::Value::String(instance_id.to_string()));
        metadata.insert("workflow_status".to_string(), serde_json::json!(instance.status));

        let content = if instance.status == crate::workflow::WorkflowStatus::Completed {
            format!("工作流执行成功。输出参数: {:?}", instance.output_parameters)
        } else {
            format!("工作流执行失败: {:?}", instance.error)
        };

        Ok((
            content,
            ResponseType::Workflow,
            Vec::new(),
            Vec::new(),
            metadata,
            None,
        ))
    }

    /// 处理工具调用任务
    ///
    /// 直接调用指定的工具
    async fn handle_tool_invocation_task(
        &self,
        task: &TaskRequest,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // 从任务参数中获取工具名称和参数
        let tool_name = task.parameters.get("tool_name")
            .and_then(|v| v.as_str())
            .ok_or_else(|| OrionError::AgentError(
                "缺少工具名称".to_string()
            ))?
            .to_string();

        let tool_parameters = task.parameters.get("tool_parameters")
            .and_then(|v| v.as_object())
            .map(|obj| {
                obj.iter()
                    .map(|(k, v)| (k.clone(), v.clone()))
                    .collect::<HashMap<String, serde_json::Value>>()
            })
            .unwrap_or_default();

        // 创建工具请求
        let tool_request = ToolRequest {
            id: Uuid::new_v4(),
            tool_name: tool_name.clone(),
            parameters: tool_parameters,
            context: ToolContext::default(),
            timestamp: SystemTime::now(),
        };

        // 执行工具
        let tool_result = self.tool_registry.execute_tool(tool_request).await?;

        let mut metadata = HashMap::new();
        metadata.insert("tool_name".to_string(), serde_json::Value::String(tool_name));
        metadata.insert("tool_success".to_string(), serde_json::Value::Bool(tool_result.success));

        let content = if tool_result.success {
            format!("工具执行成功: {}", serde_json::to_string_pretty(&tool_result.data)?)
        } else {
            format!("工具执行失败: {}", tool_result.error.clone().unwrap_or_else(|| "未知错误".to_string()))
        };

        Ok((
            content,
            ResponseType::ToolCall,
            vec![tool_result],
            Vec::new(),
            metadata,
            None,
        ))
    }

    /// 处理自定义任务
    ///
    /// 处理用户定义的特殊任务类型
    async fn handle_custom_task(
        &self,
        _task: &TaskRequest,
        _custom_type: &str,
    ) -> Result<(
        String,
        ResponseType,
        Vec<ToolResult>,
        Vec<String>,
        HashMap<String, serde_json::Value>,
        Option<crate::llm::TokenUsage>,
    )> {
        // TODO: 实现自定义任务处理逻辑
        Err(OrionError::AgentError(
            "自定义任务功能尚未实现".to_string()
        ))
    }

    /// 执行工具调用
    ///
    /// 执行LLM请求的工具调用
    ///
    /// # 参数
    /// * `tool_call` - 工具调用信息
    ///
    /// # 返回
    /// 返回工具执行结果
    async fn execute_tool_call(
        &self,
        tool_call: &crate::llm::ToolCall,
    ) -> Result<ToolResult> {
        self.thinking_manager.add_step(
            ThinkingStepType::ParameterPreparation,
            format!("准备工具参数: {}", tool_call.arguments),
            0.8,
        ).await;

        // 解析工具参数
        let parameters: HashMap<String, serde_json::Value> = if tool_call.arguments.is_object() {
            tool_call.arguments.as_object().unwrap().clone().into_iter()
                .map(|(k, v)| (k, v.clone()))
                .collect()
        } else {
            HashMap::new()
        };

        // 创建工具请求
        let tool_request = ToolRequest {
            id: Uuid::new_v4(),
            tool_name: tool_call.name.clone(),
            parameters,
            context: ToolContext::default(),
            timestamp: SystemTime::now(),
        };

        // 执行工具
        let tool_result = self.tool_registry.execute_tool(tool_request).await?;

        self.thinking_manager.add_result_evaluation(
            format!("工具执行结果: {}", if tool_result.success { "成功" } else { "失败" }),
            if tool_result.success { 0.9 } else { 0.3 },
        ).await;

        Ok(tool_result)
    }

    /// 获取可用工具列表
    ///
    /// # 返回
    /// 返回当前可用的工具定义列表
    pub async fn get_available_tools(&self) -> Vec<ToolDefinition> {
        self.tool_registry.list_tools().await
            .into_iter()
            .map(|tool_def| ToolDefinition {
                name: tool_def.name,
                description: tool_def.description,
                parameters: tool_def.parameters.into_iter()
                    .map(|param| (param.name, serde_json::json!({
                        "type": param.param_type,
                        "description": param.description,
                        "required": param.required
                    })))
                    .collect(),
            })
            .collect()
    }
}
