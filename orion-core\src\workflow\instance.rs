//! # 工作流实例模块
//!
//! 包含工作流实例相关的数据结构。
//! 定义了工作流的运行时状态、执行上下文、统计信息等。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use uuid::Uuid;

/// 工作流实例
/// 
/// 表示一个正在执行或已完成的工作流实例。
/// 包含运行时状态、执行进度、输入输出参数等信息。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowInstance {
    /// 实例唯一标识符
    pub id: Uuid,
    
    /// 关联的工作流定义ID
    pub workflow_id: Uuid,
    
    /// 实例名称，用于标识和显示
    pub name: String,
    
    /// 当前执行状态
    pub status: WorkflowStatus,
    
    /// 输入参数值
    /// 键为参数名，值为参数值（JSON格式）
    pub input_parameters: HashMap<String, serde_json::Value>,
    
    /// 输出参数值
    /// 键为参数名，值为参数值（JSON格式）
    pub output_parameters: HashMap<String, serde_json::Value>,
    
    /// 当前正在执行的步骤ID
    pub current_step_id: Option<Uuid>,
    
    /// 已完成的步骤ID列表
    pub completed_steps: Vec<Uuid>,
    
    /// 执行失败的步骤ID列表
    pub failed_steps: Vec<Uuid>,
    
    /// 被跳过的步骤ID列表
    pub skipped_steps: Vec<Uuid>,
    
    /// 执行上下文，包含运行时数据
    pub execution_context: ExecutionContext,
    
    /// 实例创建时间
    pub created_at: SystemTime,
    
    /// 开始执行时间
    pub started_at: Option<SystemTime>,
    
    /// 执行完成时间
    pub completed_at: Option<SystemTime>,
    
    /// 错误信息（如果执行失败）
    pub error: Option<String>,
}

/// 工作流状态枚举
/// 
/// 定义工作流实例的各种执行状态。
/// 状态转换遵循特定的生命周期规则。
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WorkflowStatus {
    /// 待执行状态
    /// 实例已创建但尚未开始执行
    Pending,
    
    /// 执行中状态
    /// 工作流正在执行步骤
    Running,
    
    /// 已完成状态
    /// 工作流成功执行完所有步骤
    Completed,
    
    /// 失败状态
    /// 工作流执行过程中遇到无法恢复的错误
    Failed,
    
    /// 已取消状态
    /// 工作流被用户或系统主动取消
    Cancelled,
    
    /// 暂停状态
    /// 工作流执行被暂停，可以恢复
    Paused,
    
    /// 等待人工干预状态
    /// 工作流在等待人工处理某个步骤
    WaitingForIntervention,
}

/// 执行上下文
/// 
/// 存储工作流执行过程中的运行时数据。
/// 包括变量、步骤结果、统计信息等。
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ExecutionContext {
    /// 全局变量存储
    /// 键为变量名，值为变量值（JSON格式）
    pub variables: HashMap<String, serde_json::Value>,
    
    /// 步骤执行结果存储
    /// 键为步骤ID，值为步骤输出结果
    pub step_results: HashMap<Uuid, serde_json::Value>,
    
    /// 执行统计信息
    pub execution_stats: ExecutionStats,
    
    /// 检查点列表
    /// 用于错误恢复和状态回滚
    pub checkpoints: Vec<Checkpoint>,
    
    /// 会话ID（可选）
    /// 用于关联用户会话或请求
    pub session_id: Option<String>,
    
    /// 用户ID（可选）
    /// 标识触发工作流的用户
    pub user_id: Option<String>,
}

/// 执行统计信息
/// 
/// 记录工作流执行过程中的各种统计数据。
/// 用于性能监控和分析。
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ExecutionStats {
    /// 总步骤数
    pub total_steps: u32,
    
    /// 已完成步骤数
    pub completed_steps: u32,
    
    /// 失败步骤数
    pub failed_steps: u32,
    
    /// 跳过步骤数
    pub skipped_steps: u32,
    
    /// 总执行时间（毫秒）
    pub total_execution_time_ms: u64,
    
    /// 平均步骤执行时间（毫秒）
    pub average_step_time_ms: u64,
}

/// 检查点
/// 
/// 保存工作流执行过程中的状态快照。
/// 用于错误恢复和状态回滚功能。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Checkpoint {
    /// 检查点唯一标识符
    pub id: Uuid,
    
    /// 检查点名称
    pub name: String,
    
    /// 检查点对应的步骤ID
    pub step_id: Uuid,
    
    /// 变量状态快照
    /// 保存创建检查点时的所有变量值
    pub variable_snapshot: HashMap<String, serde_json::Value>,
    
    /// 检查点创建时间
    pub created_at: SystemTime,
}
