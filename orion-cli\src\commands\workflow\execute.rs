//! # 工作流执行功能
//!
//! 实现工作流的执行、监控和显示功能。

use crate::error::{CliError, Result};
use crate::commands::workflow::{types::*, utils::*};
use orion_core::workflow::{WorkflowInstance, WorkflowStatus};
use std::collections::HashMap;
use uuid::Uuid;

impl RunWorkflow {
    /// 执行运行工作流
    pub async fn execute(&self) -> Result<()> {
        let workflow_manager = create_workflow_manager(&self.config).await?;

        // 解析输入参数
        let input_params = self.parse_input_parameters().await?;

        // 解析工作流ID
        let workflow_id = validate_workflow_id(&self.workflow)?;

        // 创建工作流实例
        let instance_id = workflow_manager.create_instance(workflow_id, "CLI实例".to_string(), input_params).await
            .map_err(|e| CliError::ExecutionError {
                error: format!("创建工作流实例失败: {}", e),
            })?;

        println!("🚀 启动工作流实例: {}", instance_id);

        if self.async_run {
            // 异步执行
            workflow_manager.execute_instance(instance_id).await
                .map_err(|e| CliError::ExecutionError {
                    error: format!("启动工作流执行失败: {}", e),
                })?;

            println!("✅ 工作流已在后台启动");
            println!("💡 使用 'orion workflow show {}' 查看执行状态", instance_id);
        } else {
            // 同步执行
            let timeout = self.timeout.map(std::time::Duration::from_secs);

            if self.monitor {
                // 监控执行过程
                self.execute_with_monitoring(&workflow_manager, instance_id, timeout).await?
            } else {
                // 直接执行
                workflow_manager.execute_instance(instance_id).await
                    .map_err(|e| CliError::ExecutionError {
                        error: format!("执行工作流失败: {}", e),
                    })?;

                println!("✅ 工作流执行完成");
            }
        }

        Ok(())
    }
    
    /// 解析输入参数
    async fn parse_input_parameters(&self) -> Result<HashMap<String, serde_json::Value>> {
        let mut params = HashMap::new();
        
        // 从命令行参数解析
        if let Some(input_str) = &self.input {
            let parsed: HashMap<String, serde_json::Value> = serde_json::from_str(input_str)
                .map_err(|e| CliError::InvalidArgument {
                    error: format!("解析输入参数失败: {}", e),
                })?;
            params.extend(parsed);
        }
        
        // 从文件解析
        if let Some(input_file) = &self.input_file {
            let content = tokio::fs::read_to_string(input_file).await
                .map_err(|e| CliError::IoError {
                    error: format!("读取输入文件失败: {}", e),
                })?;

            let file_params: HashMap<String, serde_json::Value> = if input_file.extension()
                .and_then(|ext| ext.to_str()) == Some("yaml") {
                serde_yaml::from_str(&content)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("解析 YAML 输入文件失败: {}", e),
                    })?
            } else {
                serde_json::from_str(&content)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("解析 JSON 输入文件失败: {}", e),
                    })?
            };

            params.extend(file_params);
        }
        
        Ok(params)
    }
    
    /// 监控执行过程
    async fn execute_with_monitoring(
        &self,
        workflow_manager: &orion_core::workflow::WorkflowManager,
        instance_id: Uuid,
        timeout: Option<std::time::Duration>,
    ) -> Result<()> {
        use std::time::{Duration, Instant};

        // 启动执行
        workflow_manager.execute_instance(instance_id).await
            .map_err(|e| CliError::ExecutionError {
                error: format!("启动工作流执行失败: {}", e),
            })?;

        let start_time = Instant::now();
        let mut last_status = WorkflowStatus::Running;

        println!("📊 监控工作流执行...");

        loop {
            // 检查超时
            if let Some(timeout_duration) = timeout {
                if start_time.elapsed() > timeout_duration {
                    println!("⏰ 执行超时");
                    return Err(CliError::ExecutionError {
                        error: "工作流执行超时".to_string(),
                    });
                }
            }

            // 获取实例状态
            let instance = workflow_manager.get_instance(instance_id).await
                .map_err(|e| CliError::ExecutionError {
                    error: format!("获取工作流实例失败: {}", e),
                })?;

            // 状态变化时打印信息
            if instance.status != last_status {
                println!("🔄 状态变化: {:?} -> {:?}", last_status, instance.status);
                last_status = instance.status.clone();

                // 打印当前步骤信息
                if let Some(current_step_id) = &instance.current_step_id {
                    println!("📍 当前步骤: {}", current_step_id);
                }

                println!("📈 进度: {}/{} 步骤完成",
                    instance.completed_steps.len(),
                    instance.completed_steps.len() + 1 // 简化的进度计算
                );
            }

            // 检查是否完成
            match instance.status {
                WorkflowStatus::Completed => {
                    println!("✅ 工作流执行完成");
                    self.print_execution_result(&instance)?;
                    break;
                }
                WorkflowStatus::Failed => {
                    println!("❌ 工作流执行失败");
                    if let Some(error) = &instance.error {
                        println!("错误信息: {}", error);
                    }
                    return Err(CliError::ExecutionError {
                        error: "工作流执行失败".to_string(),
                    });
                }
                WorkflowStatus::Cancelled => {
                    println!("🛑 工作流已取消");
                    break;
                }
                _ => {
                    // 继续监控
                    tokio::time::sleep(Duration::from_secs(1)).await;
                }
            }
        }
        
        Ok(())
    }
    
    /// 打印执行结果
    fn print_execution_result(&self, instance: &WorkflowInstance) -> Result<()> {
        match self.format.as_str() {
            "json" | "yaml" => {
                print_output(instance, &self.format)?;
            }
            "text" | _ => {
                println!();
                println!("📋 执行结果:");
                println!("  实例ID: {}", instance.id);
                println!("  工作流ID: {}", instance.workflow_id);
                println!("  状态: {:?}", instance.status);

                if !instance.output_parameters.is_empty() {
                    println!("  输出参数:");
                    for (key, value) in &instance.output_parameters {
                        println!("    {}: {}", key, value);
                    }
                }

                let duration = if let Some(completed_at) = instance.completed_at {
                    completed_at.duration_since(instance.created_at).unwrap_or_default()
                } else {
                    std::time::SystemTime::now().duration_since(instance.created_at).unwrap_or_default()
                };
                println!("  执行时间: {:.2}秒", duration.as_secs_f64());

                let stats = &instance.execution_context.execution_stats;
                println!("  统计信息:");
                println!("    总步骤数: {}", stats.total_steps);
                println!("    已完成步骤: {}", stats.completed_steps);
                println!("    失败步骤: {}", stats.failed_steps);
            }
        }

        Ok(())
    }
}

impl ShowWorkflow {
    /// 执行显示工作流
    pub async fn execute(&self) -> Result<()> {
        println!("显示工作流: {}", self.workflow);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_run_workflow_command() {
        let cmd = RunWorkflow {
            config: PathBuf::from("test.toml"),
            workflow: "test-workflow".to_string(),
            input: Some(r#"{"key": "value"}"#.to_string()),
            input_file: None,
            async_run: false,
            monitor: true,
            format: "text".to_string(),
            timeout: Some(300),
        };

        assert_eq!(cmd.workflow, "test-workflow");
        assert!(cmd.monitor);
        assert_eq!(cmd.timeout, Some(300));
    }

    #[tokio::test]
    async fn test_parse_input_parameters() {
        let cmd = RunWorkflow {
            config: PathBuf::from("test.toml"),
            workflow: "test-workflow".to_string(),
            input: Some(r#"{"test": "value"}"#.to_string()),
            input_file: None,
            async_run: false,
            monitor: false,
            format: "text".to_string(),
            timeout: None,
        };

        let params = cmd.parse_input_parameters().await.unwrap();
        assert_eq!(params.get("test").unwrap(), "value");
    }
}