//! # Agent 创建功能
//!
//! 实现创建 Agent 的相关功能。

use crate::error::Result;
use crate::commands::agent::{types::CreateAgent, utils::*};
use orion_core::{agent::AgentConfig, sandbox::SandboxConfig};
use uuid::Uuid;

impl CreateAgent {
    /// 执行创建 Agent
    pub async fn execute(&self) -> Result<()> {
        let _config = load_config(&self.config).await?;
        
        if self.interactive {
            self.create_interactive().await
        } else {
            self.create_from_args().await
        }
    }
    
    /// 从参数创建 Agent
    async fn create_from_args(&self) -> Result<()> {
        let agent_config = if let Some(template) = &self.template {
            self.create_from_template(template)?
        } else {
            self.create_from_params()?
        };
        
        println!("✅ 成功创建 Agent: {}", self.name);
        println!("📝 配置:");
        println!("  名称: {}", agent_config.name);
        println!("  描述: {}", agent_config.description);
        println!("  模型: {}", agent_config.default_model);
        
        if self.auto_start {
            println!("🚀 正在启动 Agent...");
            println!("✅ Agent 已启动");
        }
        
        Ok(())
    }
    
    /// 交互式创建 Agent
    async fn create_interactive(&self) -> Result<()> {
        println!("🤖 创建新的 Agent");
        println!();
        println!("请输入 Agent 信息:");
        println!("名称: {}", self.name);
        
        let _agent_config = self.create_from_params()?;
        println!("✅ 成功创建 Agent: {}", self.name);
        
        Ok(())
    }
    
    /// 从模板创建
    fn create_from_template(&self, template: &str) -> Result<AgentConfig> {
        let base_config = match template {
            "basic" => self.create_basic_template(),
            "assistant" => self.create_assistant_template(),
            "coder" => self.create_coder_template(),
            "analyst" => self.create_analyst_template(),
            _ => return Err(crate::error::CliError::InvalidArgument {
                error: format!("未知的模板类型: {}", template),
            }),
        };
        
        Ok(self.apply_overrides(base_config))
    }
    
    /// 创建基础模板
    fn create_basic_template(&self) -> AgentConfig {
        AgentConfig {
            id: Uuid::new_v4(),
            name: self.name.clone(),
            description: "基础 Agent".to_string(),
            default_model: "gpt-3.5-turbo".to_string(),
            system_prompt: "你是一个有用的助手。".to_string(),
            max_context_length: 4096,
            max_tool_call_depth: 3,
            enable_sandbox: true,
            sandbox_config: Some(SandboxConfig::default()),
            enable_memory: true,
            memory_retention_seconds: Some(86400),
            enable_learning: false,
            temperature: 0.7,
            max_tokens: Some(1000),
            tool_use_strategy: orion_core::agent::ToolUseStrategy::Conservative,
            error_handling_strategy: orion_core::agent::AgentErrorHandlingStrategy::Retry {
                max_attempts: 3,
                retry_interval_seconds: 1,
            },
        }
    }
    
    /// 创建助手模板
    fn create_assistant_template(&self) -> AgentConfig {
        AgentConfig {
            id: Uuid::new_v4(),
            name: self.name.clone(),
            description: "智能助手 Agent".to_string(),
            default_model: "gpt-4".to_string(),
            system_prompt: "你是一个智能助手，能够帮助用户完成各种任务。".to_string(),
            max_context_length: 8192,
            max_tool_call_depth: 5,
            enable_sandbox: true,
            sandbox_config: Some(SandboxConfig::default()),
            enable_memory: true,
            memory_retention_seconds: Some(86400),
            enable_learning: true,
            temperature: 0.8,
            max_tokens: Some(2000),
            tool_use_strategy: orion_core::agent::ToolUseStrategy::Balanced,
            error_handling_strategy: orion_core::agent::AgentErrorHandlingStrategy::RequestHumanIntervention,
        }
    }
    
    /// 创建编码模板
    fn create_coder_template(&self) -> AgentConfig {
        AgentConfig {
            id: Uuid::new_v4(),
            name: self.name.clone(),
            description: "代码生成 Agent".to_string(),
            default_model: "claude-3".to_string(),
            system_prompt: "你是一个专业的程序员，擅长编写高质量的代码。".to_string(),
            max_context_length: 16384,
            max_tool_call_depth: 10,
            enable_sandbox: true,
            sandbox_config: Some(SandboxConfig::default()),
            enable_memory: true,
            memory_retention_seconds: Some(86400),
            enable_learning: true,
            temperature: 0.3,
            max_tokens: Some(4000),
            tool_use_strategy: orion_core::agent::ToolUseStrategy::Aggressive,
            error_handling_strategy: orion_core::agent::AgentErrorHandlingStrategy::Retry {
                max_attempts: 3,
                retry_interval_seconds: 1,
            },
        }
    }
    
    /// 创建分析师模板
    fn create_analyst_template(&self) -> AgentConfig {
        AgentConfig {
            id: Uuid::new_v4(),
            name: self.name.clone(),
            description: "数据分析 Agent".to_string(),
            default_model: "gpt-4".to_string(),
            system_prompt: "你是一个数据分析专家，能够分析和解释各种数据。".to_string(),
            max_context_length: 12288,
            max_tool_call_depth: 7,
            enable_sandbox: true,
            sandbox_config: Some(SandboxConfig::default()),
            enable_memory: true,
            memory_retention_seconds: Some(86400),
            enable_learning: true,
            temperature: 0.5,
            max_tokens: Some(3000),
            tool_use_strategy: orion_core::agent::ToolUseStrategy::Balanced,
            error_handling_strategy: orion_core::agent::AgentErrorHandlingStrategy::RequestHumanIntervention,
        }
    }
    
    /// 从参数创建
    fn create_from_params(&self) -> Result<AgentConfig> {
        let config = AgentConfig {
            id: Uuid::new_v4(),
            name: self.name.clone(),
            description: self.description.clone().unwrap_or_else(|| format!("{} Agent", self.name)),
            default_model: self.model.clone().unwrap_or_else(|| "gpt-3.5-turbo".to_string()),
            system_prompt: self.system_prompt.clone().unwrap_or_else(|| "你是一个有用的助手。".to_string()),
            max_context_length: 4096,
            max_tool_call_depth: 3,
            enable_sandbox: self.sandbox.unwrap_or(true),
            sandbox_config: Some(SandboxConfig::default()),
            enable_memory: self.memory.unwrap_or(true),
            memory_retention_seconds: Some(86400),
            enable_learning: self.learning.unwrap_or(false),
            temperature: self.temperature.unwrap_or(0.7),
            max_tokens: self.max_tokens,
            tool_use_strategy: orion_core::agent::ToolUseStrategy::Conservative,
            error_handling_strategy: orion_core::agent::AgentErrorHandlingStrategy::Retry {
                max_attempts: 3,
                retry_interval_seconds: 1,
            },
        };
        
        Ok(config)
    }
    
    /// 应用覆盖参数
    fn apply_overrides(&self, mut config: AgentConfig) -> AgentConfig {
        if let Some(description) = &self.description {
            config.description = description.clone();
        }
        if let Some(model) = &self.model {
            config.default_model = model.clone();
        }
        if let Some(system_prompt) = &self.system_prompt {
            config.system_prompt = system_prompt.clone();
        }
        if let Some(temperature) = self.temperature {
            config.temperature = temperature;
        }
        if let Some(max_tokens) = self.max_tokens {
            config.max_tokens = Some(max_tokens);
        }
        if let Some(sandbox) = self.sandbox {
            config.enable_sandbox = sandbox;
        }
        if let Some(memory) = self.memory {
            config.enable_memory = memory;
        }
        if let Some(learning) = self.learning {
            config.enable_learning = learning;
        }
        
        config
    }
}

#[cfg(test)]  
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_create_from_template() {
        let cmd = CreateAgent {
            config: PathBuf::from("test.toml"),
            name: "test-agent".to_string(),
            description: None,
            model: None,
            system_prompt: None,
            temperature: None,
            max_tokens: None,
            sandbox: None,
            memory: None,
            learning: None,
            template: Some("basic".to_string()),
            interactive: false,
            auto_start: false,
        };
        
        let config = cmd.create_from_template("basic").unwrap();
        assert_eq!(config.name, "test-agent");
        assert_eq!(config.default_model, "gpt-3.5-turbo");
        assert_eq!(config.temperature, 0.7);
    }
}