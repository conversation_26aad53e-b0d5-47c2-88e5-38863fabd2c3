//! # Orion Core
//!
//! Orion AI Agent 核心库，提供高性能异步消息队列、工作流引擎、
//! LLM 适配器、安全沙箱和上下文管理等核心功能。
//!
//! ## 主要模块
//!
//! - [`error`] - 错误处理和类型定义
//! - [`config`] - 配置管理系统
//! - [`message`] - h2A 启发的异步消息队列
//! - [`llm`] - 可插拔 LLM 引擎系统
//! - [`security`] - 工业级安全沙箱
//! - [`tools`] - 基础工具集
//! - [`context`] - 智能上下文管理
//! - [`workflow`] - 工作流引擎
//! - [`agent`] - 单 Agent 实现

pub mod error;
pub mod config;
pub mod message;
pub mod llm;
// pub mod security;
pub mod sandbox;
pub mod tools;
pub mod context;
pub mod workflow;
pub mod agent;

// 重新导出核心类型
pub use error::{
    OrionError, Result, CoreError, LlmError, WorkflowError, NetworkError, ConfigError,
    ErrorSeverity, ErrorCategory, ErrorCode, ErrorContext, RecoveryStrategy, ErrorStats
};
pub use config::OrionConfig;
pub use message::{Message, MessageBus, MessagePriority};
pub use llm::{LlmEngine, KimiProvider, ModelConfig, ModelParameters};
// pub use security::SecuritySandbox;
pub use tools::Tool;
pub use context::ContextManager;
pub use workflow::{WorkflowDefinition, WorkflowManager};
pub use agent::{Agent, AgentConfig};

/// Orion Core 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 初始化 Orion Core 库
/// 
/// 这个函数设置日志系统和其他全局状态。
/// 应该在使用任何其他 Orion Core 功能之前调用。
pub fn init() -> Result<()> {
    // 初始化 tracing 日志系统
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .try_init()
        .map_err(|e| OrionError::Core(CoreError::initialization_failed(format!("日志系统初始化失败: {}", e))))?;
    
    tracing::info!("Orion Core v{} 初始化完成", VERSION);
    Ok(())
}