//! # 上下文压缩模块
//!
//! 提供智能的上下文压缩功能，包括多种压缩策略和算法。
//! 支持基于时间、重要性、相关性等多维度的压缩决策。

use crate::context::config::CompressionStrategy;
use crate::context::types::{ContextEntry, ImportanceLevel};
use crate::error::Result;
use std::collections::HashMap;
use std::time::{Duration, SystemTime};
use uuid::Uuid;

/// 压缩管理器
/// 
/// 负责执行上下文压缩操作，根据不同的策略识别和删除不重要的条目。
/// 支持多种压缩算法和智能评分机制。
pub struct CompressionManager {
    /// 压缩统计信息
    stats: CompressionStats,
}

impl CompressionManager {
    /// 创建新的压缩管理器
    /// 
    /// # 返回
    /// 
    /// 返回新的压缩管理器实例
    pub fn new() -> Self {
        Self {
            stats: CompressionStats::new(),
        }
    }

    /// 识别需要压缩的条目
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目的映射
    /// * `strategy` - 压缩策略
    /// 
    /// # 返回
    /// 
    /// 返回需要删除的条目ID列表
    pub fn identify_entries_for_compression(
        &mut self,
        entries: &HashMap<Uuid, ContextEntry>,
        strategy: &CompressionStrategy,
    ) -> Result<Vec<Uuid>> {
        let start_time = SystemTime::now();

        let candidates = match strategy {
            CompressionStrategy::TimeBased { retention_duration } => {
                self.identify_time_based_candidates(entries, *retention_duration)
            }
            CompressionStrategy::ImportanceBased { min_importance } => {
                self.identify_importance_based_candidates(entries, min_importance)
            }
            CompressionStrategy::RelevanceBased { min_relevance } => {
                self.identify_relevance_based_candidates(entries, *min_relevance)
            }
            CompressionStrategy::SizeBased { max_entries } => {
                self.identify_size_based_candidates(entries, *max_entries)
            }
            CompressionStrategy::Smart { target_compression_ratio } => {
                self.identify_smart_candidates(entries, *target_compression_ratio)
            }
        };

        // 更新统计信息
        let duration = SystemTime::now().duration_since(start_time).unwrap_or_default();
        self.stats.record_compression_analysis(candidates.len(), duration);

        tracing::info!(
            "压缩分析完成: 策略={:?}, 候选条目数={}, 耗时={:?}",
            strategy,
            candidates.len(),
            duration
        );

        Ok(candidates)
    }

    /// 基于时间的压缩候选识别
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目
    /// * `retention_duration` - 保留时间
    /// 
    /// # 返回
    /// 
    /// 返回超过保留时间的条目ID列表
    fn identify_time_based_candidates(
        &self,
        entries: &HashMap<Uuid, ContextEntry>,
        retention_duration: Duration,
    ) -> Vec<Uuid> {
        let cutoff_time = SystemTime::now() - retention_duration;
        let mut candidates = Vec::new();

        for (id, entry) in entries.iter() {
            if entry.created_at < cutoff_time {
                candidates.push(*id);
            }
        }

        candidates
    }

    /// 基于重要性的压缩候选识别
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目
    /// * `min_importance` - 最小重要性级别
    /// 
    /// # 返回
    /// 
    /// 返回低于最小重要性级别的条目ID列表
    fn identify_importance_based_candidates(
        &self,
        entries: &HashMap<Uuid, ContextEntry>,
        min_importance: &ImportanceLevel,
    ) -> Vec<Uuid> {
        let mut candidates = Vec::new();

        for (id, entry) in entries.iter() {
            if entry.importance < *min_importance {
                candidates.push(*id);
            }
        }

        candidates
    }

    /// 基于相关性的压缩候选识别
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目
    /// * `min_relevance` - 最小相关性评分
    /// 
    /// # 返回
    /// 
    /// 返回低于最小相关性评分的条目ID列表
    fn identify_relevance_based_candidates(
        &self,
        entries: &HashMap<Uuid, ContextEntry>,
        min_relevance: f64,
    ) -> Vec<Uuid> {
        let mut candidates = Vec::new();

        for (id, entry) in entries.iter() {
            if entry.relevance_score < min_relevance {
                candidates.push(*id);
            }
        }

        candidates
    }

    /// 基于大小的压缩候选识别
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目
    /// * `max_entries` - 最大条目数
    /// 
    /// # 返回
    /// 
    /// 返回需要删除的条目ID列表（保留相关性最高的条目）
    fn identify_size_based_candidates(
        &self,
        entries: &HashMap<Uuid, ContextEntry>,
        max_entries: usize,
    ) -> Vec<Uuid> {
        if entries.len() <= max_entries {
            return Vec::new();
        }

        let mut sorted_entries: Vec<_> = entries.iter().collect();
        sorted_entries.sort_by(|a, b| {
            a.1.relevance_score
                .partial_cmp(&b.1.relevance_score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        let remove_count = entries.len() - max_entries;
        sorted_entries
            .iter()
            .take(remove_count)
            .map(|(id, _)| **id)
            .collect()
    }

    /// 智能压缩候选识别
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目
    /// * `target_compression_ratio` - 目标压缩比例
    /// 
    /// # 返回
    /// 
    /// 返回基于综合评分的候选条目ID列表
    fn identify_smart_candidates(
        &self,
        entries: &HashMap<Uuid, ContextEntry>,
        target_compression_ratio: f64,
    ) -> Vec<Uuid> {
        let target_count = (entries.len() as f64 * target_compression_ratio) as usize;
        if target_count == 0 {
            return Vec::new();
        }

        let mut scored_entries: Vec<_> = entries
            .iter()
            .map(|(id, entry)| {
                let score = self.calculate_compression_score(entry);
                (*id, score)
            })
            .collect();

        // 按压缩评分排序（越低越容易被压缩）
        scored_entries.sort_by(|a, b| a.1.partial_cmp(&b.1).unwrap_or(std::cmp::Ordering::Equal));

        scored_entries
            .iter()
            .take(target_count)
            .map(|(id, _)| *id)
            .collect()
    }

    /// 计算压缩评分
    /// 
    /// 评分越低，越容易被压缩删除
    /// 
    /// # 参数
    /// 
    /// * `entry` - 上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回压缩评分（0.0-1.0）
    pub fn calculate_compression_score(&self, entry: &ContextEntry) -> f64 {
        let mut score = 0.0;

        // 相关性评分权重（40%）
        score += entry.relevance_score * 0.4;

        // 重要性权重（30%）
        score += match entry.importance {
            ImportanceLevel::Critical => 1.0,
            ImportanceLevel::High => 0.8,
            ImportanceLevel::Normal => 0.5,
            ImportanceLevel::Low => 0.2,
        } * 0.3;

        // 访问频率权重（20%）
        let access_factor = (entry.access_count as f64).ln().max(0.0) / 10.0;
        score += access_factor.min(1.0) * 0.2;

        // 时间衰减权重（10%）
        let age = SystemTime::now()
            .duration_since(entry.created_at)
            .unwrap_or(Duration::from_secs(0))
            .as_secs() as f64;
        let time_factor = (-age / 86400.0).exp(); // 按天衰减
        score += time_factor * 0.1;

        score.clamp(0.0, 1.0)
    }

    /// 获取压缩统计信息
    /// 
    /// # 返回
    /// 
    /// 返回压缩统计信息
    pub fn get_stats(&self) -> &CompressionStats {
        &self.stats
    }

    /// 重置统计信息
    pub fn reset_stats(&mut self) {
        self.stats = CompressionStats::new();
    }

    /// 预估压缩效果
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目
    /// * `strategy` - 压缩策略
    /// 
    /// # 返回
    /// 
    /// 返回压缩预估结果
    pub fn estimate_compression_effect(
        &self,
        entries: &HashMap<Uuid, ContextEntry>,
        strategy: &CompressionStrategy,
    ) -> CompressionEstimate {
        let total_entries = entries.len();
        let total_size = entries.values().map(|e| e.content.len()).sum::<usize>();

        // 模拟压缩过程（不实际修改数据）
        let candidates = match strategy {
            CompressionStrategy::TimeBased { retention_duration } => {
                self.identify_time_based_candidates(entries, *retention_duration)
            }
            CompressionStrategy::ImportanceBased { min_importance } => {
                self.identify_importance_based_candidates(entries, min_importance)
            }
            CompressionStrategy::RelevanceBased { min_relevance } => {
                self.identify_relevance_based_candidates(entries, *min_relevance)
            }
            CompressionStrategy::SizeBased { max_entries } => {
                self.identify_size_based_candidates(entries, *max_entries)
            }
            CompressionStrategy::Smart { target_compression_ratio } => {
                self.identify_smart_candidates(entries, *target_compression_ratio)
            }
        };

        let removed_entries = candidates.len();
        let removed_size = candidates
            .iter()
            .filter_map(|id| entries.get(id))
            .map(|e| e.content.len())
            .sum::<usize>();

        let remaining_entries = total_entries - removed_entries;
        let remaining_size = total_size - removed_size;

        CompressionEstimate {
            total_entries,
            removed_entries,
            remaining_entries,
            total_size,
            removed_size,
            remaining_size,
            compression_ratio: if total_entries > 0 {
                removed_entries as f64 / total_entries as f64
            } else {
                0.0
            },
            size_reduction_ratio: if total_size > 0 {
                removed_size as f64 / total_size as f64
            } else {
                0.0
            },
        }
    }
}

impl Default for CompressionManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 压缩统计信息
/// 
/// 记录压缩操作的统计数据
#[derive(Debug, Clone)]
pub struct CompressionStats {
    /// 总压缩次数
    pub total_compressions: usize,
    /// 总删除条目数
    pub total_removed_entries: usize,
    /// 总分析时间
    pub total_analysis_time: Duration,
    /// 平均分析时间
    pub average_analysis_time: Duration,
}

impl CompressionStats {
    /// 创建新的压缩统计信息
    pub fn new() -> Self {
        Self {
            total_compressions: 0,
            total_removed_entries: 0,
            total_analysis_time: Duration::from_secs(0),
            average_analysis_time: Duration::from_secs(0),
        }
    }

    /// 记录压缩分析
    /// 
    /// # 参数
    /// 
    /// * `removed_count` - 删除的条目数
    /// * `analysis_time` - 分析耗时
    pub fn record_compression_analysis(&mut self, removed_count: usize, analysis_time: Duration) {
        self.total_compressions += 1;
        self.total_removed_entries += removed_count;
        self.total_analysis_time += analysis_time;
        
        if self.total_compressions > 0 {
            self.average_analysis_time = self.total_analysis_time / self.total_compressions as u32;
        }
    }
}

/// 压缩预估结果
/// 
/// 提供压缩操作的预估效果
#[derive(Debug, Clone)]
pub struct CompressionEstimate {
    /// 总条目数
    pub total_entries: usize,
    /// 将删除的条目数
    pub removed_entries: usize,
    /// 剩余条目数
    pub remaining_entries: usize,
    /// 总大小（字节）
    pub total_size: usize,
    /// 将删除的大小（字节）
    pub removed_size: usize,
    /// 剩余大小（字节）
    pub remaining_size: usize,
    /// 压缩比例（删除条目数/总条目数）
    pub compression_ratio: f64,
    /// 大小减少比例（删除大小/总大小）
    pub size_reduction_ratio: f64,
}

impl CompressionEstimate {
    /// 生成预估报告
    /// 
    /// # 返回
    /// 
    /// 返回格式化的预估报告字符串
    pub fn generate_report(&self) -> String {
        format!(
            "=== 压缩预估报告 ===\n\
             总条目数: {}\n\
             将删除条目数: {}\n\
             剩余条目数: {}\n\
             压缩比例: {:.1}%\n\
             总大小: {} 字节\n\
             将删除大小: {} 字节\n\
             剩余大小: {} 字节\n\
             大小减少比例: {:.1}%",
            self.total_entries,
            self.removed_entries,
            self.remaining_entries,
            self.compression_ratio * 100.0,
            self.total_size,
            self.removed_size,
            self.remaining_size,
            self.size_reduction_ratio * 100.0
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    fn create_test_entry(
        relevance: f64,
        importance: ImportanceLevel,
        age_seconds: u64,
        access_count: u64,
    ) -> ContextEntry {
        let created_at = SystemTime::now() - Duration::from_secs(age_seconds);
        ContextEntry {
            id: Uuid::new_v4(),
            session_id: "test".to_string(),
            entry_type: crate::context::types::ContextEntryType::UserInput,
            content: "test content".to_string(),
            metadata: HashMap::new(),
            relevance_score: relevance,
            importance,
            created_at,
            last_accessed: created_at,
            access_count,
            tags: Vec::new(),
            parent_id: None,
            children_ids: Vec::new(),
        }
    }

    #[test]
    fn test_compression_score_calculation() {
        let manager = CompressionManager::new();

        // 高质量条目
        let high_quality = create_test_entry(0.9, ImportanceLevel::Critical, 3600, 10);
        let score = manager.calculate_compression_score(&high_quality);
        assert!(score > 0.7); // 高质量条目应该有高分

        // 低质量条目
        let low_quality = create_test_entry(0.1, ImportanceLevel::Low, 86400 * 7, 0);
        let score = manager.calculate_compression_score(&low_quality);
        assert!(score < 0.3); // 低质量条目应该有低分
    }

    #[test]
    fn test_time_based_compression() {
        let manager = CompressionManager::new();
        let mut entries = HashMap::new();

        // 新条目
        let new_entry = create_test_entry(0.5, ImportanceLevel::Normal, 3600, 1);
        entries.insert(new_entry.id, new_entry);

        // 旧条目
        let old_entry = create_test_entry(0.5, ImportanceLevel::Normal, 86400 * 7, 1);
        entries.insert(old_entry.id, old_entry.clone());

        let candidates = manager.identify_time_based_candidates(&entries, Duration::from_secs(86400 * 3));
        assert_eq!(candidates.len(), 1);
        assert_eq!(candidates[0], old_entry.id);
    }

    #[test]
    fn test_compression_estimate() {
        let manager = CompressionManager::new();
        let mut entries = HashMap::new();

        for i in 0..10 {
            let entry = create_test_entry(
                i as f64 / 10.0,
                ImportanceLevel::Normal,
                3600,
                i,
            );
            entries.insert(entry.id, entry);
        }

        let strategy = CompressionStrategy::RelevanceBased { min_relevance: 0.5 };
        let estimate = manager.estimate_compression_effect(&entries, &strategy);

        assert_eq!(estimate.total_entries, 10);
        assert!(estimate.removed_entries > 0);
        assert_eq!(estimate.remaining_entries, estimate.total_entries - estimate.removed_entries);

        let report = estimate.generate_report();
        assert!(report.contains("总条目数: 10"));
    }
}
