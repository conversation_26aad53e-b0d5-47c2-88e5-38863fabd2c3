//! # 上下文统计信息模块
//!
//! 提供上下文管理系统的统计信息收集、分析和报告功能。
//! 包括条目统计、性能指标、使用模式分析等。

use crate::context::types::ContextEntry;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;

/// 上下文统计信息
/// 
/// 收集和维护上下文管理系统的各种统计数据，
/// 用于性能监控、容量规划和使用分析。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextStats {
    /// 总条目数量
    pub total_entries: usize,
    /// 按类型分组的条目数量统计
    pub entries_by_type: HashMap<String, usize>,
    /// 按重要性级别分组的条目数量统计
    pub entries_by_importance: HashMap<String, usize>,
    /// 活跃会话数量
    pub active_sessions: usize,
    /// 平均相关性评分
    pub average_relevance: f64,
    /// 总存储大小（字节）
    pub total_size_bytes: u64,
    /// 最后更新时间
    pub last_updated: SystemTime,
}

impl Default for ContextStats {
    fn default() -> Self {
        Self {
            total_entries: 0,
            entries_by_type: HashMap::new(),
            entries_by_importance: HashMap::new(),
            active_sessions: 0,
            average_relevance: 0.0,
            total_size_bytes: 0,
            last_updated: SystemTime::now(),
        }
    }
}

impl ContextStats {
    /// 创建新的统计信息实例
    /// 
    /// # 返回
    /// 
    /// 返回新的统计信息实例
    pub fn new() -> Self {
        Self::default()
    }

    /// 重置所有统计信息
    pub fn reset(&mut self) {
        self.total_entries = 0;
        self.entries_by_type.clear();
        self.entries_by_importance.clear();
        self.active_sessions = 0;
        self.average_relevance = 0.0;
        self.total_size_bytes = 0;
        self.last_updated = SystemTime::now();
    }

    /// 获取最常见的条目类型
    /// 
    /// # 返回
    /// 
    /// 返回最常见的条目类型及其数量
    pub fn most_common_entry_type(&self) -> Option<(String, usize)> {
        self.entries_by_type
            .iter()
            .max_by_key(|(_, &count)| count)
            .map(|(type_name, &count)| (type_name.clone(), count))
    }

    /// 获取最常见的重要性级别
    /// 
    /// # 返回
    /// 
    /// 返回最常见的重要性级别及其数量
    pub fn most_common_importance_level(&self) -> Option<(String, usize)> {
        self.entries_by_importance
            .iter()
            .max_by_key(|(_, &count)| count)
            .map(|(importance, &count)| (importance.clone(), count))
    }

    /// 获取平均条目大小
    /// 
    /// # 返回
    /// 
    /// 返回平均条目大小（字节）
    pub fn average_entry_size(&self) -> f64 {
        if self.total_entries == 0 {
            0.0
        } else {
            self.total_size_bytes as f64 / self.total_entries as f64
        }
    }

    /// 获取存储使用率
    /// 
    /// # 参数
    /// 
    /// * `max_size_bytes` - 最大存储大小
    /// 
    /// # 返回
    /// 
    /// 返回存储使用率（0.0-1.0）
    pub fn storage_usage_ratio(&self, max_size_bytes: u64) -> f64 {
        if max_size_bytes == 0 {
            0.0
        } else {
            (self.total_size_bytes as f64 / max_size_bytes as f64).min(1.0)
        }
    }

    /// 获取条目密度（每个会话的平均条目数）
    /// 
    /// # 返回
    /// 
    /// 返回条目密度
    pub fn entry_density(&self) -> f64 {
        if self.active_sessions == 0 {
            0.0
        } else {
            self.total_entries as f64 / self.active_sessions as f64
        }
    }

    /// 生成统计报告
    /// 
    /// # 返回
    /// 
    /// 返回格式化的统计报告字符串
    pub fn generate_report(&self) -> String {
        let mut report = String::new();
        
        report.push_str("=== 上下文统计报告 ===\n");
        report.push_str(&format!("总条目数: {}\n", self.total_entries));
        report.push_str(&format!("活跃会话数: {}\n", self.active_sessions));
        report.push_str(&format!("平均相关性评分: {:.3}\n", self.average_relevance));
        report.push_str(&format!("总存储大小: {} 字节\n", self.total_size_bytes));
        report.push_str(&format!("平均条目大小: {:.1} 字节\n", self.average_entry_size()));
        report.push_str(&format!("条目密度: {:.1} 条目/会话\n", self.entry_density()));
        
        if let Some((type_name, count)) = self.most_common_entry_type() {
            report.push_str(&format!("最常见条目类型: {} ({} 个)\n", type_name, count));
        }
        
        if let Some((importance, count)) = self.most_common_importance_level() {
            report.push_str(&format!("最常见重要性级别: {} ({} 个)\n", importance, count));
        }
        
        report.push_str(&format!("最后更新: {:?}\n", self.last_updated));
        
        report
    }
}

/// 统计信息管理器
/// 
/// 负责收集、更新和维护上下文统计信息的管理器。
/// 提供实时统计更新和批量统计计算功能。
pub struct StatsManager {
    /// 当前统计信息
    stats: ContextStats,
}

impl StatsManager {
    /// 创建新的统计信息管理器
    /// 
    /// # 返回
    /// 
    /// 返回新的统计信息管理器实例
    pub fn new() -> Self {
        Self {
            stats: ContextStats::new(),
        }
    }

    /// 获取当前统计信息
    /// 
    /// # 返回
    /// 
    /// 返回当前统计信息的克隆
    pub fn get_stats(&self) -> ContextStats {
        self.stats.clone()
    }

    /// 重置统计信息
    pub fn reset(&mut self) {
        self.stats.reset();
    }

    /// 增加条目统计
    /// 
    /// # 参数
    /// 
    /// * `entry` - 要统计的条目
    pub fn add_entry_stats(&mut self, entry: &ContextEntry) {
        self.stats.total_entries += 1;
        
        // 更新类型统计
        let type_key = format!("{:?}", entry.entry_type);
        *self.stats.entries_by_type.entry(type_key).or_insert(0) += 1;
        
        // 更新重要性统计
        let importance_key = format!("{:?}", entry.importance);
        *self.stats.entries_by_importance.entry(importance_key).or_insert(0) += 1;
        
        // 更新大小统计
        self.stats.total_size_bytes += entry.content.len() as u64;
        
        // 重新计算平均相关性评分
        self.recalculate_average_relevance();
        
        self.stats.last_updated = SystemTime::now();
    }

    /// 移除条目统计
    /// 
    /// # 参数
    /// 
    /// * `entry` - 要移除统计的条目
    pub fn remove_entry_stats(&mut self, entry: &ContextEntry) {
        if self.stats.total_entries > 0 {
            self.stats.total_entries -= 1;
        }
        
        // 更新类型统计
        let type_key = format!("{:?}", entry.entry_type);
        if let Some(count) = self.stats.entries_by_type.get_mut(&type_key) {
            if *count > 0 {
                *count -= 1;
            }
            if *count == 0 {
                self.stats.entries_by_type.remove(&type_key);
            }
        }
        
        // 更新重要性统计
        let importance_key = format!("{:?}", entry.importance);
        if let Some(count) = self.stats.entries_by_importance.get_mut(&importance_key) {
            if *count > 0 {
                *count -= 1;
            }
            if *count == 0 {
                self.stats.entries_by_importance.remove(&importance_key);
            }
        }
        
        // 更新大小统计
        if self.stats.total_size_bytes >= entry.content.len() as u64 {
            self.stats.total_size_bytes -= entry.content.len() as u64;
        }
        
        // 重新计算平均相关性评分
        self.recalculate_average_relevance();
        
        self.stats.last_updated = SystemTime::now();
    }

    /// 更新会话统计
    /// 
    /// # 参数
    /// 
    /// * `session_count` - 活跃会话数量
    pub fn update_session_stats(&mut self, session_count: usize) {
        self.stats.active_sessions = session_count;
        self.stats.last_updated = SystemTime::now();
    }

    /// 批量重新计算统计信息
    /// 
    /// # 参数
    /// 
    /// * `entries` - 所有条目的引用
    /// * `session_count` - 活跃会话数量
    pub fn recalculate_all_stats<'a, I>(&mut self, entries: I, session_count: usize)
    where
        I: Iterator<Item = &'a ContextEntry>,
    {
        self.stats.reset();
        
        let mut total_relevance = 0.0;
        
        for entry in entries {
            self.stats.total_entries += 1;
            
            // 更新类型统计
            let type_key = format!("{:?}", entry.entry_type);
            *self.stats.entries_by_type.entry(type_key).or_insert(0) += 1;
            
            // 更新重要性统计
            let importance_key = format!("{:?}", entry.importance);
            *self.stats.entries_by_importance.entry(importance_key).or_insert(0) += 1;
            
            // 累计相关性评分
            total_relevance += entry.relevance_score;
            
            // 累计大小
            self.stats.total_size_bytes += entry.content.len() as u64;
        }
        
        // 计算平均相关性评分
        if self.stats.total_entries > 0 {
            self.stats.average_relevance = total_relevance / self.stats.total_entries as f64;
        }
        
        self.stats.active_sessions = session_count;
        self.stats.last_updated = SystemTime::now();
    }

    /// 重新计算平均相关性评分
    /// 
    /// 注意：这个方法需要访问所有条目，在实际实现中应该由调用者提供相关性评分总和
    fn recalculate_average_relevance(&mut self) {
        // 这里是一个简化的实现，实际使用中应该由外部提供相关性评分数据
        // 或者在每次更新时增量计算
        tracing::debug!("需要重新计算平均相关性评分");
    }

    /// 更新平均相关性评分
    /// 
    /// # 参数
    /// 
    /// * `total_relevance` - 所有条目的相关性评分总和
    pub fn update_average_relevance(&mut self, total_relevance: f64) {
        if self.stats.total_entries > 0 {
            self.stats.average_relevance = total_relevance / self.stats.total_entries as f64;
        } else {
            self.stats.average_relevance = 0.0;
        }
        self.stats.last_updated = SystemTime::now();
    }
}

impl Default for StatsManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::context::types::{ContextEntryType, ImportanceLevel};
    use std::collections::HashMap;
    use uuid::Uuid;

    fn create_test_entry(entry_type: ContextEntryType, importance: ImportanceLevel, content: &str) -> ContextEntry {
        ContextEntry {
            id: Uuid::new_v4(),
            session_id: "test_session".to_string(),
            entry_type,
            content: content.to_string(),
            metadata: HashMap::new(),
            relevance_score: 0.8,
            importance,
            created_at: SystemTime::now(),
            last_accessed: SystemTime::now(),
            access_count: 0,
            tags: Vec::new(),
            parent_id: None,
            children_ids: Vec::new(),
        }
    }

    #[test]
    fn test_stats_manager() {
        let mut manager = StatsManager::new();
        
        let entry1 = create_test_entry(ContextEntryType::UserInput, ImportanceLevel::High, "测试内容1");
        let entry2 = create_test_entry(ContextEntryType::AgentResponse, ImportanceLevel::Normal, "测试内容2");
        
        manager.add_entry_stats(&entry1);
        manager.add_entry_stats(&entry2);
        
        let stats = manager.get_stats();
        assert_eq!(stats.total_entries, 2);
        assert_eq!(stats.entries_by_type.len(), 2);
        assert_eq!(stats.entries_by_importance.len(), 2);
        
        manager.remove_entry_stats(&entry1);
        let stats = manager.get_stats();
        assert_eq!(stats.total_entries, 1);
    }

    #[test]
    fn test_context_stats() {
        let mut stats = ContextStats::new();
        stats.total_entries = 100;
        stats.active_sessions = 10;
        stats.total_size_bytes = 50000;
        
        assert_eq!(stats.entry_density(), 10.0);
        assert_eq!(stats.average_entry_size(), 500.0);
        assert_eq!(stats.storage_usage_ratio(100000), 0.5);
        
        let report = stats.generate_report();
        assert!(report.contains("总条目数: 100"));
        assert!(report.contains("活跃会话数: 10"));
    }
}
