//! # ASCII 艺术字
//!
//! 包含 Orion 的 Logo 和艺术字。

/// Orion 短版 Logo
pub const ORION_SHORT_LOGO: &str = r#"
   ____       _             
  / __ \     (_)            
 | |  | |_ __ _  ___  _ __  
 | |  | | '__| |/ _ \| '_ \ 
 | |__| | |  | | (_) | | | |
  \____/|_|  |_|\___/|_| |_|
"#;

/// Orion 长版 Logo  
pub const ORION_LONG_LOGO: &str = r#"
   ____       _                      _    _____                          
  / __ \     (_)                    | |  / ____|                         
 | |  | |_ __ _  ___  _ __           | | | (___  _   _ ___  _____ ____   
 | |  | | '__| |/ _ \| '_ \          | |  \___ \| | | / __|/ ____|  _ \  
 | |__| | |  | | (_) | | | |         | |  ____) | |_| \__ \ (__  | | | | 
  \____/|_|  |_|\___/|_| |_|         |_| |_____/ \__, |___/\___|_| |_| | 
                                                  __/ |                  
                                                 |___/                   
"#;

/// 星空装饰
pub const STAR_DECORATION: &str = r#"
    ✦       ✧       ✦       ✧       ✦
         ✧       ✦       ✧       ✦    
    ✦       ✧       ✦       ✧       ✦
"#;

/// 获取 ASCII 艺术的宽度
pub fn get_ascii_width(art: &str) -> usize {
    art.lines()
        .map(|line| line.chars().count())
        .max()
        .unwrap_or(0)
}

/// 获取 ASCII 艺术的高度
pub fn get_ascii_height(art: &str) -> usize {
    art.lines().count()
}

/// 根据终端宽度选择合适的 Logo
pub fn select_logo_for_width(terminal_width: u16) -> &'static str {
    let long_logo_width = get_ascii_width(ORION_LONG_LOGO);
    
    if terminal_width as usize >= long_logo_width + 4 {
        ORION_LONG_LOGO
    } else {
        ORION_SHORT_LOGO
    }
}

/// 居中显示 ASCII 艺术
pub fn center_ascii_art(art: &str, terminal_width: u16) -> String {
    let art_width = get_ascii_width(art);
    let padding = if terminal_width as usize > art_width {
        (terminal_width as usize - art_width) / 2
    } else {
        0
    };
    
    art.lines()
        .map(|line| format!("{}{}", " ".repeat(padding), line))
        .collect::<Vec<_>>()
        .join("\n")
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_ascii_width() {
        let width = get_ascii_width(ORION_SHORT_LOGO);
        assert!(width > 0);
    }

    #[test]
    fn test_get_ascii_height() {
        let height = get_ascii_height(ORION_SHORT_LOGO);
        assert!(height > 0);
    }

    #[test]
    fn test_select_logo_for_width() {
        let short_logo = select_logo_for_width(40);
        assert_eq!(short_logo, ORION_SHORT_LOGO);
        
        let long_logo = select_logo_for_width(120);
        assert_eq!(long_logo, ORION_LONG_LOGO);
    }

    #[test]
    fn test_center_ascii_art() {
        let centered = center_ascii_art("test", 10);
        assert!(centered.starts_with("   ")); // 应该有一些缩进
    }
}