//! # 网络错误模块
//!
//! 包含所有与网络通信相关的错误类型。
//! 涵盖HTTP请求、API调用、网络连接、超时等网络操作中的错误。

use thiserror::Error;

/// 网络相关错误枚举
/// 
/// 定义了与网络通信相关的各种错误情况。
/// 包括连接失败、请求超时、HTTP状态错误等问题。
#[derive(Error, Debug, Clone)]
pub enum NetworkError {
    /// 通用网络请求错误
    /// 
    /// 用于不属于其他特定类别的网络相关错误。
    #[error("网络请求失败: {message}")]
    RequestFailed {
        /// 错误详细信息
        message: String,
    },

    /// 连接超时错误
    /// 
    /// 当网络连接建立超时时抛出。
    #[error("连接超时: {url}, 超时时间: {timeout_seconds}秒")]
    ConnectionTimeout {
        /// 目标URL
        url: String,
        /// 超时时间（秒）
        timeout_seconds: u64,
    },

    /// 请求超时错误
    /// 
    /// 当HTTP请求执行超时时抛出。
    #[error("请求超时: {url}, 超时时间: {timeout_seconds}秒")]
    RequestTimeout {
        /// 目标URL
        url: String,
        /// 超时时间（秒）
        timeout_seconds: u64,
    },

    /// HTTP状态错误
    /// 
    /// 当收到非成功的HTTP状态码时抛出。
    #[error("HTTP状态错误: {status_code} - {message}")]
    HttpStatusError {
        /// HTTP状态码
        status_code: u16,
        /// 错误信息
        message: String,
    },

    /// DNS解析失败错误
    /// 
    /// 当域名解析失败时抛出。
    #[error("DNS解析失败: {domain}")]
    DnsResolutionFailed {
        /// 域名
        domain: String,
    },

    /// 网络不可达错误
    /// 
    /// 当目标网络不可达时抛出。
    #[error("网络不可达: {url}")]
    NetworkUnreachable {
        /// 目标URL
        url: String,
    },

    /// 代理错误
    /// 
    /// 当代理服务器出现问题时抛出。
    #[error("代理错误: {message}")]
    ProxyError {
        /// 错误信息
        message: String,
    },
}

impl NetworkError {
    /// 创建通用请求失败错误
    pub fn request_failed(message: impl Into<String>) -> Self {
        Self::RequestFailed {
            message: message.into(),
        }
    }

    /// 创建连接超时错误
    pub fn connection_timeout(url: impl Into<String>, timeout_seconds: u64) -> Self {
        Self::ConnectionTimeout {
            url: url.into(),
            timeout_seconds,
        }
    }

    /// 创建请求超时错误
    pub fn request_timeout(url: impl Into<String>, timeout_seconds: u64) -> Self {
        Self::RequestTimeout {
            url: url.into(),
            timeout_seconds,
        }
    }

    /// 创建HTTP状态错误
    pub fn http_status_error(status_code: u16, message: impl Into<String>) -> Self {
        Self::HttpStatusError {
            status_code,
            message: message.into(),
        }
    }

    /// 创建DNS解析失败错误
    pub fn dns_resolution_failed(domain: impl Into<String>) -> Self {
        Self::DnsResolutionFailed {
            domain: domain.into(),
        }
    }

    /// 创建网络不可达错误
    pub fn network_unreachable(url: impl Into<String>) -> Self {
        Self::NetworkUnreachable {
            url: url.into(),
        }
    }

    /// 创建代理错误
    pub fn proxy_error(message: impl Into<String>) -> Self {
        Self::ProxyError {
            message: message.into(),
        }
    }

    /// 检查错误是否可以重试
    pub fn is_retryable(&self) -> bool {
        match self {
            NetworkError::ConnectionTimeout { .. }
            | NetworkError::RequestTimeout { .. }
            | NetworkError::DnsResolutionFailed { .. }
            | NetworkError::NetworkUnreachable { .. }
            | NetworkError::ProxyError { .. } => true,
            NetworkError::HttpStatusError { status_code, .. } => {
                *status_code >= 500 && *status_code < 600
            }
            _ => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_network_error_creation() {
        let error = NetworkError::request_failed("测试网络错误");
        assert!(error.to_string().contains("网络请求失败"));
        assert!(error.to_string().contains("测试网络错误"));
    }

    #[test]
    fn test_connection_timeout() {
        let error = NetworkError::connection_timeout("https://example.com", 30);
        assert!(error.to_string().contains("连接超时"));
        assert!(error.to_string().contains("https://example.com"));
        assert!(error.to_string().contains("30秒"));
    }

    #[test]
    fn test_http_status_error() {
        let error = NetworkError::http_status_error(404, "页面未找到");
        assert!(error.to_string().contains("HTTP状态错误"));
        assert!(error.to_string().contains("404"));
        assert!(error.to_string().contains("页面未找到"));
    }

    #[test]
    fn test_is_retryable() {
        // 可重试的错误
        assert!(NetworkError::connection_timeout("url", 30).is_retryable());
        assert!(NetworkError::http_status_error(500, "服务器错误").is_retryable());
        
        // 不可重试的错误
        assert!(!NetworkError::http_status_error(404, "未找到").is_retryable());
        assert!(!NetworkError::request_failed("请求失败").is_retryable());
    }
}
