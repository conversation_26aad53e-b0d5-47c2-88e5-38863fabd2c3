//! # 工具命令类型定义
//!
//! 定义工具模块中使用的所有命令结构和类型。

use clap::{Args, Subcommand};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;

/// 工具命令
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ToolsCommand {
    #[command(subcommand)]
    pub action: ToolsAction,
}

/// 工具操作
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum ToolsAction {
    /// 列出工具
    List(ListTools),
    /// 搜索工具
    Search(SearchTools),
    /// 显示工具详情
    Show(ShowTool),
    /// 安装工具
    Install(InstallTool),
    /// 卸载工具
    Uninstall(UninstallTool),
    /// 更新工具
    Update(UpdateTool),
    /// 执行工具
    Run(RunTool),
    /// 测试工具
    Test(TestTool),
    /// 创建工具
    Create(CreateTool),
    /// 验证工具
    Validate(ValidateTool),
    /// 工具配置
    Config(ConfigTool),
}

/// 列出工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ListTools {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 输出格式
    #[arg(short, long, default_value = "table", value_parser = ["table", "json", "yaml"])]
    pub format: String,
    
    /// 过滤分类
    #[arg(short, long)]
    pub category: Option<String>,
    
    /// 显示详细信息
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 只显示已安装的工具
    #[arg(long)]
    pub installed: bool,
    
    /// 只显示可用的工具
    #[arg(long)]
    pub available: bool,
}

/// 搜索工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct SearchTools {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 搜索关键词
    pub query: String,
    
    /// 输出格式
    #[arg(short, long, default_value = "table", value_parser = ["table", "json", "yaml"])]
    pub format: String,
    
    /// 限制结果数量
    #[arg(short, long, default_value_t = 20)]
    pub limit: usize,
    
    /// 搜索范围
    #[arg(long, value_parser = ["name", "description", "tags", "all"], default_value = "all")]
    pub scope: String,
}

/// 显示工具详情
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ShowTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称
    pub tool_name: String,
    
    /// 输出格式
    #[arg(short, long, default_value = "yaml", value_parser = ["yaml", "json", "text"])]
    pub format: String,
    
    /// 显示示例
    #[arg(long, default_value_t = true)]
    pub examples: bool,
    
    /// 显示参数详情
    #[arg(long, default_value_t = true)]
    pub parameters: bool,
}

/// 安装工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct InstallTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称或路径
    pub tool: String,
    
    /// 工具版本
    #[arg(short, long)]
    pub version: Option<String>,
    
    /// 强制安装（覆盖已存在的工具）
    #[arg(short, long)]
    pub force: bool,
    
    /// 从本地文件安装
    #[arg(long)]
    pub local: bool,
    
    /// 从 URL 安装
    #[arg(long)]
    pub url: Option<String>,
    
    /// 验证工具
    #[arg(long, default_value_t = true)]
    pub validate: bool,
}

/// 卸载工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct UninstallTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称
    pub tool_name: String,
    
    /// 确认卸载
    #[arg(short, long)]
    pub yes: bool,
    
    /// 保留配置
    #[arg(long)]
    pub keep_config: bool,
}

/// 更新工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct UpdateTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称（留空更新所有工具）
    pub tool_name: Option<String>,
    
    /// 目标版本
    #[arg(short, long)]
    pub version: Option<String>,
    
    /// 强制更新
    #[arg(short, long)]
    pub force: bool,
    
    /// 检查更新但不执行
    #[arg(long)]
    pub check_only: bool,
}

/// 执行工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct RunTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称
    pub tool_name: String,
    
    /// 工具参数（JSON 格式）
    #[arg(short, long)]
    pub params: Option<String>,
    
    /// 参数文件
    #[arg(long)]
    pub params_file: Option<PathBuf>,
    
    /// 输出格式
    #[arg(long, default_value = "text", value_parser = ["text", "json", "yaml"])]
    pub format: String,
    
    /// 超时时间（秒）
    #[arg(long)]
    pub timeout: Option<u64>,
    
    /// 使用沙箱
    #[arg(long, default_value_t = true)]
    pub sandbox: bool,
    
    /// 详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 测试工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct TestTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称
    pub tool_name: String,
    
    /// 测试用例名称
    #[arg(short, long)]
    pub test_case: Option<String>,
    
    /// 运行所有测试
    #[arg(long)]
    pub all: bool,
    
    /// 详细输出
    #[arg(short, long)]
    pub verbose: bool,
}

/// 创建工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct CreateTool {
    /// 工具名称
    pub name: String,
    
    /// 工具描述
    #[arg(short, long)]
    pub description: Option<String>,
    
    /// 输出目录
    #[arg(short, long, default_value = ".")]
    pub output: PathBuf,
    
    /// 工具模板
    #[arg(short, long, value_parser = ["basic", "http", "file", "llm", "custom"])]
    pub template: Option<String>,
    
    /// 交互模式
    #[arg(short, long)]
    pub interactive: bool,
    
    /// 编程语言
    #[arg(short, long, value_parser = ["rust", "python", "javascript", "go"], default_value = "rust")]
    pub language: String,
}

/// 验证工具
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ValidateTool {
    /// 工具文件路径
    pub tool_file: PathBuf,
    
    /// 详细输出
    #[arg(short, long)]
    pub verbose: bool,
    
    /// 严格模式
    #[arg(long)]
    pub strict: bool,
}

/// 工具配置
#[derive(Debug, Clone, Args, Serialize, Deserialize)]
pub struct ConfigTool {
    /// 配置文件路径
    #[arg(short, long, default_value = "orion.toml")]
    pub config: PathBuf,
    
    /// 工具名称
    pub tool_name: String,
    
    /// 配置键
    pub key: Option<String>,
    
    /// 配置值
    pub value: Option<String>,
    
    /// 列出所有配置
    #[arg(short, long)]
    pub list: bool,
    
    /// 重置配置
    #[arg(long)]
    pub reset: bool,
}