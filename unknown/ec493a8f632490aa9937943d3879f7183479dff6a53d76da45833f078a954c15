//! # 配置管理模块
//!
//! 提供Orion系统的配置管理功能，包括配置文件加载、
//! 环境变量处理、配置验证和动态配置更新等。

use crate::error::{OrionError, Result, ConfigError};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};

/// Orion 主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OrionConfig {
    /// 通用配置
    pub general: GeneralConfig,
    /// API 配置
    pub api: ApiConfig,
    /// Agent 配置
    pub agents: AgentConfig,
    /// 工具配置
    pub tools: ToolsConfig,
    /// 工作流配置
    pub workflows: WorkflowsConfig,
    /// 日志配置
    pub logging: LoggingConfig,
    /// 安全配置
    pub security: SecurityConfig,
}

/// 通用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GeneralConfig {
    /// 日志级别
    pub log_level: String,
    /// 工作目录
    pub work_dir: PathBuf,
    /// 输出格式
    pub output_format: String,
    /// 是否启用颜色输出
    pub enable_color: bool,
    /// 是否启用性能分析
    pub enable_profiling: bool,
}

/// API 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiConfig {
    /// API 基础 URL
    pub base_url: String,
    /// API 密钥
    pub api_key: Option<String>,
    /// 请求超时时间（秒）
    pub timeout: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试间隔（秒）
    pub retry_interval: u64,
}

/// Agent 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfig {
    /// 默认模型
    pub default_model: String,
    /// 最大并发 Agent 数量
    pub max_concurrent: usize,
    /// 是否自动启动
    pub auto_start: bool,
    /// 默认超时时间（秒）
    pub default_timeout: u64,
    /// 最大上下文长度
    pub max_context_length: usize,
}

/// 工具配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolsConfig {
    /// 工具注册表 URL
    pub registry_url: String,
    /// 是否自动更新
    pub auto_update: bool,
    /// 缓存目录
    pub cache_dir: PathBuf,
    /// 工具执行超时时间（秒）
    pub execution_timeout: u64,
    /// 最大并发工具执行数
    pub max_concurrent_executions: usize,
}

/// 工作流配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowsConfig {
    /// 模板目录
    pub template_dir: PathBuf,
    /// 最大执行时间（秒）
    pub max_execution_time: u64,
    /// 是否自动保存
    pub auto_save: bool,
    /// 最大并发工作流数
    pub max_concurrent_workflows: usize,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// 日志级别
    pub level: String,
    /// 日志格式
    pub format: String,
    /// 日志文件路径
    pub file_path: Option<PathBuf>,
    /// 是否启用控制台输出
    pub enable_console: bool,
    /// 是否启用文件输出
    pub enable_file: bool,
    /// 日志轮转大小（MB）
    pub rotation_size: u64,
    /// 保留日志文件数量
    pub retention_count: u32,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// 是否启用沙箱
    pub enable_sandbox: bool,
    /// 沙箱配置
    pub sandbox: SandboxConfig,
    /// 允许的域名列表
    pub allowed_domains: Vec<String>,
    /// 禁止的命令列表
    pub blocked_commands: Vec<String>,
}

/// 沙箱配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SandboxConfig {
    /// 内存限制（MB）
    pub memory_limit: u64,
    /// CPU 限制（百分比）
    pub cpu_limit: u32,
    /// 执行超时时间（秒）
    pub timeout: u64,
    /// 临时目录
    pub temp_dir: PathBuf,
}

impl Default for OrionConfig {
    fn default() -> Self {
        Self {
            general: GeneralConfig::default(),
            api: ApiConfig::default(),
            agents: AgentConfig::default(),
            tools: ToolsConfig::default(),
            workflows: WorkflowsConfig::default(),
            logging: LoggingConfig::default(),
            security: SecurityConfig::default(),
        }
    }
}

impl Default for GeneralConfig {
    fn default() -> Self {
        Self {
            log_level: "info".to_string(),
            work_dir: dirs::home_dir()
                .unwrap_or_else(|| PathBuf::from("."))
                .join("orion-workspace"),
            output_format: "table".to_string(),
            enable_color: true,
            enable_profiling: false,
        }
    }
}

impl Default for ApiConfig {
    fn default() -> Self {
        Self {
            base_url: "https://api.orion-ai.com".to_string(),
            api_key: None,
            timeout: 30,
            max_retries: 3,
            retry_interval: 1,
        }
    }
}

impl Default for AgentConfig {
    fn default() -> Self {
        Self {
            default_model: "gpt-4".to_string(),
            max_concurrent: 5,
            auto_start: false,
            default_timeout: 300,
            max_context_length: 8192,
        }
    }
}

impl Default for ToolsConfig {
    fn default() -> Self {
        Self {
            registry_url: "https://tools.orion-ai.com".to_string(),
            auto_update: true,
            cache_dir: dirs::cache_dir()
                .unwrap_or_else(|| PathBuf::from(".cache"))
                .join("orion")
                .join("tools"),
            execution_timeout: 60,
            max_concurrent_executions: 10,
        }
    }
}

impl Default for WorkflowsConfig {
    fn default() -> Self {
        Self {
            template_dir: dirs::config_dir()
                .unwrap_or_else(|| PathBuf::from(".config"))
                .join("orion")
                .join("templates"),
            max_execution_time: 3600,
            auto_save: true,
            max_concurrent_workflows: 3,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: "pretty".to_string(),
            file_path: None,
            enable_console: true,
            enable_file: false,
            rotation_size: 100,
            retention_count: 10,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enable_sandbox: true,
            sandbox: SandboxConfig::default(),
            allowed_domains: vec![
                "api.openai.com".to_string(),
                "api.anthropic.com".to_string(),
            ],
            blocked_commands: vec![
                "rm".to_string(),
                "del".to_string(),
                "format".to_string(),
            ],
        }
    }
}

impl Default for SandboxConfig {
    fn default() -> Self {
        Self {
            memory_limit: 512,
            cpu_limit: 50,
            timeout: 300,
            temp_dir: std::env::temp_dir().join("orion-sandbox"),
        }
    }
}

impl OrionConfig {
    /// 从文件加载配置
    pub fn from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path_buf = path.as_ref().to_path_buf();
        let content = std::fs::read_to_string(&path_buf)
            .map_err(|e| OrionError::Config(ConfigError::file_read_error(&path_buf, format!("读取配置文件失败: {}", e))))?;
        
        toml::from_str(&content)
            .map_err(|e| OrionError::Config(ConfigError::parse_error(&path_buf, format!("解析配置文件失败: {}", e))))
    }
    
    /// 保存配置到文件
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)
            .map_err(|e| OrionError::SerializationError(format!("序列化配置失败: {}", e)))?;
        
        // 确保目录存在
        if let Some(parent) = path.as_ref().parent() {
            std::fs::create_dir_all(parent)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(parent, format!("创建配置目录失败: {}", e))))?;
        }
        
        let path_buf = path.as_ref().to_path_buf();
        std::fs::write(&path_buf, content)
            .map_err(|e| OrionError::Config(ConfigError::file_read_error(&path_buf, format!("写入配置文件失败: {}", e))))?;
        
        Ok(())
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证工作目录
        if !self.general.work_dir.exists() {
            std::fs::create_dir_all(&self.general.work_dir)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(&self.general.work_dir, format!("创建工作目录失败: {}", e))))?;
        }
        
        // 验证缓存目录
        if !self.tools.cache_dir.exists() {
            std::fs::create_dir_all(&self.tools.cache_dir)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(&self.tools.cache_dir, format!("创建缓存目录失败: {}", e))))?;
        }
        
        // 验证模板目录
        if !self.workflows.template_dir.exists() {
            std::fs::create_dir_all(&self.workflows.template_dir)
                .map_err(|e| OrionError::Config(ConfigError::file_read_error(&self.workflows.template_dir, format!("创建模板目录失败: {}", e))))?;
        }
        
        Ok(())
    }
}
