//! # Agent 命令模块
//!
//! 实现 Orion Agent 的管理和交互功能，重构为模块化结构。

// 声明子模块
pub mod types;
pub mod utils;
pub mod list;
pub mod create;
pub mod lifecycle;
pub mod status;
pub mod chat;
pub mod config;
pub mod management;
pub mod io;

// 重新导出主要类型
pub use types::*;

use crate::error::Result;

impl AgentCommand {
    /// 执行 Agent 命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            AgentAction::List(cmd) => cmd.execute().await,
            AgentAction::Create(cmd) => cmd.execute().await,
            AgentAction::Start(cmd) => cmd.execute().await,
            AgentAction::Stop(cmd) => cmd.execute().await,
            AgentAction::Restart(cmd) => cmd.execute().await,
            AgentAction::Status(cmd) => cmd.execute().await,
            AgentAction::Stats(cmd) => cmd.execute().await,
            AgentAction::Chat(cmd) => cmd.execute().await,
            AgentAction::Config(cmd) => cmd.execute().await,
            AgentAction::Delete(cmd) => cmd.execute().await,
            AgentAction::Clone(cmd) => cmd.execute().await,
            AgentAction::Export(cmd) => cmd.execute().await,
            AgentAction::Import(cmd) => cmd.execute().await,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_agent_command_creation() {
        let list_cmd = ListAgents {
            config: PathBuf::from("test.toml"),
            format: "table".to_string(),
            verbose: false,
            status: None,
            stats: false,
        };

        let agent_cmd = AgentCommand {
            action: AgentAction::List(list_cmd),
        };

        // 测试基本结构
        match &agent_cmd.action {
            AgentAction::List(cmd) => {
                assert_eq!(cmd.format, "table");
                assert!(!cmd.verbose);
            }
            _ => panic!("Expected List action"),
        }
    }

    #[tokio::test]
    async fn test_create_agent_command() {
        let create_cmd = CreateAgent {
            config: PathBuf::from("test.toml"),
            name: "test-agent".to_string(),
            description: Some("测试 Agent".to_string()),
            model: Some("gpt-4".to_string()),
            system_prompt: None,
            temperature: Some(0.8),
            max_tokens: Some(2000),
            sandbox: Some(true),
            memory: Some(true),
            learning: Some(false),
            template: None,
            interactive: false,
            auto_start: false,
        };

        assert_eq!(create_cmd.name, "test-agent");
        assert_eq!(create_cmd.model, Some("gpt-4".to_string()));
        assert_eq!(create_cmd.temperature, Some(0.8));
    }

    #[tokio::test]
    async fn test_chat_command() {
        let chat_cmd = ChatWithAgent {
            config: PathBuf::from("test.toml"),
            agent_name: "test-agent".to_string(),
            message: Some("Hello".to_string()),
            file: None,
            interactive: false,
            session_id: None,
            task_type: Some("qa".to_string()),
            priority: Some("normal".to_string()),
            timeout: Some(30),
            format: "text".to_string(),
        };

        assert_eq!(chat_cmd.agent_name, "test-agent");
        assert_eq!(chat_cmd.message, Some("Hello".to_string()));
        assert_eq!(chat_cmd.task_type, Some("qa".to_string()));
    }

    #[test]
    fn test_agent_action_variants() {
        // 测试所有 AgentAction 变体都存在
        let actions = vec![
            "List", "Create", "Start", "Stop", "Restart", 
            "Status", "Stats", "Chat", "Config", "Delete", 
            "Clone", "Export", "Import"
        ];

        // 这个测试确保我们没有遗漏任何命令类型
        assert_eq!(actions.len(), 13);
    }
}