//! # 上下文管理器核心实现
//!
//! 整合所有子模块，提供统一的上下文管理接口。
//! 保持与原始API的完全兼容性，同时利用模块化的优势。

use crate::context::{
    compression::CompressionManager,
    config::ContextManagerConfig,
    index::IndexManager,
    query::ContextQuery,
    relevance::RelevanceCalculator,
    stats::StatsManager,
    types::ContextEntry,
};
use crate::error::{OrionError, Result};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::RwLock;
use uuid::Uuid;

/// 上下文管理器
/// 
/// 整合所有上下文管理功能的核心管理器。
/// 提供完整的上下文生命周期管理，包括添加、查询、压缩、统计等功能。
pub struct ContextManager {
    /// 配置信息
    config: ContextManagerConfig,
    /// 上下文条目存储
    entries: Arc<RwLock<HashMap<Uuid, ContextEntry>>>,
    /// 索引管理器
    index_manager: Arc<IndexManager>,
    /// 统计信息管理器
    stats_manager: Arc<RwLock<StatsManager>>,
    /// 压缩管理器
    compression_manager: Arc<RwLock<CompressionManager>>,
    /// 相关性计算器
    relevance_calculator: Arc<RwLock<RelevanceCalculator>>,
    /// 最近访问的条目（LRU 缓存）
    recent_entries: Arc<RwLock<VecDeque<Uuid>>>,
}

impl ContextManager {
    /// 创建新的上下文管理器
    /// 
    /// # 参数
    /// 
    /// * `config` - 管理器配置
    /// 
    /// # 返回
    /// 
    /// 返回新的上下文管理器实例
    pub fn new(config: ContextManagerConfig) -> Self {
        // 验证配置
        if let Err(e) = config.validate() {
            tracing::warn!("配置验证失败，使用默认配置: {}", e);
        }

        let relevance_config = crate::context::relevance::RelevanceConfig {
            default_relevance_score: config.default_relevance_score,
            access_boost: config.access_weight,
            time_decay_factor: config.relevance_decay_factor,
            ..Default::default()
        };

        Self {
            config,
            entries: Arc::new(RwLock::new(HashMap::new())),
            index_manager: Arc::new(IndexManager::new()),
            stats_manager: Arc::new(RwLock::new(StatsManager::new())),
            compression_manager: Arc::new(RwLock::new(CompressionManager::new())),
            relevance_calculator: Arc::new(RwLock::new(RelevanceCalculator::new(relevance_config))),
            recent_entries: Arc::new(RwLock::new(VecDeque::new())),
        }
    }

    /// 使用默认配置创建上下文管理器
    /// 
    /// # 返回
    /// 
    /// 返回使用默认配置的上下文管理器实例
    pub fn with_default_config() -> Self {
        Self::new(ContextManagerConfig::default())
    }

    /// 添加上下文条目
    /// 
    /// # 参数
    /// 
    /// * `entry` - 要添加的上下文条目
    /// 
    /// # 返回
    /// 
    /// 返回条目ID
    pub async fn add_entry(&self, mut entry: ContextEntry) -> Result<Uuid> {
        // 设置创建时间和最后访问时间
        let now = SystemTime::now();
        entry.created_at = now;
        entry.last_accessed = now;
        entry.access_count = 0;

        // 如果启用智能相关性计算，更新相关性评分
        if self.config.enable_smart_relevance {
            let entries = self.entries.read().await;
            let mut calculator = self.relevance_calculator.write().await;
            entry.relevance_score = calculator.calculate_smart_relevance(&entry, Some(&*entries));
        }

        let entry_id = entry.id;
        let session_id = entry.session_id.clone();
        let entry_type = format!("{:?}", entry.entry_type);
        let importance = format!("{:?}", entry.importance);
        let tags = entry.tags.clone();

        // 存储条目
        {
            let mut entries = self.entries.write().await;
            entries.insert(entry_id, entry.clone());
        }

        // 更新索引
        self.index_manager
            .add_entry(entry_id, &session_id, &entry_type, &importance, &tags)
            .await;

        // 更新统计信息
        {
            let mut stats = self.stats_manager.write().await;
            stats.add_entry_stats(&entry);
        }

        // 更新最近访问列表
        {
            let mut recent = self.recent_entries.write().await;
            recent.push_front(entry_id);
            if recent.len() > 1000 {
                recent.pop_back();
            }
        }

        // 检查是否需要自动压缩
        self.check_auto_compression().await?;

        tracing::debug!("添加上下文条目: {}", entry_id);
        Ok(entry_id)
    }

    /// 获取上下文条目
    /// 
    /// # 参数
    /// 
    /// * `entry_id` - 条目ID
    /// 
    /// # 返回
    /// 
    /// 返回条目（如果存在）
    pub async fn get_entry(&self, entry_id: Uuid) -> Result<Option<ContextEntry>> {
        let mut entries = self.entries.write().await;
        if let Some(entry) = entries.get_mut(&entry_id) {
            // 更新访问信息
            entry.last_accessed = SystemTime::now();
            entry.access_count += 1;

            // 更新相关性评分（基于访问频率）
            if self.config.enable_smart_relevance {
                let calculator = self.relevance_calculator.read().await;
                entry.relevance_score = calculator.apply_access_boost(entry.relevance_score);
            }

            Ok(Some(entry.clone()))
        } else {
            Ok(None)
        }
    }

    /// 查询上下文条目
    /// 
    /// # 参数
    /// 
    /// * `query` - 查询条件
    /// 
    /// # 返回
    /// 
    /// 返回匹配的条目列表
    pub async fn query_entries(&self, query: ContextQuery) -> Result<Vec<ContextEntry>> {
        let entries = self.entries.read().await;
        let mut results = Vec::new();

        for entry in entries.values() {
            if self.matches_query(entry, &query) {
                results.push(entry.clone());
            }
        }

        // 按相关性评分排序
        results.sort_by(|a, b| {
            b.relevance_score
                .partial_cmp(&a.relevance_score)
                .unwrap_or(std::cmp::Ordering::Equal)
        });

        // 应用限制
        if let Some(limit) = query.limit {
            results.truncate(limit);
        }

        // 如果需要包含子条目
        if query.include_children {
            let mut all_results = Vec::new();
            for entry in results {
                all_results.push(entry.clone());
                all_results.extend(self.get_children_recursive(&entry).await);
            }
            results = all_results;
        }

        Ok(results)
    }

    /// 获取会话的所有条目
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话ID
    /// 
    /// # 返回
    /// 
    /// 返回会话的所有条目
    pub async fn get_session_entries(&self, session_id: &str) -> Result<Vec<ContextEntry>> {
        let query = ContextQuery::for_session(session_id.to_string());
        self.query_entries(query).await
    }

    /// 删除上下文条目
    /// 
    /// # 参数
    /// 
    /// * `entry_id` - 条目ID
    /// 
    /// # 返回
    /// 
    /// 返回是否成功删除
    pub async fn remove_entry(&self, entry_id: Uuid) -> Result<bool> {
        let removed_entry = {
            let mut entries = self.entries.write().await;
            entries.remove(&entry_id)
        };

        if let Some(entry) = removed_entry {
            // 更新索引
            let entry_type = format!("{:?}", entry.entry_type);
            let importance = format!("{:?}", entry.importance);
            self.index_manager
                .remove_entry(entry_id, &entry.session_id, &entry_type, &importance, &entry.tags)
                .await;

            // 更新统计信息
            {
                let mut stats = self.stats_manager.write().await;
                stats.remove_entry_stats(&entry);
            }

            // 从最近访问列表中移除
            {
                let mut recent = self.recent_entries.write().await;
                recent.retain(|&id| id != entry_id);
            }

            tracing::debug!("删除上下文条目: {}", entry_id);
            Ok(true)
        } else {
            Ok(false)
        }
    }

    /// 清理会话
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话ID
    /// 
    /// # 返回
    /// 
    /// 返回删除的条目数量
    pub async fn clear_session(&self, session_id: &str) -> Result<usize> {
        let session_entries = self.get_session_entries(session_id).await?;
        let mut removed_count = 0;

        for entry in session_entries {
            if self.remove_entry(entry.id).await? {
                removed_count += 1;
            }
        }

        tracing::info!("清理会话 '{}': 删除 {} 个条目", session_id, removed_count);
        Ok(removed_count)
    }

    /// 压缩上下文
    /// 
    /// # 参数
    /// 
    /// * `strategy` - 压缩策略（可选，使用配置中的默认策略）
    /// 
    /// # 返回
    /// 
    /// 返回删除的条目数量
    pub async fn compress_context(
        &self,
        strategy: Option<crate::context::config::CompressionStrategy>,
    ) -> Result<usize> {
        let strategy = strategy.unwrap_or_else(|| self.config.compression_strategy.clone());
        
        let entries_to_remove = {
            let entries = self.entries.read().await;
            let mut compression_manager = self.compression_manager.write().await;
            compression_manager.identify_entries_for_compression(&*entries, &strategy)?
        };

        let mut removed_count = 0;
        for entry_id in entries_to_remove {
            if self.remove_entry(entry_id).await? {
                removed_count += 1;
            }
        }

        tracing::info!("上下文压缩完成: 删除 {} 个条目", removed_count);
        Ok(removed_count)
    }

    /// 获取统计信息
    /// 
    /// # 返回
    /// 
    /// 返回当前统计信息
    pub async fn get_stats(&self) -> crate::context::stats::ContextStats {
        self.stats_manager.read().await.get_stats()
    }

    /// 更新条目的相关性评分
    /// 
    /// # 参数
    /// 
    /// * `entry_id` - 条目ID
    /// * `new_score` - 新的相关性评分
    /// 
    /// # 返回
    /// 
    /// 返回操作结果
    pub async fn update_relevance_score(&self, entry_id: Uuid, new_score: f64) -> Result<()> {
        let mut entries = self.entries.write().await;
        if let Some(entry) = entries.get_mut(&entry_id) {
            entry.relevance_score = new_score.clamp(0.0, 1.0);
            Ok(())
        } else {
            Err(OrionError::ContextError(format!("条目不存在: {}", entry_id)))
        }
    }

    /// 批量更新相关性评分
    /// 
    /// # 参数
    /// 
    /// * `updates` - 更新映射（条目ID -> 新评分）
    /// 
    /// # 返回
    /// 
    /// 返回更新的条目数量
    pub async fn batch_update_relevance(&self, updates: HashMap<Uuid, f64>) -> Result<usize> {
        let mut entries = self.entries.write().await;
        let mut updated_count = 0;

        for (entry_id, new_score) in updates {
            if let Some(entry) = entries.get_mut(&entry_id) {
                entry.relevance_score = new_score.clamp(0.0, 1.0);
                updated_count += 1;
            }
        }

        Ok(updated_count)
    }

    /// 检查查询是否匹配条目
    /// 
    /// # 参数
    /// 
    /// * `entry` - 条目
    /// * `query` - 查询条件
    /// 
    /// # 返回
    /// 
    /// 返回是否匹配
    fn matches_query(&self, entry: &ContextEntry, query: &ContextQuery) -> bool {
        // 检查会话 ID
        if let Some(session_id) = &query.session_id {
            if &entry.session_id != session_id {
                return false;
            }
        }

        // 检查条目类型
        if let Some(entry_types) = &query.entry_types {
            if !entry_types.contains(&entry.entry_type) {
                return false;
            }
        }

        // 检查标签
        if let Some(tags) = &query.tags {
            if !tags.iter().any(|tag| entry.tags.contains(tag)) {
                return false;
            }
        }

        // 检查相关性评分
        if let Some(min_relevance) = query.min_relevance {
            if entry.relevance_score < min_relevance {
                return false;
            }
        }

        // 检查重要性级别
        if let Some(min_importance) = &query.min_importance {
            if entry.importance < *min_importance {
                return false;
            }
        }

        // 检查时间范围
        if let Some(time_range) = &query.time_range {
            if !time_range.contains(entry.created_at) {
                return false;
            }
        }

        // 检查查询文本（简单的包含匹配）
        if let Some(query_text) = &query.query_text {
            let query_lower = query_text.to_lowercase();
            if !entry.content.to_lowercase().contains(&query_lower) {
                return false;
            }
        }

        true
    }

    /// 递归获取子条目
    ///
    /// # 参数
    ///
    /// * `entry` - 父条目
    ///
    /// # 返回
    ///
    /// 返回所有子条目
    fn get_children_recursive<'a>(&'a self, entry: &'a ContextEntry) -> std::pin::Pin<Box<dyn std::future::Future<Output = Vec<ContextEntry>> + Send + 'a>> {
        Box::pin(async move {
            let mut children = Vec::new();
            let entries = self.entries.read().await;

            // 首先收集所有直接子条目
            let mut direct_children = Vec::new();
            for child_id in &entry.children_ids {
                if let Some(child_entry) = entries.get(child_id) {
                    direct_children.push(child_entry.clone());
                }
            }

            // 释放读锁
            drop(entries);

            // 然后递归处理每个子条目
            for child_entry in direct_children {
                children.push(child_entry.clone());
                children.extend(self.get_children_recursive(&child_entry).await);
            }

            children
        })
    }

    /// 检查自动压缩
    /// 
    /// # 返回
    /// 
    /// 返回操作结果
    async fn check_auto_compression(&self) -> Result<()> {
        let stats = self.get_stats().await;
        let usage_ratio = stats.total_entries as f64 / self.config.max_entries as f64;

        if usage_ratio >= self.config.auto_compression_threshold {
            tracing::info!("触发自动压缩: 使用率 {:.2}%", usage_ratio * 100.0);
            self.compress_context(None).await?;
        }

        Ok(())
    }
}

impl Default for ContextManager {
    fn default() -> Self {
        Self::with_default_config()
    }
}

// 为了保持与原始代码的兼容性，添加一些辅助方法
impl ContextManager {
    /// 获取配置信息
    ///
    /// # 返回
    ///
    /// 返回当前配置
    pub fn get_config(&self) -> &ContextManagerConfig {
        &self.config
    }

    /// 获取索引管理器
    ///
    /// # 返回
    ///
    /// 返回索引管理器的引用
    pub fn index_manager(&self) -> &IndexManager {
        &self.index_manager
    }

    /// 获取所有会话ID
    ///
    /// # 返回
    ///
    /// 返回所有活跃会话的ID列表
    pub async fn get_all_sessions(&self) -> Vec<String> {
        self.index_manager.get_all_sessions().await
    }

    /// 获取所有标签
    ///
    /// # 返回
    ///
    /// 返回所有使用过的标签列表
    pub async fn get_all_tags(&self) -> Vec<String> {
        self.index_manager.get_all_tags().await
    }

    /// 获取压缩预估
    ///
    /// # 参数
    ///
    /// * `strategy` - 压缩策略
    ///
    /// # 返回
    ///
    /// 返回压缩预估结果
    pub async fn estimate_compression(
        &self,
        strategy: &crate::context::config::CompressionStrategy,
    ) -> crate::context::compression::CompressionEstimate {
        let entries = self.entries.read().await;
        let compression_manager = self.compression_manager.read().await;
        compression_manager.estimate_compression_effect(&*entries, strategy)
    }

    /// 重新计算所有条目的相关性评分
    ///
    /// # 返回
    ///
    /// 返回更新的条目数量
    pub async fn recalculate_all_relevance(&self) -> Result<usize> {
        let entries = self.entries.read().await;
        let mut calculator = self.relevance_calculator.write().await;
        let updates = calculator.batch_calculate_relevance(&*entries);
        drop(entries);
        drop(calculator);

        self.batch_update_relevance(updates).await
    }

    /// 清理空的索引条目
    pub async fn cleanup_indexes(&self) {
        self.index_manager.cleanup_empty_entries().await;
    }

    /// 获取详细的系统状态
    ///
    /// # 返回
    ///
    /// 返回系统状态信息
    pub async fn get_system_status(&self) -> SystemStatus {
        let stats = self.get_stats().await;
        let index_stats = self.index_manager.get_index_stats().await;
        let compression_stats = self.compression_manager.read().await.get_stats().clone();
        let relevance_stats = self.relevance_calculator.read().await.get_stats().clone();

        SystemStatus {
            context_stats: stats,
            index_stats,
            compression_stats,
            relevance_stats,
            config: self.config.clone(),
        }
    }
}

/// 系统状态信息
///
/// 包含上下文管理系统的完整状态信息
#[derive(Debug, Clone)]
pub struct SystemStatus {
    /// 上下文统计信息
    pub context_stats: crate::context::stats::ContextStats,
    /// 索引统计信息
    pub index_stats: crate::context::index::IndexStats,
    /// 压缩统计信息
    pub compression_stats: crate::context::compression::CompressionStats,
    /// 相关性统计信息
    pub relevance_stats: crate::context::relevance::RelevanceStats,
    /// 配置信息
    pub config: ContextManagerConfig,
}

impl SystemStatus {
    /// 生成系统状态报告
    ///
    /// # 返回
    ///
    /// 返回格式化的系统状态报告
    pub fn generate_report(&self) -> String {
        let mut report = String::new();

        report.push_str("=== 上下文管理系统状态报告 ===\n\n");

        // 基本统计信息
        report.push_str(&self.context_stats.generate_report());
        report.push_str("\n\n");

        // 索引统计信息
        report.push_str(&self.index_stats.generate_report());
        report.push_str("\n\n");

        // 相关性统计信息
        report.push_str(&self.relevance_stats.generate_report());
        report.push_str("\n\n");

        // 配置信息
        report.push_str("=== 配置信息 ===\n");
        report.push_str(&format!("最大条目数: {}\n", self.config.max_entries));
        report.push_str(&format!("最大会话数: {}\n", self.config.max_sessions));
        report.push_str(&format!("自动压缩阈值: {:.1}%\n", self.config.auto_compression_threshold * 100.0));
        report.push_str(&format!("压缩策略: {}\n", self.config.compression_strategy.description()));
        report.push_str(&format!("智能相关性计算: {}\n", if self.config.enable_smart_relevance { "启用" } else { "禁用" }));

        report
    }
}
