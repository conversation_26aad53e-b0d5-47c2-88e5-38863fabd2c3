@echo off
chcp 65001 >nul
REM Orion Development Environment Setup Script

echo Setting up Orion development environment...

REM Create PowerShell Profile if not exists
powershell -Command "if (!(Test-Path $PROFILE)) { New-Item -Path $PROFILE -ItemType File -Force }"

REM Add Orion configuration to PowerShell Profile
powershell -Command "if ((Get-Content $PROFILE -Raw -ErrorAction SilentlyContinue) -notmatch 'Orion Dev Environment') { Add-Content $PROFILE \"`n# Orion Dev Environment`nfunction orion { & 'E:\Orion\scripts\orion-dev.bat' @args }`nfunction cdo { Set-Location 'E:\Orion' }`nWrite-Host 'Orion dev environment loaded' -ForegroundColor Green\" }"

echo PowerShell Profile updated successfully

echo.
echo Next steps:
echo 1. Restart PowerShell or run: . $PROFILE
echo 2. Use 'orion' command to start development
echo.

set /p choice="Reload PowerShell Profile now? (y/n): "
if /i "%choice%"=="y" (
    powershell -Command ". $PROFILE"
    echo Environment reloaded successfully
)

pause