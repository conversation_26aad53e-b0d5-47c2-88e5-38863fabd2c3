//! # 配置验证功能
//!
//! 实现配置文件的验证和检查功能。

use crate::error::{CliError, Result};
use crate::commands::config::types::ValidateConfig;
use orion_core::config::OrionConfig;

impl ValidateConfig {
    /// 执行验证配置
    pub async fn execute(&self) -> Result<()> {
        println!("🔍 验证配置文件: {}", self.config.display());
        
        // 检查文件是否存在
        if !self.config.exists() {
            return Err(CliError::ConfigError {
                error: format!("配置文件不存在: {}", self.config.display()),
            });
        }
        
        // 尝试加载配置
        let config = match OrionConfig::from_file(&self.config) {
            Ok(config) => {
                println!("✅ 配置文件格式正确");
                config
            }
            Err(e) => {
                return Err(CliError::ConfigError {
                    error: format!("配置文件格式错误: {}", e),
                });
            }
        };
        
        // 验证配置内容
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        
        self.validate_agent_config(&config, &mut errors, &mut warnings);
        self.validate_security_config(&config, &mut errors, &mut warnings);
        self.validate_workflow_config(&config, &mut errors, &mut warnings);
        self.validate_logging_config(&config, &mut errors, &mut warnings);
        self.validate_tools_config(&config, &mut errors, &mut warnings);
        
        // 输出验证结果
        if !warnings.is_empty() {
            println!("\n⚠️  警告:");
            for warning in &warnings {
                println!("  - {}", warning);
            }
        }
        
        if !errors.is_empty() {
            println!("\n❌ 错误:");
            for error in &errors {
                println!("  - {}", error);
            }
            return Err(CliError::ConfigError {
                error: format!("配置验证失败，发现 {} 个错误", errors.len()),
            });
        }
        
        println!("\n✅ 配置验证通过");
        
        if self.verbose {
            println!("\n📊 配置摘要:");
            self.print_config_summary(&config);
        }
        
        Ok(())
    }
    
    /// 验证 Agent 配置
    fn validate_agent_config(&self, config: &OrionConfig, errors: &mut Vec<String>, warnings: &mut Vec<String>) {
        let agents = &config.agents;
        if agents.default_model.is_empty() {
            errors.push("Agent 默认模型不能为空".to_string());
        }

        if agents.max_concurrent == 0 {
            errors.push("最大并发 Agent 数量必须大于 0".to_string());
        }

        if agents.default_timeout == 0 {
            warnings.push("Agent 默认超时时间为 0 可能导致任务永不超时".to_string());
        }

        if agents.max_context_length == 0 {
            errors.push("最大上下文长度必须大于 0".to_string());
        }
    }
    
    /// 验证安全配置
    fn validate_security_config(&self, config: &OrionConfig, errors: &mut Vec<String>, warnings: &mut Vec<String>) {
        let security = &config.security;
        if security.sandbox.timeout == 0 {
            warnings.push("沙箱超时时间为 0 可能导致任务永不超时".to_string());
        }

        if security.sandbox.memory_limit == 0 {
            warnings.push("内存限制为 0 可能导致无限制内存使用".to_string());
        }

        if security.sandbox.cpu_limit == 0 || security.sandbox.cpu_limit > 100 {
            errors.push("CPU 使用率限制必须在 1-100 范围内".to_string());
        }
    }
    
    /// 验证工作流配置
    fn validate_workflow_config(&self, config: &OrionConfig, errors: &mut Vec<String>, warnings: &mut Vec<String>) {
        let workflows = &config.workflows;
        if workflows.max_concurrent_workflows == 0 {
            errors.push("最大并发工作流数必须大于 0".to_string());
        }

        if workflows.max_execution_time == 0 {
            warnings.push("最大执行时间为 0 可能导致工作流永不超时".to_string());
        }
    }
    
    /// 验证日志配置
    fn validate_logging_config(&self, config: &OrionConfig, errors: &mut Vec<String>, warnings: &mut Vec<String>) {
        let logging = &config.logging;
        let valid_levels = ["trace", "debug", "info", "warn", "error"];
        if !valid_levels.contains(&logging.level.as_str()) {
            errors.push(format!("无效的日志级别: {}", logging.level));
        }

        if logging.enable_file && logging.file_path.is_none() {
            errors.push("启用文件日志时必须指定日志文件路径".to_string());
        }

        if logging.rotation_size == 0 {
            warnings.push("日志文件大小限制为 0 可能导致日志文件无限增长".to_string());
        }
    }
    
    /// 验证工具配置
    fn validate_tools_config(&self, config: &OrionConfig, errors: &mut Vec<String>, warnings: &mut Vec<String>) {
        let tools = &config.tools;
        if tools.execution_timeout == 0 {
            warnings.push("工具执行超时时间为 0 可能导致工具永不超时".to_string());
        }

        if tools.max_concurrent_executions == 0 {
            errors.push("最大并发工具执行数必须大于 0".to_string());
        }
    }
    
    /// 打印配置摘要
    fn print_config_summary(&self, config: &OrionConfig) {
        let agents = &config.agents;
        println!("  Agent: {} (并发: {}, 超时: {}s)",
            agents.default_model, agents.max_concurrent, agents.default_timeout);

        let security = &config.security;
        println!("  安全: 沙箱={}, 内存={}MB, CPU={}%",
            security.enable_sandbox, security.sandbox.memory_limit, security.sandbox.cpu_limit);

        let workflows = &config.workflows;
        println!("  工作流: 并发={}, 自动保存={}",
            workflows.max_concurrent_workflows, workflows.auto_save);

        let logging = &config.logging;
        println!("  日志: 级别={}, 文件日志={}",
            logging.level, logging.enable_file);
    }
}