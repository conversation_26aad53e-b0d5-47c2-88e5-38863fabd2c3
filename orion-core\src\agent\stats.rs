//! # Agent 统计信息管理模块
//!
//! 提供Agent运行统计信息的收集、更新和分析功能，
//! 帮助监控Agent的性能和使用情况。

use crate::agent::types::{AgentStats, TaskResponse};
use std::sync::Arc;
use std::time::SystemTime;
use tokio::sync::Mutex;

/// 统计信息管理器
/// 
/// 负责收集和管理Agent的运行统计数据
pub struct StatsManager {
    /// 统计数据，使用Mutex保证线程安全
    stats: Arc<Mutex<AgentStats>>,
    /// Agent名称，用于日志记录
    agent_name: String,
}

impl StatsManager {
    /// 创建新的统计信息管理器
    /// 
    /// # 参数
    /// * `agent_name` - Agent名称，用于日志记录
    /// 
    /// # 返回
    /// 返回新创建的StatsManager实例
    pub fn new(agent_name: String) -> Self {
        let mut stats = AgentStats::default();
        stats.started_at = SystemTime::now();
        stats.last_activity_at = SystemTime::now();

        Self {
            stats: Arc::new(Mutex::new(stats)),
            agent_name,
        }
    }

    /// 获取当前统计信息
    /// 
    /// # 返回
    /// 返回当前统计信息的克隆
    pub async fn get_stats(&self) -> AgentStats {
        self.stats.lock().await.clone()
    }

    /// 更新任务完成统计
    /// 
    /// 根据任务响应更新相关的统计信息
    /// 
    /// # 参数
    /// * `response` - 任务响应结果
    pub async fn update_task_stats(&self, response: &TaskResponse) {
        let mut stats = self.stats.lock().await;
        
        // 更新任务计数
        stats.total_tasks += 1;
        
        if response.success {
            stats.successful_tasks += 1;
        } else {
            stats.failed_tasks += 1;
        }

        // 更新工具调用统计
        stats.total_tool_calls += response.tool_results.len() as u64;
        stats.successful_tool_calls += response.tool_results.iter()
            .filter(|result| result.success)
            .count() as u64;

        // 更新处理时间统计
        stats.total_processing_time_ms += response.processing_time_ms;
        stats.average_processing_time_ms = if stats.total_tasks > 0 {
            stats.total_processing_time_ms / stats.total_tasks
        } else {
            0
        };

        // 更新令牌使用统计
        if let Some(token_usage) = &response.token_usage {
            stats.total_tokens_used += token_usage.total_tokens as u64;
        }

        // 更新最后活动时间
        stats.last_activity_at = SystemTime::now();

        tracing::debug!(
            "Agent '{}' 统计信息已更新: 总任务数={}, 成功率={:.2}%",
            self.agent_name,
            stats.total_tasks,
            self.calculate_success_rate(&stats)
        );
    }

    /// 增加工具调用计数
    /// 
    /// 当Agent调用工具时更新统计信息
    /// 
    /// # 参数
    /// * `success` - 工具调用是否成功
    pub async fn increment_tool_call(&self, success: bool) {
        let mut stats = self.stats.lock().await;
        stats.total_tool_calls += 1;
        
        if success {
            stats.successful_tool_calls += 1;
        }

        stats.last_activity_at = SystemTime::now();

        tracing::debug!(
            "Agent '{}' 工具调用统计已更新: 总调用数={}, 成功率={:.2}%",
            self.agent_name,
            stats.total_tool_calls,
            self.calculate_tool_success_rate(&stats)
        );
    }

    /// 添加处理时间
    /// 
    /// 添加单次操作的处理时间到统计中
    /// 
    /// # 参数
    /// * `processing_time_ms` - 处理时间（毫秒）
    pub async fn add_processing_time(&self, processing_time_ms: u64) {
        let mut stats = self.stats.lock().await;
        stats.total_processing_time_ms += processing_time_ms;
        
        // 重新计算平均处理时间
        if stats.total_tasks > 0 {
            stats.average_processing_time_ms = stats.total_processing_time_ms / stats.total_tasks;
        }

        stats.last_activity_at = SystemTime::now();
    }

    /// 添加令牌使用量
    /// 
    /// 添加LLM调用的令牌使用量到统计中
    /// 
    /// # 参数
    /// * `tokens_used` - 使用的令牌数量
    pub async fn add_tokens_used(&self, tokens_used: u64) {
        let mut stats = self.stats.lock().await;
        stats.total_tokens_used += tokens_used;
        stats.last_activity_at = SystemTime::now();

        tracing::debug!(
            "Agent '{}' 令牌使用统计已更新: 总使用量={}",
            self.agent_name,
            stats.total_tokens_used
        );
    }

    /// 重置统计信息
    /// 
    /// 清除所有统计数据，重新开始计数
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.lock().await;
        let now = SystemTime::now();
        
        *stats = AgentStats {
            total_tasks: 0,
            successful_tasks: 0,
            failed_tasks: 0,
            total_tool_calls: 0,
            successful_tool_calls: 0,
            total_processing_time_ms: 0,
            average_processing_time_ms: 0,
            total_tokens_used: 0,
            started_at: now,
            last_activity_at: now,
        };

        tracing::info!("Agent '{}' 统计信息已重置", self.agent_name);
    }

    /// 获取运行时间
    /// 
    /// # 返回
    /// 返回Agent运行的总时间（毫秒）
    pub async fn get_uptime_ms(&self) -> u64 {
        let stats = self.stats.lock().await;
        SystemTime::now()
            .duration_since(stats.started_at)
            .map(|d| d.as_millis() as u64)
            .unwrap_or(0)
    }

    /// 获取空闲时间
    /// 
    /// # 返回
    /// 返回自最后活动以来的空闲时间（毫秒）
    pub async fn get_idle_time_ms(&self) -> u64 {
        let stats = self.stats.lock().await;
        SystemTime::now()
            .duration_since(stats.last_activity_at)
            .map(|d| d.as_millis() as u64)
            .unwrap_or(0)
    }

    /// 计算任务成功率
    /// 
    /// # 返回
    /// 返回任务成功率（百分比）
    pub async fn get_task_success_rate(&self) -> f64 {
        let stats = self.stats.lock().await;
        self.calculate_success_rate(&stats)
    }

    /// 计算工具调用成功率
    /// 
    /// # 返回
    /// 返回工具调用成功率（百分比）
    pub async fn get_tool_success_rate(&self) -> f64 {
        let stats = self.stats.lock().await;
        self.calculate_tool_success_rate(&stats)
    }

    /// 获取平均每小时任务数
    /// 
    /// # 返回
    /// 返回平均每小时处理的任务数
    pub async fn get_tasks_per_hour(&self) -> f64 {
        let stats = self.stats.lock().await;
        let uptime_hours = SystemTime::now()
            .duration_since(stats.started_at)
            .map(|d| d.as_secs_f64() / 3600.0)
            .unwrap_or(0.0);

        if uptime_hours > 0.0 {
            stats.total_tasks as f64 / uptime_hours
        } else {
            0.0
        }
    }

    /// 获取平均每小时令牌使用量
    /// 
    /// # 返回
    /// 返回平均每小时使用的令牌数
    pub async fn get_tokens_per_hour(&self) -> f64 {
        let stats = self.stats.lock().await;
        let uptime_hours = SystemTime::now()
            .duration_since(stats.started_at)
            .map(|d| d.as_secs_f64() / 3600.0)
            .unwrap_or(0.0);

        if uptime_hours > 0.0 {
            stats.total_tokens_used as f64 / uptime_hours
        } else {
            0.0
        }
    }

    /// 生成统计报告
    /// 
    /// # 返回
    /// 返回格式化的统计报告字符串
    pub async fn generate_report(&self) -> String {
        let stats = self.stats.lock().await;
        let uptime_ms = self.get_uptime_ms().await;
        let idle_ms = self.get_idle_time_ms().await;
        
        format!(
            "Agent '{}' 统计报告:\n\
            ==================\n\
            运行时间: {:.2} 小时\n\
            空闲时间: {:.2} 分钟\n\
            总任务数: {}\n\
            成功任务: {} ({:.1}%)\n\
            失败任务: {} ({:.1}%)\n\
            工具调用: {} (成功率: {:.1}%)\n\
            平均处理时间: {} ms\n\
            总令牌使用: {}\n\
            任务处理速度: {:.1} 任务/小时\n\
            令牌使用速度: {:.0} 令牌/小时",
            self.agent_name,
            uptime_ms as f64 / (1000.0 * 3600.0),
            idle_ms as f64 / (1000.0 * 60.0),
            stats.total_tasks,
            stats.successful_tasks,
            self.calculate_success_rate(&stats),
            stats.failed_tasks,
            if stats.total_tasks > 0 { 
                (stats.failed_tasks as f64 / stats.total_tasks as f64) * 100.0 
            } else { 
                0.0 
            },
            stats.total_tool_calls,
            self.calculate_tool_success_rate(&stats),
            stats.average_processing_time_ms,
            stats.total_tokens_used,
            self.get_tasks_per_hour().await,
            self.get_tokens_per_hour().await
        )
    }

    /// 计算任务成功率（内部方法）
    fn calculate_success_rate(&self, stats: &AgentStats) -> f64 {
        if stats.total_tasks > 0 {
            (stats.successful_tasks as f64 / stats.total_tasks as f64) * 100.0
        } else {
            0.0
        }
    }

    /// 计算工具调用成功率（内部方法）
    fn calculate_tool_success_rate(&self, stats: &AgentStats) -> f64 {
        if stats.total_tool_calls > 0 {
            (stats.successful_tool_calls as f64 / stats.total_tool_calls as f64) * 100.0
        } else {
            0.0
        }
    }
}

impl Clone for StatsManager {
    /// 克隆统计信息管理器
    /// 
    /// 注意：克隆的实例共享相同的统计数据
    fn clone(&self) -> Self {
        Self {
            stats: self.stats.clone(),
            agent_name: self.agent_name.clone(),
        }
    }
}
