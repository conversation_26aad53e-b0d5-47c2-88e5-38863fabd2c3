//! # Agent 配置模块
//!
//! 定义Agent的配置结构体和相关策略，包括工具使用策略、
//! 错误处理策略等配置选项。

use crate::sandbox::SandboxConfig;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 工具使用策略枚举
/// 
/// 定义Agent使用工具的不同策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ToolUseStrategy {
    /// 保守策略 - 仅使用明确需要的工具，减少不必要的工具调用
    Conservative,
    /// 积极策略 - 主动使用工具来增强响应质量
    Aggressive,
    /// 平衡策略 - 在保守和积极之间取得平衡
    Balanced,
    /// 自定义策略 - 用户自定义的工具使用规则
    Custom {
        /// 工具选择阈值（0.0-1.0），决定何时使用工具
        selection_threshold: f32,
        /// 最大并发工具数，限制同时执行的工具数量
        max_concurrent_tools: u32,
    },
}

/// Agent 错误处理策略枚举
/// 
/// 定义Agent遇到错误时的处理方式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AgentErrorHandlingStrategy {
    /// 立即停止 - 遇到错误立即停止执行
    Stop,
    /// 重试策略 - 自动重试失败的操作
    Retry {
        /// 最大重试次数
        max_attempts: u32,
        /// 重试间隔时间（秒）
        retry_interval_seconds: u64,
    },
    /// 降级处理 - 使用备用模型或方法
    Fallback {
        /// 降级使用的模型名称
        fallback_model: String,
    },
    /// 请求人工干预 - 通知用户需要手动处理
    RequestHumanIntervention,
}

/// Agent 配置结构体
/// 
/// 包含Agent运行所需的所有配置参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AgentConfig {
    /// Agent唯一标识符
    pub id: Uuid,
    /// Agent名称，用于识别和管理
    pub name: String,
    /// Agent描述，说明其功能和用途
    pub description: String,
    /// 默认使用的LLM模型名称
    pub default_model: String,
    /// 系统提示词，定义Agent的行为和角色
    pub system_prompt: String,
    /// 最大上下文长度，限制输入的token数量
    pub max_context_length: usize,
    /// 最大工具调用深度，防止无限递归调用
    pub max_tool_call_depth: u32,
    /// 是否启用沙箱环境，用于安全执行代码
    pub enable_sandbox: bool,
    /// 沙箱配置参数（如果启用沙箱）
    pub sandbox_config: Option<SandboxConfig>,
    /// 是否启用记忆功能，保存对话历史
    pub enable_memory: bool,
    /// 记忆保留时间（秒），超时后自动清理
    pub memory_retention_seconds: Option<u64>,
    /// 是否启用学习功能，从交互中学习改进
    pub enable_learning: bool,
    /// 温度参数（0.0-2.0），控制响应的随机性
    pub temperature: f32,
    /// 最大生成token数量
    pub max_tokens: Option<u32>,
    /// 工具使用策略
    pub tool_use_strategy: ToolUseStrategy,
    /// 错误处理策略
    pub error_handling_strategy: AgentErrorHandlingStrategy,
}

impl Default for AgentConfig {
    /// 创建默认的Agent配置
    /// 
    /// 提供合理的默认值，适用于大多数使用场景
    fn default() -> Self {
        Self {
            id: Uuid::new_v4(),
            name: "Orion Agent".to_string(),
            description: "智能助手Agent，能够处理各种任务和问题".to_string(),
            default_model: "gpt-4".to_string(),
            system_prompt: "你是一个智能助手，能够帮助用户完成各种任务。请始终保持友好、专业和有帮助的态度。".to_string(),
            max_context_length: 8192,
            max_tool_call_depth: 10,
            enable_sandbox: true,
            sandbox_config: Some(SandboxConfig::default()),
            enable_memory: true,
            memory_retention_seconds: Some(86400), // 24小时
            enable_learning: false,
            temperature: 0.7,
            max_tokens: Some(2048),
            tool_use_strategy: ToolUseStrategy::Balanced,
            error_handling_strategy: AgentErrorHandlingStrategy::Retry {
                max_attempts: 3,
                retry_interval_seconds: 1,
            },
        }
    }
}

impl AgentConfig {
    /// 创建新的Agent配置
    /// 
    /// # 参数
    /// * `name` - Agent名称
    /// * `description` - Agent描述
    /// * `model` - 使用的LLM模型
    /// 
    /// # 返回
    /// 返回新创建的AgentConfig实例
    pub fn new(name: String, description: String, model: String) -> Self {
        Self {
            name,
            description,
            default_model: model,
            ..Default::default()
        }
    }

    /// 设置系统提示词
    /// 
    /// # 参数
    /// * `prompt` - 新的系统提示词
    /// 
    /// # 返回
    /// 返回修改后的配置实例（链式调用）
    pub fn with_system_prompt(mut self, prompt: String) -> Self {
        self.system_prompt = prompt;
        self
    }

    /// 设置温度参数
    /// 
    /// # 参数
    /// * `temperature` - 温度值（0.0-2.0）
    /// 
    /// # 返回
    /// 返回修改后的配置实例（链式调用）
    pub fn with_temperature(mut self, temperature: f32) -> Self {
        self.temperature = temperature.clamp(0.0, 2.0);
        self
    }

    /// 设置最大token数量
    /// 
    /// # 参数
    /// * `max_tokens` - 最大token数量
    /// 
    /// # 返回
    /// 返回修改后的配置实例（链式调用）
    pub fn with_max_tokens(mut self, max_tokens: u32) -> Self {
        self.max_tokens = Some(max_tokens);
        self
    }

    /// 设置工具使用策略
    /// 
    /// # 参数
    /// * `strategy` - 工具使用策略
    /// 
    /// # 返回
    /// 返回修改后的配置实例（链式调用）
    pub fn with_tool_strategy(mut self, strategy: ToolUseStrategy) -> Self {
        self.tool_use_strategy = strategy;
        self
    }

    /// 启用或禁用沙箱
    /// 
    /// # 参数
    /// * `enable` - 是否启用沙箱
    /// 
    /// # 返回
    /// 返回修改后的配置实例（链式调用）
    pub fn with_sandbox(mut self, enable: bool) -> Self {
        self.enable_sandbox = enable;
        if enable && self.sandbox_config.is_none() {
            self.sandbox_config = Some(SandboxConfig::default());
        }
        self
    }

    /// 启用或禁用记忆功能
    /// 
    /// # 参数
    /// * `enable` - 是否启用记忆
    /// * `retention_seconds` - 记忆保留时间（可选）
    /// 
    /// # 返回
    /// 返回修改后的配置实例（链式调用）
    pub fn with_memory(mut self, enable: bool, retention_seconds: Option<u64>) -> Self {
        self.enable_memory = enable;
        self.memory_retention_seconds = retention_seconds;
        self
    }

    /// 验证配置的有效性
    /// 
    /// # 返回
    /// 如果配置有效返回Ok(())，否则返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.name.is_empty() {
            return Err("Agent名称不能为空".to_string());
        }

        if self.default_model.is_empty() {
            return Err("默认模型不能为空".to_string());
        }

        if self.temperature < 0.0 || self.temperature > 2.0 {
            return Err("温度参数必须在0.0-2.0之间".to_string());
        }

        if self.max_context_length == 0 {
            return Err("最大上下文长度必须大于0".to_string());
        }

        if self.max_tool_call_depth == 0 {
            return Err("最大工具调用深度必须大于0".to_string());
        }

        // 验证自定义工具策略的参数
        if let ToolUseStrategy::Custom { selection_threshold, max_concurrent_tools } = &self.tool_use_strategy {
            if *selection_threshold < 0.0 || *selection_threshold > 1.0 {
                return Err("工具选择阈值必须在0.0-1.0之间".to_string());
            }
            if *max_concurrent_tools == 0 {
                return Err("最大并发工具数必须大于0".to_string());
            }
        }

        Ok(())
    }
}
