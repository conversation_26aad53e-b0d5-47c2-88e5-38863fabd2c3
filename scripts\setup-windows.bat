@echo off
REM Orion 开发环境一键设置脚本

echo 🚀 正在设置 Orion 开发环境...

REM 检查 PowerShell Profile 是否存在
powershell -Command "if (!(Test-Path $PROFILE)) { New-Item -Path $PROFILE -ItemType File -Force }"

REM 检查是否已经添加了 Orion 配置
powershell -Command "$profileContent = Get-Content $PROFILE -Raw -ErrorAction SilentlyContinue; if ($profileContent -notmatch 'Orion 开发环境') { Add-Content $PROFILE \"`n# Orion 开发环境`n. 'E:\Orion\scripts\orion-profile.ps1'\" }"

echo ✅ PowerShell Profile 已更新

echo.
echo 📋 下一步操作：
echo 1. 重启 PowerShell 或执行：. $PROFILE
echo 2. 使用 'orion' 命令开始开发
echo.

REM 提供立即生效的选项
set /p choice="是否立即重新加载 PowerShell Profile? (y/n): "
if /i "%choice%"=="y" (
    powershell -Command ". $PROFILE; Write-Host '🎉 环境已生效，可以使用 orion 命令了' -ForegroundColor Green"
)

pause