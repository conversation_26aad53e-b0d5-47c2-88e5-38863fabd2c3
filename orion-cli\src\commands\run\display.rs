//! # 显示和输出功能
//!
//! 实现响应显示和打字机效果等输出功能。

use crate::error::{CliError, Result};
use crate::commands::run::types::RunCommand;

impl RunCommand {
    /// 打字机效果显示文本
    #[allow(dead_code)] // 这个方法将在未来版本中使用
    pub async fn typewriter_print(&self, text: &str) {
        for ch in text.chars() {
            // 根据字符类型使用不同颜色
            let colored_char = match ch {
                '!' | '？' | '。' | '，' | '、' => format!("\x1b[36m{}\x1b[0m", ch), // 青色标点
                '0'..='9' => format!("\x1b[33m{}\x1b[0m", ch), // 黄色数字
                'A'..='Z' | 'a'..='z' => format!("\x1b[32m{}\x1b[0m", ch), // 绿色英文
                _ => ch.to_string(), // 默认颜色中文
            };

            print!("{}", colored_char);
            std::io::Write::flush(&mut std::io::stdout()).unwrap();

            // 根据配置的速度添加延迟
            if self.typing_speed > 0 {
                // 对于标点符号稍微延迟长一点
                let delay = match ch {
                    '!' | '？' | '。' => self.typing_speed * 3,
                    '，' | '、' => self.typing_speed * 2,
                    ' ' => self.typing_speed / 2, // 空格延迟短一点
                    _ => self.typing_speed,
                };
                tokio::time::sleep(tokio::time::Duration::from_millis(delay)).await;
            }
        }
    }
    
    /// 打印响应
    pub fn print_response(&self, response: &orion_core::agent::TaskResponse) -> Result<()> {
        match self.output_format.as_str() {
            "json" => {
                let json = serde_json::to_string_pretty(response)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化响应失败: {}", e),
                    })?;
                println!("{}", json);
            }
            "yaml" => {
                let yaml = serde_yaml::to_string(response)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化响应失败: {}", e),
                    })?;
                println!("{}", yaml);
            }
            "text" | _ => {
                self.print_text_response(response)?;
            }
        }
        
        Ok(())
    }
    
    /// 打印文本格式响应
    fn print_text_response(&self, response: &orion_core::agent::TaskResponse) -> Result<()> {
        if response.success {
            println!("✅ {}", response.content);
        } else {
            eprintln!("❌ {}", response.content);
            if let Some(error) = &response.error {
                eprintln!("错误详情: {}", error);
            }
        }
        
        // 显示工具调用结果
        if !response.tool_results.is_empty() {
            println!("\n🔧 工具调用结果:");
            for (i, tool_result) in response.tool_results.iter().enumerate() {
                println!("  {}. 工具调用 {} - {}",
                    i + 1,
                    tool_result.request_id,
                    if tool_result.success { "成功" } else { "失败" }
                );
            }
        }
        
        // 显示生成的文件
        if !response.generated_files.is_empty() {
            println!("\n📁 生成的文件:");
            for file in &response.generated_files {
                println!("  - {}", file);
            }
        }
        
        // 显示处理时间
        if self.verbose {
            println!("\n⏱️  处理时间: {}ms", response.processing_time_ms);
            if let Some(token_usage) = &response.token_usage {
                println!("🎯 令牌使用: {} 总计 ({} 输入 + {} 输出)",
                    token_usage.total_tokens,
                    token_usage.input_tokens,
                    token_usage.output_tokens
                );
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_display_config() {
        let cmd = RunCommand {
            config: None,
            name: "display-test".to_string(),
            interactive: true,
            verbose: true,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 10,
            workdir: None,
            command: None,
            file: None,
            output_format: "yaml".to_string(),
            stream: true,
            typing_speed: 25,
        };
        
        assert_eq!(cmd.output_format, "yaml");
        assert!(cmd.verbose);
        assert!(cmd.stream);
        assert_eq!(cmd.typing_speed, 25);
    }
}