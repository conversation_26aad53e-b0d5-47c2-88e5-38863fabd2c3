//! # 工作流管理功能
//!
//! 实现工作流的删除功能。

use crate::error::Result;
use crate::commands::workflow::{types::DeleteWorkflow, utils::*};

impl DeleteWorkflow {
    /// 执行删除工作流
    pub async fn execute(&self) -> Result<()> {
        let _workflow_manager = create_workflow_manager(&self.config).await?;
        
        if !self.yes {
            println!("⚠️  确认删除工作流 '{}'? 请使用 --yes 参数确认", self.workflow);
            return Ok(());
        }
        
        println!("🗑️  正在删除工作流: {}", self.workflow);
        
        if self.all_instances {
            println!("📁 同时删除所有相关实例...");
            simulate_processing_delay(800).await;
        }
        
        simulate_processing_delay(500).await;
        
        println!("✅ 工作流 '{}' 已成功删除", self.workflow);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[tokio::test]
    async fn test_delete_workflow() {
        let cmd = DeleteWorkflow {
            config: PathBuf::from("test.toml"),
            workflow: "test-workflow".to_string(),
            yes: false,
            all_instances: false,
        };

        assert_eq!(cmd.workflow, "test-workflow");
        assert!(!cmd.yes);
        assert!(!cmd.all_instances);
    }
}