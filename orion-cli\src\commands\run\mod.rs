//! # 运行命令模块
//!
//! 实现 Orion Agent 的启动和交互式运行功能，重构为模块化结构。

// 声明子模块
pub mod types;
pub mod utils;
pub mod setup;
pub mod execution;
pub mod interactive;
pub mod daemon;
pub mod display;

// 重新导出主要类型
pub use types::*;

use crate::error::Result;

impl RunCommand {
    /// 执行运行命令
    pub async fn execute(&mut self) -> Result<()> {
        // 加载配置
        let config = utils::load_config(self.config.as_ref()).await?;
        
        // 创建核心组件
        let (agent, _components) = self.create_agent(config).await?;
        
        tracing::info!("Orion Agent '{}' 启动成功", self.name);
        
        // 根据运行模式执行
        match self.determine_run_mode() {
            RunMode::SingleCommand(command) => {
                self.execute_single_command(&agent, &command).await
            }
            RunMode::FileExecution(file_path) => {
                self.execute_from_file(&agent, &file_path).await
            }
            RunMode::Interactive => {
                self.run_interactive_mode(&agent).await
            }
            RunMode::Daemon => {
                self.run_daemon_mode(&agent).await
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_run_command_creation() {
        let cmd = RunCommand {
            config: None,
            name: "test-agent".to_string(),
            interactive: false,
            verbose: false,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 5,
            workdir: None,
            command: Some("hello".to_string()),
            file: None,
            output_format: "text".to_string(),
            stream: false,
            no_stream: false,
            typing_speed: 30,
        };
        
        assert_eq!(cmd.name, "test-agent");
        assert!(!cmd.interactive);
        assert_eq!(cmd.max_concurrent_tasks, 5);
    }

    #[test]
    fn test_run_mode_determination() {
        // 测试单命令模式
        let cmd_single = RunCommand {
            config: None,
            name: "test".to_string(),
            interactive: true,
            verbose: false,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 10,
            workdir: None,
            command: Some("test command".to_string()),
            file: None,
            output_format: "text".to_string(),
            stream: false,
            no_stream: false,
            typing_speed: 30,
        };
        
        match cmd_single.determine_run_mode() {
            RunMode::SingleCommand(cmd) => assert_eq!(cmd, "test command"),
            _ => panic!("Expected SingleCommand mode"),
        }
        
        // 测试文件执行模式
        let cmd_file = RunCommand {
            config: None,
            name: "test".to_string(),
            interactive: true,
            verbose: false,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 10,
            workdir: None,
            command: None,
            file: Some("test.txt".to_string()),
            output_format: "text".to_string(),
            stream: false,
            no_stream: false,
            typing_speed: 30,
        };
        
        match cmd_file.determine_run_mode() {
            RunMode::FileExecution(file) => assert_eq!(file, "test.txt"),
            _ => panic!("Expected FileExecution mode"),
        }
        
        // 测试交互模式
        let cmd_interactive = RunCommand {
            config: None,
            name: "test".to_string(),
            interactive: true,
            verbose: false,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 10,
            workdir: None,
            command: None,
            file: None,
            output_format: "text".to_string(),
            stream: false,
            no_stream: false,
            typing_speed: 30,
        };
        
        match cmd_interactive.determine_run_mode() {
            RunMode::Interactive => {},
            _ => panic!("Expected Interactive mode"),
        }
        
        // 测试守护进程模式
        let cmd_daemon = RunCommand {
            config: None,
            name: "test".to_string(),
            interactive: false,
            verbose: false,
            log_level: "info".to_string(),
            sandbox: true,
            max_concurrent_tasks: 10,
            workdir: None,
            command: None,
            file: None,
            output_format: "text".to_string(),
            stream: false,
            no_stream: false,
            typing_speed: 30,
        };
        
        match cmd_daemon.determine_run_mode() {
            RunMode::Daemon => {},
            _ => panic!("Expected Daemon mode"),
        }
    }

    #[test]
    fn test_command_configuration() {
        let cmd = RunCommand {
            config: Some("custom.toml".to_string()),
            name: "custom-agent".to_string(),
            interactive: true,
            verbose: true,
            log_level: "debug".to_string(),
            sandbox: false,
            max_concurrent_tasks: 15,
            workdir: Some("/custom/workdir".to_string()),
            command: None,
            file: None,
            output_format: "json".to_string(),
            stream: true,
            no_stream: false,
            typing_speed: 50,
        };
        
        assert_eq!(cmd.config, Some("custom.toml".to_string()));
        assert_eq!(cmd.name, "custom-agent");
        assert!(cmd.interactive);
        assert!(cmd.verbose);
        assert_eq!(cmd.log_level, "debug");
        assert!(!cmd.sandbox);
        assert_eq!(cmd.max_concurrent_tasks, 15);
        assert_eq!(cmd.workdir, Some("/custom/workdir".to_string()));
        assert_eq!(cmd.output_format, "json");
        assert!(cmd.stream);
        assert_eq!(cmd.typing_speed, 50);
    }
}