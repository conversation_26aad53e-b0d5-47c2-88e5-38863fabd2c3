//! # 上下文索引管理模块
//!
//! 提供高效的上下文条目索引功能，包括会话索引、标签索引、类型索引等。
//! 支持快速检索和多维度查询优化。

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

/// 索引管理器
/// 
/// 负责维护各种类型的索引，提供高效的条目检索功能。
/// 支持会话、标签、类型等多维度索引。
pub struct IndexManager {
    /// 会话索引：会话ID -> 条目ID列表
    session_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,
    /// 标签索引：标签 -> 条目ID列表
    tag_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,
    /// 类型索引：条目类型 -> 条目ID列表
    type_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,
    /// 重要性索引：重要性级别 -> 条目ID列表
    importance_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,
}

impl IndexManager {
    /// 创建新的索引管理器
    /// 
    /// # 返回
    /// 
    /// 返回新的索引管理器实例
    pub fn new() -> Self {
        Self {
            session_index: Arc::new(RwLock::new(HashMap::new())),
            tag_index: Arc::new(RwLock::new(HashMap::new())),
            type_index: Arc::new(RwLock::new(HashMap::new())),
            importance_index: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 添加条目到索引
    /// 
    /// # 参数
    /// 
    /// * `entry_id` - 条目ID
    /// * `session_id` - 会话ID
    /// * `entry_type` - 条目类型字符串
    /// * `importance` - 重要性级别字符串
    /// * `tags` - 标签列表
    pub async fn add_entry(
        &self,
        entry_id: Uuid,
        session_id: &str,
        entry_type: &str,
        importance: &str,
        tags: &[String],
    ) {
        // 更新会话索引
        {
            let mut session_index = self.session_index.write().await;
            session_index
                .entry(session_id.to_string())
                .or_insert_with(Vec::new)
                .push(entry_id);
        }

        // 更新类型索引
        {
            let mut type_index = self.type_index.write().await;
            type_index
                .entry(entry_type.to_string())
                .or_insert_with(Vec::new)
                .push(entry_id);
        }

        // 更新重要性索引
        {
            let mut importance_index = self.importance_index.write().await;
            importance_index
                .entry(importance.to_string())
                .or_insert_with(Vec::new)
                .push(entry_id);
        }

        // 更新标签索引
        {
            let mut tag_index = self.tag_index.write().await;
            for tag in tags {
                tag_index
                    .entry(tag.clone())
                    .or_insert_with(Vec::new)
                    .push(entry_id);
            }
        }

        tracing::debug!("已将条目 {} 添加到索引", entry_id);
    }

    /// 从索引中移除条目
    /// 
    /// # 参数
    /// 
    /// * `entry_id` - 条目ID
    /// * `session_id` - 会话ID
    /// * `entry_type` - 条目类型字符串
    /// * `importance` - 重要性级别字符串
    /// * `tags` - 标签列表
    pub async fn remove_entry(
        &self,
        entry_id: Uuid,
        session_id: &str,
        entry_type: &str,
        importance: &str,
        tags: &[String],
    ) {
        // 从会话索引中移除
        {
            let mut session_index = self.session_index.write().await;
            if let Some(entries) = session_index.get_mut(session_id) {
                entries.retain(|&id| id != entry_id);
                if entries.is_empty() {
                    session_index.remove(session_id);
                }
            }
        }

        // 从类型索引中移除
        {
            let mut type_index = self.type_index.write().await;
            if let Some(entries) = type_index.get_mut(entry_type) {
                entries.retain(|&id| id != entry_id);
                if entries.is_empty() {
                    type_index.remove(entry_type);
                }
            }
        }

        // 从重要性索引中移除
        {
            let mut importance_index = self.importance_index.write().await;
            if let Some(entries) = importance_index.get_mut(importance) {
                entries.retain(|&id| id != entry_id);
                if entries.is_empty() {
                    importance_index.remove(importance);
                }
            }
        }

        // 从标签索引中移除
        {
            let mut tag_index = self.tag_index.write().await;
            for tag in tags {
                if let Some(entries) = tag_index.get_mut(tag) {
                    entries.retain(|&id| id != entry_id);
                    if entries.is_empty() {
                        tag_index.remove(tag);
                    }
                }
            }
        }

        tracing::debug!("已从索引中移除条目 {}", entry_id);
    }

    /// 根据会话ID查找条目
    /// 
    /// # 参数
    /// 
    /// * `session_id` - 会话ID
    /// 
    /// # 返回
    /// 
    /// 返回该会话的所有条目ID
    pub async fn find_by_session(&self, session_id: &str) -> Vec<Uuid> {
        let session_index = self.session_index.read().await;
        session_index
            .get(session_id)
            .map(|entries| entries.clone())
            .unwrap_or_default()
    }

    /// 根据标签查找条目
    /// 
    /// # 参数
    /// 
    /// * `tags` - 标签列表
    /// 
    /// # 返回
    /// 
    /// 返回包含任一指定标签的条目ID集合
    pub async fn find_by_tags(&self, tags: &[String]) -> Vec<Uuid> {
        let tag_index = self.tag_index.read().await;
        let mut result = Vec::new();
        let mut seen = std::collections::HashSet::new();

        for tag in tags {
            if let Some(entries) = tag_index.get(tag) {
                for &entry_id in entries {
                    if seen.insert(entry_id) {
                        result.push(entry_id);
                    }
                }
            }
        }

        result
    }

    /// 根据条目类型查找条目
    /// 
    /// # 参数
    /// 
    /// * `entry_types` - 条目类型列表
    /// 
    /// # 返回
    /// 
    /// 返回指定类型的条目ID集合
    pub async fn find_by_types(&self, entry_types: &[String]) -> Vec<Uuid> {
        let type_index = self.type_index.read().await;
        let mut result = Vec::new();
        let mut seen = std::collections::HashSet::new();

        for entry_type in entry_types {
            if let Some(entries) = type_index.get(entry_type) {
                for &entry_id in entries {
                    if seen.insert(entry_id) {
                        result.push(entry_id);
                    }
                }
            }
        }

        result
    }

    /// 根据重要性级别查找条目
    /// 
    /// # 参数
    /// 
    /// * `importance_levels` - 重要性级别列表
    /// 
    /// # 返回
    /// 
    /// 返回指定重要性级别的条目ID集合
    pub async fn find_by_importance(&self, importance_levels: &[String]) -> Vec<Uuid> {
        let importance_index = self.importance_index.read().await;
        let mut result = Vec::new();
        let mut seen = std::collections::HashSet::new();

        for importance in importance_levels {
            if let Some(entries) = importance_index.get(importance) {
                for &entry_id in entries {
                    if seen.insert(entry_id) {
                        result.push(entry_id);
                    }
                }
            }
        }

        result
    }

    /// 获取所有会话ID
    /// 
    /// # 返回
    /// 
    /// 返回所有活跃会话的ID列表
    pub async fn get_all_sessions(&self) -> Vec<String> {
        let session_index = self.session_index.read().await;
        session_index.keys().cloned().collect()
    }

    /// 获取所有标签
    /// 
    /// # 返回
    /// 
    /// 返回所有使用过的标签列表
    pub async fn get_all_tags(&self) -> Vec<String> {
        let tag_index = self.tag_index.read().await;
        tag_index.keys().cloned().collect()
    }

    /// 获取所有条目类型
    /// 
    /// # 返回
    /// 
    /// 返回所有使用过的条目类型列表
    pub async fn get_all_types(&self) -> Vec<String> {
        let type_index = self.type_index.read().await;
        type_index.keys().cloned().collect()
    }

    /// 获取索引统计信息
    /// 
    /// # 返回
    /// 
    /// 返回索引统计信息
    pub async fn get_index_stats(&self) -> IndexStats {
        let session_count = {
            let session_index = self.session_index.read().await;
            session_index.len()
        };

        let tag_count = {
            let tag_index = self.tag_index.read().await;
            tag_index.len()
        };

        let type_count = {
            let type_index = self.type_index.read().await;
            type_index.len()
        };

        let importance_count = {
            let importance_index = self.importance_index.read().await;
            importance_index.len()
        };

        IndexStats {
            session_count,
            tag_count,
            type_count,
            importance_count,
        }
    }

    /// 清理空的索引条目
    /// 
    /// 移除所有空的索引条目，释放内存
    pub async fn cleanup_empty_entries(&self) {
        // 清理会话索引
        {
            let mut session_index = self.session_index.write().await;
            session_index.retain(|_, entries| !entries.is_empty());
        }

        // 清理标签索引
        {
            let mut tag_index = self.tag_index.write().await;
            tag_index.retain(|_, entries| !entries.is_empty());
        }

        // 清理类型索引
        {
            let mut type_index = self.type_index.write().await;
            type_index.retain(|_, entries| !entries.is_empty());
        }

        // 清理重要性索引
        {
            let mut importance_index = self.importance_index.write().await;
            importance_index.retain(|_, entries| !entries.is_empty());
        }

        tracing::debug!("已清理空的索引条目");
    }

    /// 重建所有索引
    /// 
    /// 清空现有索引并重新构建，用于数据一致性恢复
    pub async fn rebuild_indexes(&self) {
        {
            let mut session_index = self.session_index.write().await;
            session_index.clear();
        }

        {
            let mut tag_index = self.tag_index.write().await;
            tag_index.clear();
        }

        {
            let mut type_index = self.type_index.write().await;
            type_index.clear();
        }

        {
            let mut importance_index = self.importance_index.write().await;
            importance_index.clear();
        }

        tracing::info!("已重建所有索引");
    }
}

impl Default for IndexManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 索引统计信息
/// 
/// 提供索引使用情况的统计数据
#[derive(Debug, Clone)]
pub struct IndexStats {
    /// 会话数量
    pub session_count: usize,
    /// 标签数量
    pub tag_count: usize,
    /// 条目类型数量
    pub type_count: usize,
    /// 重要性级别数量
    pub importance_count: usize,
}

impl IndexStats {
    /// 生成统计报告
    /// 
    /// # 返回
    /// 
    /// 返回格式化的统计报告字符串
    pub fn generate_report(&self) -> String {
        format!(
            "=== 索引统计报告 ===\n\
             活跃会话数: {}\n\
             使用的标签数: {}\n\
             条目类型数: {}\n\
             重要性级别数: {}",
            self.session_count, self.tag_count, self.type_count, self.importance_count
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_index_manager() {
        let manager = IndexManager::new();
        let entry_id = Uuid::new_v4();

        // 添加条目到索引
        manager
            .add_entry(
                entry_id,
                "test_session",
                "UserInput",
                "High",
                &["tag1".to_string(), "tag2".to_string()],
            )
            .await;

        // 测试会话查找
        let session_entries = manager.find_by_session("test_session").await;
        assert_eq!(session_entries.len(), 1);
        assert_eq!(session_entries[0], entry_id);

        // 测试标签查找
        let tag_entries = manager.find_by_tags(&["tag1".to_string()]).await;
        assert_eq!(tag_entries.len(), 1);
        assert_eq!(tag_entries[0], entry_id);

        // 测试类型查找
        let type_entries = manager.find_by_types(&["UserInput".to_string()]).await;
        assert_eq!(type_entries.len(), 1);
        assert_eq!(type_entries[0], entry_id);

        // 测试重要性查找
        let importance_entries = manager.find_by_importance(&["High".to_string()]).await;
        assert_eq!(importance_entries.len(), 1);
        assert_eq!(importance_entries[0], entry_id);

        // 移除条目
        manager
            .remove_entry(
                entry_id,
                "test_session",
                "UserInput",
                "High",
                &["tag1".to_string(), "tag2".to_string()],
            )
            .await;

        // 验证移除后的结果
        let session_entries = manager.find_by_session("test_session").await;
        assert_eq!(session_entries.len(), 0);
    }

    #[tokio::test]
    async fn test_index_stats() {
        let manager = IndexManager::new();
        let entry_id1 = Uuid::new_v4();
        let entry_id2 = Uuid::new_v4();

        manager
            .add_entry(entry_id1, "session1", "UserInput", "High", &["tag1".to_string()])
            .await;
        manager
            .add_entry(entry_id2, "session2", "AgentResponse", "Normal", &["tag2".to_string()])
            .await;

        let stats = manager.get_index_stats().await;
        assert_eq!(stats.session_count, 2);
        assert_eq!(stats.tag_count, 2);
        assert_eq!(stats.type_count, 2);
        assert_eq!(stats.importance_count, 2);

        let report = stats.generate_report();
        assert!(report.contains("活跃会话数: 2"));
        assert!(report.contains("使用的标签数: 2"));
    }
}
