//! # 工作流模块
//!
//! 提供完整的工作流管理和执行功能。
//! 
//! 这个模块包含了工作流系统的所有核心组件：
//! - 工作流定义和配置
//! - 工作流实例和执行上下文
//! - 工作流执行器和管理器
//! - 各种工作流相关的类型定义
//!
//! # 主要功能
//!
//! ## 工作流定义
//! - 支持多种步骤类型（工具调用、LLM调用、条件分支、循环等）
//! - 灵活的参数映射和数据流控制
//! - 丰富的配置选项（超时、重试、资源限制等）
//!
//! ## 工作流执行
//! - 异步执行引擎，支持并发控制
//! - 完善的错误处理和重试机制
//! - 实时状态监控和进度跟踪
//!
//! ## 工作流管理
//! - 工作流定义的注册和版本管理
//! - 工作流实例的创建和生命周期管理
//! - 执行历史和统计信息的记录
//!
//! # 使用示例
//!
//! ```rust,no_run
//! use orion_core::workflow::{
//!     WorkflowDefinition, WorkflowManager, DefaultWorkflowExecutor,
//!     StepType, WorkflowConfig
//! };
//! use orion_core::tools::ToolRegistry;
//! use orion_core::message::MessageBus;
//! use std::sync::Arc;
//! use std::collections::HashMap;
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! // 创建依赖组件
//! let tool_registry = Arc::new(ToolRegistry::new());
//! let message_bus = Arc::new(MessageBus::new());
//!
//! // 创建工作流执行器
//! let executor = Arc::new(DefaultWorkflowExecutor::new(
//!     tool_registry,
//!     message_bus,
//!     10 // 最大并发数
//! ));
//!
//! // 创建工作流管理器
//! let manager = WorkflowManager::new(executor);
//!
//! // 注册工作流定义（需要先创建定义）
//! // manager.register_workflow(workflow_definition).await?;
//!
//! // 创建并执行工作流实例
//! // let instance_id = manager.create_instance(
//! //     workflow_id,
//! //     "我的工作流实例".to_string(),
//! //     HashMap::new()
//! // ).await?;
//!
//! // manager.execute_instance(instance_id).await?;
//! # Ok(())
//! # }
//! ```

// 声明子模块
pub mod config;
pub mod definition;
pub mod execution;
pub mod executor;
pub mod instance;
pub mod manager;
pub mod types;

// 重新导出主要的公共接口
pub use types::*;

// 重新导出测试相关的内容（仅在测试时可用）
#[cfg(test)]
pub mod tests {
    use super::*;
    use crate::tools::ToolRegistry;
    use crate::message::MessageBus;
    use std::collections::HashMap;
    use std::sync::Arc;
    use std::time::SystemTime;
    use uuid::Uuid;

    /// 创建测试用的工作流定义
    pub fn create_test_workflow_definition() -> WorkflowDefinition {
        let workflow_id = Uuid::new_v4();
        let step_id = Uuid::new_v4();
        
        WorkflowDefinition {
            id: workflow_id,
            name: "测试工作流".to_string(),
            version: "1.0.0".to_string(),
            description: "测试工作流描述".to_string(),
            tags: vec!["test".to_string()],
            input_parameters: vec![],
            output_parameters: vec![],
            steps: vec![
                WorkflowStep {
                    id: step_id,
                    name: "测试步骤".to_string(),
                    step_type: StepType::ToolCall {
                        tool_name: "test_tool".to_string(),
                    },
                    description: "测试步骤描述".to_string(),
                    config: StepConfig::default(),
                    input_mapping: HashMap::new(),
                    output_mapping: HashMap::new(),
                    condition: None,
                    retry_config: None,
                    timeout_seconds: None,
                    next_steps: HashMap::new(),
                    error_handler: None,
                },
            ],
            initial_step_id: step_id,
            config: WorkflowConfig::default(),
            created_at: SystemTime::now(),
            updated_at: SystemTime::now(),
        }
    }

    /// 创建测试用的工作流管理器
    pub fn create_test_workflow_manager() -> WorkflowManager {
        let tool_registry = Arc::new(ToolRegistry::new());
        let message_bus = Arc::new(MessageBus::new());
        let executor = Arc::new(DefaultWorkflowExecutor::new(tool_registry, message_bus, 10));
        WorkflowManager::new(executor)
    }

    #[tokio::test]
    async fn test_workflow_definition_creation() {
        let definition = create_test_workflow_definition();
        assert_eq!(definition.name, "测试工作流");
        assert_eq!(definition.steps.len(), 1);
        assert!(!definition.steps.is_empty());
    }

    #[tokio::test]
    async fn test_workflow_manager_creation() {
        let manager = create_test_workflow_manager();
        
        // 测试工作流定义注册
        let definition = create_test_workflow_definition();
        let workflow_id = definition.id;
        
        manager.register_workflow(definition).await.unwrap();
        
        // 测试实例创建
        let instance_id = manager.create_instance(
            workflow_id,
            "测试实例".to_string(),
            HashMap::new(),
        ).await.unwrap();
        
        // 测试实例获取
        let instance = manager.get_instance(instance_id).await.unwrap();
        assert_eq!(instance.name, "测试实例");
        assert_eq!(instance.workflow_id, workflow_id);
        assert_eq!(instance.status, WorkflowStatus::Pending);
    }

    #[tokio::test]
    async fn test_workflow_config_defaults() {
        let config = WorkflowConfig::default();
        assert_eq!(config.max_execution_time_seconds, Some(3600));
        assert_eq!(config.max_concurrent_steps, Some(10));
        assert!(config.enable_state_persistence);
        assert!(config.enable_execution_logging);
        assert_eq!(config.error_handling_strategy, ErrorHandlingStrategy::StopImmediately);
        assert_eq!(config.variable_scope, VariableScope::Global);
    }

    #[tokio::test]
    async fn test_step_config_defaults() {
        let config = StepConfig::default();
        assert!(!config.skippable);
        assert!(!config.parallel);
        assert_eq!(config.priority, 0);
        assert!(config.resource_limits.is_none());
        assert!(config.custom_properties.is_empty());
    }
}
