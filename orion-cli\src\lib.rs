//! # Orion CLI 库
//!
//! Orion Agent 系统命令行工具的核心库，提供命令处理、错误管理等功能。
//!
//! ## 主要模块
//!
//! - [`commands`] - 命令处理模块，包含所有 CLI 子命令的实现
//! - [`error`] - 错误处理模块，提供统一的错误类型和处理机制
//!
//! ## 使用示例
//!
//! ```rust
//! use orion_cli::{commands::Commands, error::Result};
//! use clap::Parser;
//!
//! #[tokio::main]
//! async fn main() -> Result<()> {
//!     let command = Commands::parse();
//!     command.execute().await
//! }
//! ```

pub mod commands;
pub mod error;
pub mod ui;

// 重新导出常用类型
pub use error::{CliError, Result};
pub use commands::Commands;

/// CLI 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// CLI 名称
pub const NAME: &str = env!("CARGO_PKG_NAME");

/// CLI 描述
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// CLI 作者
pub const AUTHORS: &str = env!("CARGO_PKG_AUTHORS");

/// CLI 仓库地址
pub const REPOSITORY: &str = "https://github.com/orion-ai/orion";

/// 获取完整的版本信息
pub fn version_info() -> String {
    format!(
        "{} v{}\n编译时间: {}\n提交哈希: {}\nRust 版本: {}",
        NAME,
        VERSION,
        build_time(),
        git_hash(),
        rust_version()
    )
}

/// 获取构建时间
fn build_time() -> &'static str {
    env!("VERGEN_BUILD_TIMESTAMP")
}

/// 获取 Git 提交哈希
fn git_hash() -> &'static str {
    env!("VERGEN_GIT_SHA")
}

/// 获取 Rust 版本
fn rust_version() -> &'static str {
    env!("VERGEN_RUSTC_SEMVER")
}

/// 初始化 CLI 环境
pub fn init() -> Result<()> {
    // 设置 panic hook
    std::panic::set_hook(Box::new(|panic_info| {
        eprintln!("💥 程序发生严重错误:");
        eprintln!("{}", panic_info);
        eprintln!("\n请将此错误信息报告给开发团队: {}", REPOSITORY);
    }));
    
    Ok(())
}

/// 清理 CLI 环境
pub fn cleanup() {
    // 执行清理操作
    // 例如：清理临时文件、关闭连接等
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_version_constants() {
        assert!(!VERSION.is_empty());
        assert!(!NAME.is_empty());
        assert!(!DESCRIPTION.is_empty());
        assert!(!REPOSITORY.is_empty());
    }
    
    #[test]
    fn test_version_info() {
        let info = version_info();
        assert!(info.contains(VERSION));
        assert!(info.contains(NAME));
    }
    
    #[test]
    fn test_init() {
        let result = init();
        assert!(result.is_ok());
    }
    
    #[test]
    fn test_cleanup() {
        // 测试清理函数不会 panic
        cleanup();
    }
}