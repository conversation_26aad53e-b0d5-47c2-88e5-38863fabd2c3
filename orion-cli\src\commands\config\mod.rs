//! # 配置命令模块
//!
//! 实现 Orion 配置文件的管理功能，包括初始化、查看、设置和验证配置。

// 声明子模块
pub mod types;
pub mod utils;
pub mod init;
pub mod show;
pub mod set;
pub mod get;
pub mod validate;
pub mod reset;
pub mod export;
pub mod import;

// 重新导出主要类型
pub use types::*;

use crate::error::Result;

impl ConfigCommand {
    /// 执行配置命令
    pub async fn execute(&self) -> Result<()> {
        match &self.action {
            ConfigAction::Init(cmd) => cmd.execute().await,
            ConfigAction::Show(cmd) => cmd.execute().await,
            ConfigAction::Set(cmd) => cmd.execute().await,
            ConfigAction::Get(cmd) => cmd.execute().await,
            ConfigAction::Validate(cmd) => cmd.execute().await,
            ConfigAction::Reset(cmd) => cmd.execute().await,
            ConfigAction::Export(cmd) => cmd.execute().await,
            ConfigAction::Import(cmd) => cmd.execute().await,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;
    
    #[tokio::test]
    async fn test_config_command_creation() {
        let init_cmd = InitConfig {
            output: PathBuf::from("test.toml"),
            force: false,
            template: "default".to_string(),
            with_examples: false,
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Init(init_cmd),
        };
        
        // 确保命令创建成功
        match config_cmd.action {
            ConfigAction::Init(ref cmd) => {
                assert_eq!(cmd.template, "default");
                assert!(!cmd.force);
                assert!(!cmd.with_examples);
            }
            _ => panic!("Expected Init action"),
        }
    }
    
    #[tokio::test]
    async fn test_show_config_creation() {
        let show_cmd = ShowConfig {
            config: PathBuf::from("test.toml"),
            format: "json".to_string(),
            show_secrets: true,
            section: Some("api".to_string()),
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Show(show_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Show(ref cmd) => {
                assert_eq!(cmd.format, "json");
                assert!(cmd.show_secrets);
                assert_eq!(cmd.section, Some("api".to_string()));
            }
            _ => panic!("Expected Show action"),
        }
    }
    
    #[tokio::test]
    async fn test_set_config_creation() {
        let set_cmd = SetConfig {
            config: PathBuf::from("test.toml"),
            key: "agents.default_model".to_string(),
            value: "gpt-4".to_string(),
            value_type: "string".to_string(),
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Set(set_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Set(ref cmd) => {
                assert_eq!(cmd.key, "agents.default_model");
                assert_eq!(cmd.value, "gpt-4");
                assert_eq!(cmd.value_type, "string");
            }
            _ => panic!("Expected Set action"),
        }
    }
    
    #[tokio::test]
    async fn test_get_config_creation() {
        let get_cmd = GetConfig {
            config: PathBuf::from("test.toml"),
            key: "logging.level".to_string(),
            format: "value".to_string(),
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Get(get_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Get(ref cmd) => {
                assert_eq!(cmd.key, "logging.level");
                assert_eq!(cmd.format, "value");
            }
            _ => panic!("Expected Get action"),
        }
    }
    
    #[tokio::test]
    async fn test_validate_config_creation() {
        let validate_cmd = ValidateConfig {
            config: PathBuf::from("test.toml"),
            verbose: true,
            check_dependencies: false,
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Validate(validate_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Validate(ref cmd) => {
                assert!(cmd.verbose);
                assert!(!cmd.check_dependencies);
            }
            _ => panic!("Expected Validate action"),
        }
    }
    
    #[tokio::test]
    async fn test_reset_config_creation() {
        let reset_cmd = ResetConfig {
            config: PathBuf::from("test.toml"),
            section: Some("security".to_string()),
            yes: true,
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Reset(reset_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Reset(ref cmd) => {
                assert_eq!(cmd.section, Some("security".to_string()));
                assert!(cmd.yes);
            }
            _ => panic!("Expected Reset action"),
        }
    }
    
    #[tokio::test]
    async fn test_export_config_creation() {
        let export_cmd = ExportConfig {
            config: PathBuf::from("source.toml"),
            output: PathBuf::from("export.json"),
            format: "json".to_string(),
            include_secrets: false,
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Export(export_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Export(ref cmd) => {
                assert_eq!(cmd.output, PathBuf::from("export.json"));
                assert_eq!(cmd.format, "json");
                assert!(!cmd.include_secrets);
            }
            _ => panic!("Expected Export action"),
        }
    }
    
    #[tokio::test]
    async fn test_import_config_creation() {
        let import_cmd = ImportConfig {
            config: PathBuf::from("target.toml"),
            input: PathBuf::from("source.yaml"),
            merge: true,
            force: false,
        };
        
        let config_cmd = ConfigCommand {
            action: ConfigAction::Import(import_cmd),
        };
        
        match config_cmd.action {
            ConfigAction::Import(ref cmd) => {
                assert_eq!(cmd.input, PathBuf::from("source.yaml"));
                assert!(cmd.merge);
                assert!(!cmd.force);
            }
            _ => panic!("Expected Import action"),
        }
    }
}