//! # 上下文管理配置模块
//!
//! 提供上下文管理器的配置选项和压缩策略定义。
//! 支持灵活的配置管理和多种压缩策略。

use crate::context::types::ImportanceLevel;
use serde::{Deserialize, Serialize};
use std::time::Duration;

/// 上下文管理器配置
/// 
/// 定义上下文管理器的各种配置参数，包括容量限制、压缩策略、
/// 相关性计算等选项。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextManagerConfig {
    /// 最大条目数量，超过此数量将触发压缩
    pub max_entries: usize,
    /// 最大会话数量，超过此数量将清理旧会话
    pub max_sessions: usize,
    /// 默认相关性评分，用于新条目的初始评分
    pub default_relevance_score: f64,
    /// 自动压缩阈值，当使用率超过此值时触发自动压缩
    pub auto_compression_threshold: f64,
    /// 压缩策略，定义如何进行上下文压缩
    pub compression_strategy: CompressionStrategy,
    /// 是否启用智能相关性计算
    pub enable_smart_relevance: bool,
    /// 相关性衰减因子，用于时间衰减计算
    pub relevance_decay_factor: f64,
    /// 访问权重，访问时对相关性评分的增加量
    pub access_weight: f64,
}

impl Default for ContextManagerConfig {
    fn default() -> Self {
        Self {
            max_entries: 10000,
            max_sessions: 100,
            default_relevance_score: 0.5,
            auto_compression_threshold: 0.8,
            compression_strategy: CompressionStrategy::Smart {
                target_compression_ratio: 0.3,
            },
            enable_smart_relevance: true,
            relevance_decay_factor: 0.95,
            access_weight: 0.1,
        }
    }
}

impl ContextManagerConfig {
    /// 创建新的配置构建器
    /// 
    /// # 返回
    /// 
    /// 返回新的配置构建器实例
    pub fn builder() -> ContextManagerConfigBuilder {
        ContextManagerConfigBuilder::new()
    }

    /// 创建高性能配置
    /// 
    /// 适用于高并发、大容量的场景
    /// 
    /// # 返回
    /// 
    /// 返回高性能配置
    pub fn high_performance() -> Self {
        Self {
            max_entries: 50000,
            max_sessions: 500,
            auto_compression_threshold: 0.9,
            compression_strategy: CompressionStrategy::SizeBased {
                max_entries: 40000,
            },
            enable_smart_relevance: false, // 关闭智能计算以提高性能
            ..Default::default()
        }
    }

    /// 创建内存优化配置
    /// 
    /// 适用于内存受限的环境
    /// 
    /// # 返回
    /// 
    /// 返回内存优化配置
    pub fn memory_optimized() -> Self {
        Self {
            max_entries: 1000,
            max_sessions: 20,
            auto_compression_threshold: 0.6,
            compression_strategy: CompressionStrategy::Smart {
                target_compression_ratio: 0.5,
            },
            ..Default::default()
        }
    }

    /// 验证配置的有效性
    /// 
    /// # 返回
    /// 
    /// 如果配置有效返回Ok，否则返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.max_entries == 0 {
            return Err("最大条目数不能为0".to_string());
        }

        if self.max_sessions == 0 {
            return Err("最大会话数不能为0".to_string());
        }

        if !(0.0..=1.0).contains(&self.default_relevance_score) {
            return Err("默认相关性评分必须在0.0-1.0之间".to_string());
        }

        if !(0.0..=1.0).contains(&self.auto_compression_threshold) {
            return Err("自动压缩阈值必须在0.0-1.0之间".to_string());
        }

        if !(0.0..=1.0).contains(&self.relevance_decay_factor) {
            return Err("相关性衰减因子必须在0.0-1.0之间".to_string());
        }

        if self.access_weight < 0.0 {
            return Err("访问权重不能为负数".to_string());
        }

        Ok(())
    }
}

/// 上下文压缩策略
/// 
/// 定义不同的压缩策略，用于在上下文容量达到限制时
/// 决定哪些条目应该被删除。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CompressionStrategy {
    /// 基于时间的压缩
    /// 
    /// 删除超过指定保留时间的旧条目
    TimeBased {
        /// 保留时间，超过此时间的条目将被删除
        retention_duration: Duration,
    },
    /// 基于重要性的压缩
    /// 
    /// 删除低于指定重要性级别的条目
    ImportanceBased {
        /// 最小重要性级别，低于此级别的条目将被删除
        min_importance: ImportanceLevel,
    },
    /// 基于相关性的压缩
    /// 
    /// 删除低于指定相关性评分的条目
    RelevanceBased {
        /// 最小相关性评分，低于此评分的条目将被删除
        min_relevance: f64,
    },
    /// 基于大小的压缩
    /// 
    /// 保持固定数量的条目，删除相关性最低的条目
    SizeBased {
        /// 最大条目数，超过此数量时删除相关性最低的条目
        max_entries: usize,
    },
    /// 智能压缩
    /// 
    /// 综合考虑时间、重要性、相关性等因素进行压缩
    Smart {
        /// 目标压缩比例，压缩后保留的条目比例
        target_compression_ratio: f64,
    },
}

impl CompressionStrategy {
    /// 获取策略的描述
    /// 
    /// # 返回
    /// 
    /// 返回策略的文字描述
    pub fn description(&self) -> String {
        match self {
            CompressionStrategy::TimeBased { retention_duration } => {
                format!("基于时间压缩，保留{}秒内的条目", retention_duration.as_secs())
            }
            CompressionStrategy::ImportanceBased { min_importance } => {
                format!("基于重要性压缩，保留{:?}级别以上的条目", min_importance)
            }
            CompressionStrategy::RelevanceBased { min_relevance } => {
                format!("基于相关性压缩，保留相关性评分{}以上的条目", min_relevance)
            }
            CompressionStrategy::SizeBased { max_entries } => {
                format!("基于大小压缩，最多保留{}个条目", max_entries)
            }
            CompressionStrategy::Smart { target_compression_ratio } => {
                format!("智能压缩，目标保留比例{:.1}%", target_compression_ratio * 100.0)
            }
        }
    }

    /// 检查策略是否需要实时计算
    /// 
    /// # 返回
    /// 
    /// 如果策略需要实时计算返回true
    pub fn requires_realtime_calculation(&self) -> bool {
        matches!(self, CompressionStrategy::Smart { .. })
    }
}

/// 配置构建器
/// 
/// 提供流式API来构建上下文管理器配置
#[derive(Debug)]
pub struct ContextManagerConfigBuilder {
    config: ContextManagerConfig,
}

impl ContextManagerConfigBuilder {
    /// 创建新的配置构建器
    /// 
    /// # 返回
    /// 
    /// 返回新的配置构建器实例
    pub fn new() -> Self {
        Self {
            config: ContextManagerConfig::default(),
        }
    }

    /// 设置最大条目数
    /// 
    /// # 参数
    /// 
    /// * `max_entries` - 最大条目数
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn max_entries(mut self, max_entries: usize) -> Self {
        self.config.max_entries = max_entries;
        self
    }

    /// 设置最大会话数
    /// 
    /// # 参数
    /// 
    /// * `max_sessions` - 最大会话数
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn max_sessions(mut self, max_sessions: usize) -> Self {
        self.config.max_sessions = max_sessions;
        self
    }

    /// 设置默认相关性评分
    /// 
    /// # 参数
    /// 
    /// * `score` - 默认相关性评分
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn default_relevance_score(mut self, score: f64) -> Self {
        self.config.default_relevance_score = score;
        self
    }

    /// 设置自动压缩阈值
    /// 
    /// # 参数
    /// 
    /// * `threshold` - 自动压缩阈值
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn auto_compression_threshold(mut self, threshold: f64) -> Self {
        self.config.auto_compression_threshold = threshold;
        self
    }

    /// 设置压缩策略
    /// 
    /// # 参数
    /// 
    /// * `strategy` - 压缩策略
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn compression_strategy(mut self, strategy: CompressionStrategy) -> Self {
        self.config.compression_strategy = strategy;
        self
    }

    /// 设置是否启用智能相关性计算
    /// 
    /// # 参数
    /// 
    /// * `enable` - 是否启用
    /// 
    /// # 返回
    /// 
    /// 返回构建器自身，支持链式调用
    pub fn enable_smart_relevance(mut self, enable: bool) -> Self {
        self.config.enable_smart_relevance = enable;
        self
    }

    /// 构建配置
    /// 
    /// # 返回
    /// 
    /// 返回构建的配置，如果配置无效则panic
    pub fn build(self) -> ContextManagerConfig {
        if let Err(e) = self.config.validate() {
            panic!("配置无效: {}", e);
        }
        self.config
    }

    /// 尝试构建配置
    /// 
    /// # 返回
    /// 
    /// 返回构建结果，如果配置无效返回错误
    pub fn try_build(self) -> Result<ContextManagerConfig, String> {
        self.config.validate()?;
        Ok(self.config)
    }
}

impl Default for ContextManagerConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_builder() {
        let config = ContextManagerConfig::builder()
            .max_entries(5000)
            .max_sessions(50)
            .default_relevance_score(0.6)
            .auto_compression_threshold(0.7)
            .enable_smart_relevance(false)
            .build();

        assert_eq!(config.max_entries, 5000);
        assert_eq!(config.max_sessions, 50);
        assert_eq!(config.default_relevance_score, 0.6);
        assert_eq!(config.auto_compression_threshold, 0.7);
        assert!(!config.enable_smart_relevance);
    }

    #[test]
    fn test_preset_configs() {
        let high_perf = ContextManagerConfig::high_performance();
        assert_eq!(high_perf.max_entries, 50000);
        assert!(!high_perf.enable_smart_relevance);

        let memory_opt = ContextManagerConfig::memory_optimized();
        assert_eq!(memory_opt.max_entries, 1000);
        assert_eq!(memory_opt.auto_compression_threshold, 0.6);
    }

    #[test]
    fn test_config_validation() {
        let mut config = ContextManagerConfig::default();
        assert!(config.validate().is_ok());

        config.max_entries = 0;
        assert!(config.validate().is_err());

        config.max_entries = 1000;
        config.default_relevance_score = 1.5;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_compression_strategy_description() {
        let time_based = CompressionStrategy::TimeBased {
            retention_duration: Duration::from_secs(3600),
        };
        assert!(time_based.description().contains("3600"));

        let smart = CompressionStrategy::Smart {
            target_compression_ratio: 0.3,
        };
        assert!(smart.description().contains("30.0%"));
        assert!(smart.requires_realtime_calculation());
    }
}
