//! # OpenAI 提供商实现
//!
//! 实现 OpenAI API 的 LLM 提供商，支持 GPT 系列模型。

use crate::error::{OrionError, Result, LlmError};
use super::super::provider::LlmProvider;
use super::super::types::{
    LlmRequest, LlmResponse, StreamChunk, ModelConfig, MessageRole, 
    ToolCall, TokenUsage, FinishReason
};
use async_trait::async_trait;
use std::collections::HashMap;
use std::time::{Duration, SystemTime};

/// OpenAI 提供商实现
/// 
/// 支持 OpenAI 的 GPT 系列模型，包括 GPT-4、GPT-3.5-turbo 等。
pub struct OpenAiProvider {
    /// HTTP 客户端
    client: reqwest::Client,
    /// 模型配置
    config: ModelConfig,
}

impl OpenAiProvider {
    /// 创建新的 OpenAI 提供商
    /// 
    /// # 参数
    /// 
    /// * `config` - 模型配置，包含 API 密钥、端点等信息
    /// 
    /// # 返回
    /// 
    /// 返回配置好的 OpenAI 提供商实例
    pub fn new(config: ModelConfig) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(config.timeout_seconds))
            .build()
            .map_err(|e| OrionError::Llm(LlmError::engine_error(format!("创建 HTTP 客户端失败: {}", e))))?;
        
        Ok(Self { client, config })
    }
}

#[async_trait]
impl LlmProvider for OpenAiProvider {
    fn name(&self) -> &str {
        "openai"
    }
    
    async fn supported_models(&self) -> Result<Vec<String>> {
        Ok(vec![
            "gpt-4".to_string(),
            "gpt-4-turbo".to_string(),
            "gpt-3.5-turbo".to_string(),
            "gpt-4o".to_string(),
            "gpt-4o-mini".to_string(),
        ])
    }
    
    async fn complete(&self, request: LlmRequest) -> Result<LlmResponse> {
        // 构建 OpenAI API 请求
        let mut body = serde_json::json!({
            "model": self.config.name,
            "messages": request.messages.iter().map(|msg| {
                serde_json::json!({
                    "role": match msg.role {
                        MessageRole::System => "system",
                        MessageRole::User => "user",
                        MessageRole::Assistant => "assistant",
                        MessageRole::Tool => "tool",
                    },
                    "content": msg.content
                })
            }).collect::<Vec<_>>(),
        });
        
        // 添加模型参数
        let params = request.parameters.as_ref().unwrap_or(&self.config.parameters);
        body["temperature"] = serde_json::json!(params.temperature);
        body["max_tokens"] = serde_json::json!(params.max_tokens);
        
        if let Some(top_p) = params.top_p {
            body["top_p"] = serde_json::json!(top_p);
        }
        
        if !params.stop_sequences.is_empty() {
            body["stop"] = serde_json::json!(params.stop_sequences);
        }
        
        // 添加工具定义
        if !request.tools.is_empty() {
            body["tools"] = serde_json::json!(request.tools.iter().map(|tool| {
                serde_json::json!({
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.parameters
                    }
                })
            }).collect::<Vec<_>>());
        }
        
        // 发送请求
        let endpoint = self.config.endpoint.as_deref().unwrap_or("https://api.openai.com/v1/chat/completions");
        let api_key = self.config.api_key.as_ref()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("openai", "缺少 API 密钥")))?;
        
        let response = self.client
            .post(endpoint)
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await
            .map_err(|e| OrionError::Llm(LlmError::api_call_failed("openai", format!("请求失败: {}", e))))?;
        
        if !response.status().is_success() {
            let error_text = response.text().await.unwrap_or_else(|_| "未知错误".to_string());
            return Err(OrionError::Llm(LlmError::api_call_failed("openai", format!("API 错误: {}", error_text))));
        }
        
        let response_json: serde_json::Value = response.json().await
            .map_err(|e| OrionError::Llm(LlmError::api_call_failed("openai", format!("解析响应失败: {}", e))))?;
        
        // 解析响应
        let choice = response_json["choices"][0].as_object()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("openai", "无效的响应格式")))?;
        
        let message = choice["message"].as_object()
            .ok_or_else(|| OrionError::Llm(LlmError::api_call_failed("openai", "无效的消息格式")))?;
        
        let content = message["content"].as_str().unwrap_or("").to_string();
        
        // 解析工具调用
        let mut tool_calls = Vec::new();
        if let Some(calls) = message["tool_calls"].as_array() {
            for call in calls {
                if let (Some(id), Some(function)) = (call["id"].as_str(), call["function"].as_object()) {
                    if let Some(name) = function["name"].as_str() {
                        let arguments = function["arguments"].clone();
                        tool_calls.push(ToolCall {
                            id: id.to_string(),
                            name: name.to_string(),
                            arguments,
                        });
                    }
                }
            }
        }
        
        // 解析使用统计
        let usage = if let Some(usage_obj) = response_json["usage"].as_object() {
            TokenUsage {
                input_tokens: usage_obj["prompt_tokens"].as_u64().unwrap_or(0) as u32,
                output_tokens: usage_obj["completion_tokens"].as_u64().unwrap_or(0) as u32,
                total_tokens: usage_obj["total_tokens"].as_u64().unwrap_or(0) as u32,
            }
        } else {
            TokenUsage::default()
        };
        
        // 解析完成原因
        let finish_reason = match choice["finish_reason"].as_str() {
            Some("stop") => FinishReason::Stop,
            Some("length") => FinishReason::Length,
            Some("tool_calls") => FinishReason::ToolCalls,
            Some("content_filter") => FinishReason::ContentFilter,
            Some(other) => FinishReason::Error(format!("未知完成原因: {}", other)),
            None => FinishReason::Error("缺少完成原因".to_string()),
        };
        
        Ok(LlmResponse {
            request_id: request.id,
            content,
            tool_calls,
            usage,
            finish_reason,
            metadata: HashMap::new(),
            timestamp: SystemTime::now(),
        })
    }
    
    async fn stream(&self, _request: LlmRequest) -> Result<tokio::sync::mpsc::Receiver<Result<StreamChunk>>> {
        // TODO: 实现流式响应
        Err(OrionError::Llm(LlmError::engine_error("流式响应暂未实现")))
    }
    
    async fn validate_config(&self, config: &ModelConfig) -> Result<()> {
        if config.api_key.is_none() {
            return Err(OrionError::Llm(LlmError::api_call_failed("openai", "缺少 API 密钥")));
        }
        
        let supported = self.supported_models().await?;
        if !supported.contains(&config.name) {
            return Err(OrionError::Llm(LlmError::unsupported_model(format!(
                "{}，支持的模型: {:?}",
                config.name,
                supported
            ))));
        }
        
        Ok(())
    }
}
