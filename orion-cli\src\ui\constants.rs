//! # UI 常量
//!
//! 定义用户界面的常量配置。

/// 估计的艺术字宽度
pub const ESTIMATED_ART_WIDTH: usize = 60;

/// 边框宽度
pub const BOX_BORDER_WIDTH: usize = 1;

/// 边框内边距
pub const BOX_PADDING_X: usize = 1;

/// UI 总宽度 (基于艺术字宽度、内边距和边框计算)
pub const UI_WIDTH: usize = ESTIMATED_ART_WIDTH + BOX_PADDING_X * 2 + BOX_BORDER_WIDTH * 2;

/// 流式输出防抖延迟 (毫秒)
pub const STREAM_DEBOUNCE_MS: u64 = 100;

/// Shell 命令名称
pub const SHELL_COMMAND_NAME: &str = "Shell Command";

/// 最小终端宽度
pub const MIN_TERMINAL_WIDTH: u16 = 60;

/// 最小终端高度
pub const MIN_TERMINAL_HEIGHT: u16 = 20;

/// 默认提示符前缀
pub const DEFAULT_PROMPT_PREFIX: &str = "orion";

/// 会话 ID 显示长度
pub const SESSION_ID_DISPLAY_LENGTH: usize = 8;

/// 进度条字符
pub const PROGRESS_CHARS: &str = "█▉▊▋▌▍▎▏ ";

/// 加载动画字符
pub const LOADING_CHARS: &[char] = &['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];

/// 状态指示器
pub mod status_indicators {
    pub const SUCCESS: &str = "✅";
    pub const ERROR: &str = "❌";
    pub const WARNING: &str = "⚠️";
    pub const INFO: &str = "ℹ️";
    pub const LOADING: &str = "⏳";
    pub const ROCKET: &str = "🚀";
    pub const GEAR: &str = "⚙️";
    pub const LIGHTBULB: &str = "💡";
    pub const STOP: &str = "🛑";
    pub const WAVE: &str = "👋";
    pub const STAR: &str = "⭐";
    pub const FIRE: &str = "🔥";
    pub const ROBOT: &str = "🤖";
    pub const BRAIN: &str = "🧠";
    pub const SPARKLES: &str = "✨";
    pub const TARGET: &str = "🎯";
    pub const SHIELD: &str = "🛡️";
    pub const MAGIC_WAND: &str = "🪄";
    pub const TELESCOPE: &str = "🔭";
}

/// 边框样式
pub mod borders {
    pub const SINGLE: (&str, &str, &str, &str, &str, &str, &str, &str) =
        ("┌", "┐", "└", "┘", "─", "│", "├", "┤");
    
    pub const DOUBLE: (&str, &str, &str, &str, &str, &str, &str, &str) =
        ("╔", "╗", "╚", "╝", "═", "║", "╠", "╣");
    
    pub const ROUNDED: (&str, &str, &str, &str, &str, &str, &str, &str) =
        ("╭", "╮", "╰", "╯", "─", "│", "├", "┤");
        
    pub const THICK: (&str, &str, &str, &str, &str, &str, &str, &str) =
        ("┏", "┓", "┗", "┛", "━", "┃", "┣", "┫");
}

/// 颜色代码 (ANSI)
pub mod ansi_colors {
    pub const RESET: &str = "\x1b[0m";
    pub const BOLD: &str = "\x1b[1m";
    pub const DIM: &str = "\x1b[2m";
    pub const ITALIC: &str = "\x1b[3m";
    pub const UNDERLINE: &str = "\x1b[4m";
    
    // 前景色
    pub const BLACK: &str = "\x1b[30m";
    pub const RED: &str = "\x1b[31m";
    pub const GREEN: &str = "\x1b[32m";
    pub const YELLOW: &str = "\x1b[33m";
    pub const BLUE: &str = "\x1b[34m";
    pub const MAGENTA: &str = "\x1b[35m";
    pub const CYAN: &str = "\x1b[36m";
    pub const WHITE: &str = "\x1b[37m";
    
    // 亮色前景色
    pub const BRIGHT_BLACK: &str = "\x1b[90m";
    pub const BRIGHT_RED: &str = "\x1b[91m";
    pub const BRIGHT_GREEN: &str = "\x1b[92m";
    pub const BRIGHT_YELLOW: &str = "\x1b[93m";
    pub const BRIGHT_BLUE: &str = "\x1b[94m";
    pub const BRIGHT_MAGENTA: &str = "\x1b[95m";
    pub const BRIGHT_CYAN: &str = "\x1b[96m";
    pub const BRIGHT_WHITE: &str = "\x1b[97m";
}

/// 键盘快捷键
pub mod keybindings {
    pub const QUIT: &str = "Ctrl+C";
    pub const CLEAR: &str = "Ctrl+L";
    pub const HISTORY_UP: &str = "↑";
    pub const HISTORY_DOWN: &str = "↓";
    pub const AUTO_COMPLETE: &str = "Tab";
    pub const HELP: &str = "F1";
    pub const CANCEL: &str = "Esc";
}

/// 帮助信息
pub mod help_text {
    pub const QUICK_HELP: &str = r#"快速帮助:
  help     - 显示完整帮助
  exit     - 退出程序
  clear    - 清屏
  history  - 显示历史记录
  version  - 显示版本信息
  
提示: 使用 Tab 键自动补全命令"#;

    pub const FULL_HELP: &str = r#"Orion CLI 帮助

命令:
  help                 显示此帮助信息
  exit, quit           退出交互模式
  clear                清除屏幕
  history              显示命令历史
  version              显示版本信息
  status               显示系统状态
  config               配置管理
  agent <command>      Agent 相关操作
  workflow <command>   工作流操作
  tool <command>       工具管理

快捷键:
  Tab                  自动补全
  ↑/↓                 历史记录导航
  Ctrl+C               退出
  Ctrl+L               清屏
  Ctrl+R               搜索历史

示例:
  orion agent list
  orion workflow run my-workflow
  orion tool install my-tool

更多信息请访问: https://github.com/orion-ai/orion"#;
}