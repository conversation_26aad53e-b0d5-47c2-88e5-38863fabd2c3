@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

set ORION_ROOT=%~dp0..
set CACHE_DIR=%ORION_ROOT%\.dev-cache
set BINARY_PATH=%ORION_ROOT%\target\debug\orion.exe
set LAST_BUILD_FILE=%CACHE_DIR%\last_build_hash

REM Create cache directory
if not exist "%CACHE_DIR%" mkdir "%CACHE_DIR%"

REM Check if rebuild is needed
call :need_rebuild
if %errorlevel% equ 1 (
    call :build_project
    if !errorlevel! neq 0 exit /b 1
)

REM Execute orion command with proper working directory
pushd "%ORION_ROOT%"
"%BINARY_PATH%" %*
set exit_code=%errorlevel%
popd
exit /b %exit_code%

:need_rebuild
REM Check if binary exists
if not exist "%BINARY_PATH%" exit /b 1

REM Calculate source hash
set source_hash=
for /r "%ORION_ROOT%\orion-cli\src" %%f in (*.rs) do call :append_hash "%%f"
for /r "%ORION_ROOT%\orion-core\src" %%f in (*.rs) do call :append_hash "%%f"
call :append_hash "%ORION_ROOT%\Cargo.toml"
call :append_hash "%ORION_ROOT%\orion-cli\Cargo.toml"
call :append_hash "%ORION_ROOT%\orion-core\Cargo.toml"

REM Read last build hash
set last_hash=
if exist "%LAST_BUILD_FILE%" (
    set /p last_hash=<"%LAST_BUILD_FILE%"
)

REM Compare hashes
if "%source_hash%" neq "%last_hash%" exit /b 1
exit /b 0

:append_hash
if exist "%~1" (
    for %%i in ("%~1") do (
        set file_size=%%~zi
        set file_time=%%~ti
        set source_hash=!source_hash!!file_size!!file_time!
    )
)
exit /b 0

:build_project
echo Detecting code changes, recompiling...
cd /d "%ORION_ROOT%"

cargo build --bin orion
if %errorlevel% equ 0 (
    echo %source_hash% > "%LAST_BUILD_FILE%"
    echo Compilation completed successfully
    exit /b 0
) else (
    echo Compilation failed
    exit /b 1
)