//! # 安全控制模块
//!
//! 提供沙箱环境的安全控制功能，包括网络访问控制、文件系统访问控制、
//! 命令白名单/黑名单、资源限制等安全机制。

use super::types::{SandboxConfig, NetworkAccess, FilesystemAccess, ScriptType};
use crate::error::{OrionError, Result};
use std::collections::HashSet;
use std::path::PathBuf;
use tokio::process::Command;
use tracing;

/// 安全管理器
/// 
/// 负责实施各种安全策略和访问控制
pub struct SecurityManager {
    /// 允许的命令白名单
    allowed_commands: HashSet<String>,
    /// 禁止的命令黑名单
    blocked_commands: HashSet<String>,
    /// 危险脚本模式检测
    dangerous_patterns: Vec<String>,
}

impl SecurityManager {
    /// 创建新的安全管理器
    pub fn new() -> Self {
        let mut manager = Self {
            allowed_commands: HashSet::new(),
            blocked_commands: HashSet::new(),
            dangerous_patterns: Vec::new(),
        };

        manager.initialize_default_policies();
        manager
    }

    /// 初始化默认安全策略
    fn initialize_default_policies(&mut self) {
        // 默认允许的安全命令
        let safe_commands = vec![
            "echo", "cat", "ls", "dir", "pwd", "whoami", "date", "time",
            "python", "python3", "node", "bash", "sh", "powershell",
            "grep", "find", "sort", "head", "tail", "wc", "cut",
        ];

        for cmd in safe_commands {
            self.allowed_commands.insert(cmd.to_string());
        }

        // 默认禁止的危险命令
        let dangerous_commands = vec![
            "rm", "del", "rmdir", "format", "fdisk", "mkfs",
            "sudo", "su", "chmod", "chown", "passwd",
            "wget", "curl", "nc", "netcat", "telnet", "ssh",
            "systemctl", "service", "kill", "killall", "pkill",
            "mount", "umount", "dd", "crontab",
        ];

        for cmd in dangerous_commands {
            self.blocked_commands.insert(cmd.to_string());
        }

        // 危险脚本模式
        self.dangerous_patterns = vec![
            "rm -rf".to_string(),
            "del /s".to_string(),
            "format c:".to_string(),
            "sudo".to_string(),
            "passwd".to_string(),
            "chmod 777".to_string(),
            "wget".to_string(),
            "curl".to_string(),
            "eval(".to_string(),
            "exec(".to_string(),
            "__import__".to_string(),
            "subprocess".to_string(),
            "os.system".to_string(),
            "shell_exec".to_string(),
            "system(".to_string(),
        ];
    }

    /// 检查命令是否被允许执行
    pub fn is_command_allowed(&self, command: &str, config: &SandboxConfig) -> bool {
        // 首先检查黑名单
        if self.blocked_commands.contains(command) {
            tracing::warn!("命令被黑名单阻止: {}", command);
            return false;
        }

        // 如果白名单不为空，检查白名单
        if !self.allowed_commands.is_empty() && !self.allowed_commands.contains(command) {
            tracing::warn!("命令不在白名单中: {}", command);
            return false;
        }

        // 检查网络相关命令
        if self.is_network_command(command) && !self.is_network_access_allowed(config) {
            tracing::warn!("网络命令被访问控制阻止: {}", command);
            return false;
        }

        true
    }

    /// 验证脚本内容的安全性
    pub fn validate_script_security(&self, script_content: &str, script_type: ScriptType) -> Result<()> {
        // 检查危险模式
        for pattern in &self.dangerous_patterns {
            if script_content.contains(pattern) {
                return Err(OrionError::SecurityError(
                    format!("脚本包含危险模式: {}", pattern)
                ));
            }
        }

        // 根据脚本类型进行特定检查
        match script_type {
            ScriptType::Python => self.validate_python_script(script_content)?,
            ScriptType::JavaScript => self.validate_javascript_script(script_content)?,
            ScriptType::Shell => self.validate_shell_script(script_content)?,
            ScriptType::PowerShell => self.validate_powershell_script(script_content)?,
        }

        Ok(())
    }

    /// 验证 Python 脚本安全性
    fn validate_python_script(&self, script_content: &str) -> Result<()> {
        let dangerous_imports = vec![
            "import os", "import subprocess", "import sys", "import socket",
            "from os", "from subprocess", "from sys", "from socket",
        ];

        for import in dangerous_imports {
            if script_content.contains(import) {
                return Err(OrionError::SecurityError(
                    format!("Python脚本包含危险导入: {}", import)
                ));
            }
        }

        Ok(())
    }

    /// 验证 JavaScript 脚本安全性
    fn validate_javascript_script(&self, script_content: &str) -> Result<()> {
        let dangerous_functions = vec![
            "require('fs')", "require('child_process')", "require('net')",
            "eval(", "Function(", "setTimeout(", "setInterval(",
        ];

        for func in dangerous_functions {
            if script_content.contains(func) {
                return Err(OrionError::SecurityError(
                    format!("JavaScript脚本包含危险函数: {}", func)
                ));
            }
        }

        Ok(())
    }

    /// 验证 Shell 脚本安全性
    fn validate_shell_script(&self, script_content: &str) -> Result<()> {
        let dangerous_commands = vec![
            "rm ", "rmdir ", "del ", "format ", "fdisk ",
            "sudo ", "su ", "chmod ", "chown ",
            "wget ", "curl ", "nc ", "netcat ",
        ];

        for cmd in dangerous_commands {
            if script_content.contains(cmd) {
                return Err(OrionError::SecurityError(
                    format!("Shell脚本包含危险命令: {}", cmd.trim())
                ));
            }
        }

        Ok(())
    }

    /// 验证 PowerShell 脚本安全性
    fn validate_powershell_script(&self, script_content: &str) -> Result<()> {
        let dangerous_cmdlets = vec![
            "Remove-Item", "Remove-Computer", "Format-Volume",
            "Invoke-Expression", "Invoke-Command", "Start-Process",
            "New-Object System.Net", "DownloadFile", "WebClient",
        ];

        for cmdlet in dangerous_cmdlets {
            if script_content.contains(cmdlet) {
                return Err(OrionError::SecurityError(
                    format!("PowerShell脚本包含危险Cmdlet: {}", cmdlet)
                ));
            }
        }

        Ok(())
    }

    /// 应用安全限制到命令
    pub fn apply_security_limits(&self, cmd: &mut Command, config: &SandboxConfig) -> Result<()> {
        // 在 Windows 上应用资源限制
        #[cfg(target_os = "windows")]
        {
            self.apply_windows_limits(cmd, config)?;
        }

        // 在 Unix 系统上应用资源限制
        #[cfg(unix)]
        {
            self.apply_unix_limits(cmd, config)?;
        }

        Ok(())
    }

    /// 在 Windows 上应用资源限制
    #[cfg(target_os = "windows")]
    fn apply_windows_limits(&self, _cmd: &mut Command, config: &SandboxConfig) -> Result<()> {
        // Windows 上的资源限制需要使用 Job Objects
        // 这里提供一个简化的实现
        tracing::warn!("Windows 平台的资源限制功能有限，配置: {:?}", config);
        Ok(())
    }

    /// 在 Unix 系统上应用资源限制
    #[cfg(unix)]
    fn apply_unix_limits(&self, cmd: &mut Command, config: &SandboxConfig) -> Result<()> {
        use std::os::unix::process::CommandExt;

        let max_memory_mb = config.max_memory_mb;
        let timeout_seconds = config.timeout_seconds;

        cmd.pre_exec(move || {
            // 设置内存限制（通过 ulimit）
            unsafe {
                let memory_limit = (max_memory_mb * 1024 * 1024) as u64;
                libc::setrlimit(
                    libc::RLIMIT_AS,
                    &libc::rlimit {
                        rlim_cur: memory_limit,
                        rlim_max: memory_limit,
                    },
                );

                // 设置 CPU 时间限制
                let cpu_limit = timeout_seconds;
                libc::setrlimit(
                    libc::RLIMIT_CPU,
                    &libc::rlimit {
                        rlim_cur: cpu_limit,
                        rlim_max: cpu_limit,
                    },
                );
            }

            Ok(())
        });

        Ok(())
    }

    /// 检查是否为网络相关命令
    fn is_network_command(&self, command: &str) -> bool {
        let network_commands = vec![
            "wget", "curl", "nc", "netcat", "telnet", "ssh", "scp", "rsync",
            "ping", "nslookup", "dig", "host", "traceroute",
        ];

        network_commands.contains(&command)
    }

    /// 检查网络访问是否被允许
    fn is_network_access_allowed(&self, config: &SandboxConfig) -> bool {
        !matches!(config.network_access, NetworkAccess::Denied)
    }

    /// 验证文件路径访问权限
    pub fn validate_file_access(&self, path: &PathBuf, is_write: bool, config: &SandboxConfig) -> Result<()> {
        match &config.filesystem_access {
            FilesystemAccess::ReadOnly(allowed_paths) => {
                if is_write {
                    return Err(OrionError::SecurityError(
                        "只读模式下不允许写入操作".to_string()
                    ));
                }
                if !self.is_path_allowed(path, allowed_paths) {
                    return Err(OrionError::SecurityError(
                        format!("路径不在允许的读取列表中: {:?}", path)
                    ));
                }
            }
            FilesystemAccess::ReadWrite(allowed_paths) => {
                if !self.is_path_allowed(path, allowed_paths) {
                    return Err(OrionError::SecurityError(
                        format!("路径不在允许的访问列表中: {:?}", path)
                    ));
                }
            }
            FilesystemAccess::Restricted(allowed_paths) => {
                if !self.is_path_allowed(path, allowed_paths) {
                    return Err(OrionError::SecurityError(
                        format!("路径被访问限制阻止: {:?}", path)
                    ));
                }
            }
        }

        Ok(())
    }

    /// 检查路径是否在允许列表中
    fn is_path_allowed(&self, path: &PathBuf, allowed_paths: &[PathBuf]) -> bool {
        allowed_paths.iter().any(|allowed_path| {
            path.starts_with(allowed_path)
        })
    }

    /// 验证网络地址访问权限
    pub fn validate_network_access(&self, address: &str, config: &SandboxConfig) -> Result<()> {
        match &config.network_access {
            NetworkAccess::Denied => {
                Err(OrionError::SecurityError(
                    "网络访问被完全禁止".to_string()
                ))
            }
            NetworkAccess::Full => Ok(()),
            NetworkAccess::Restricted(allowed_addresses) => {
                if allowed_addresses.iter().any(|addr| address.contains(addr)) {
                    Ok(())
                } else {
                    Err(OrionError::SecurityError(
                        format!("地址不在允许的网络访问列表中: {}", address)
                    ))
                }
            }
        }
    }

    /// 添加允许的命令
    pub fn add_allowed_command(&mut self, command: String) {
        self.allowed_commands.insert(command);
    }

    /// 添加禁止的命令
    pub fn add_blocked_command(&mut self, command: String) {
        self.blocked_commands.insert(command);
    }

    /// 添加危险模式
    pub fn add_dangerous_pattern(&mut self, pattern: String) {
        self.dangerous_patterns.push(pattern);
    }

    /// 获取安全策略摘要
    pub fn get_security_summary(&self) -> SecuritySummary {
        SecuritySummary {
            allowed_commands_count: self.allowed_commands.len(),
            blocked_commands_count: self.blocked_commands.len(),
            dangerous_patterns_count: self.dangerous_patterns.len(),
        }
    }
}

impl Default for SecurityManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 安全策略摘要
/// 
/// 提供当前安全策略的概览信息
#[derive(Debug, Clone)]
pub struct SecuritySummary {
    /// 允许的命令数量
    pub allowed_commands_count: usize,
    /// 禁止的命令数量
    pub blocked_commands_count: usize,
    /// 危险模式数量
    pub dangerous_patterns_count: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::sandbox::config::ConfigPresets;

    #[test]
    fn test_security_manager_creation() {
        let manager = SecurityManager::new();
        assert!(!manager.allowed_commands.is_empty());
        assert!(!manager.blocked_commands.is_empty());
        assert!(!manager.dangerous_patterns.is_empty());
    }

    #[test]
    fn test_command_validation() {
        let manager = SecurityManager::new();
        let config = ConfigPresets::default();

        // 测试允许的命令
        assert!(manager.is_command_allowed("echo", &config));
        assert!(manager.is_command_allowed("python", &config));

        // 测试禁止的命令
        assert!(!manager.is_command_allowed("rm", &config));
        assert!(!manager.is_command_allowed("sudo", &config));
    }

    #[test]
    fn test_script_validation() {
        let manager = SecurityManager::new();

        // 测试安全的 Python 脚本
        let safe_script = "print('Hello, World!')";
        assert!(manager.validate_script_security(safe_script, ScriptType::Python).is_ok());

        // 测试危险的 Python 脚本
        let dangerous_script = "import os\nos.system('rm -rf /')";
        assert!(manager.validate_script_security(dangerous_script, ScriptType::Python).is_err());
    }

    #[test]
    fn test_file_access_validation() {
        let manager = SecurityManager::new();
        let config = ConfigPresets::strict_security();

        let test_path = PathBuf::from("/tmp/test.txt");
        
        // 在严格安全模式下，应该拒绝访问
        assert!(manager.validate_file_access(&test_path, false, &config).is_err());
        assert!(manager.validate_file_access(&test_path, true, &config).is_err());
    }

    #[test]
    fn test_network_access_validation() {
        let manager = SecurityManager::new();
        
        // 测试完全禁止网络访问
        let denied_config = ConfigPresets::strict_security();
        assert!(manager.validate_network_access("example.com", &denied_config).is_err());

        // 测试允许网络访问
        let allowed_config = ConfigPresets::development();
        assert!(manager.validate_network_access("example.com", &allowed_config).is_ok());
    }
}
