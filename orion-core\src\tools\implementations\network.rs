//! # 网络工具实现
//!
//! 提供 HTTP 请求等网络操作工具。

use crate::error::{OrionError, Result};
use crate::tools::{
    Tool, ToolDefinition, ToolParameter, ParameterType, ParameterConstraints,
    ToolRequest, ToolResult, ToolExample
};
use async_trait::async_trait;
use std::collections::HashMap;
use url::Url;

/// HTTP 请求工具
/// 
/// 提供安全的 HTTP 请求功能，支持多种 HTTP 方法和自定义请求头。
pub struct HttpRequestTool {
    /// HTTP 客户端
    client: reqwest::Client,
}

impl HttpRequestTool {
    /// 创建新的 HTTP 请求工具实例
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::builder()
                .timeout(std::time::Duration::from_secs(30))
                .user_agent("Orion-Tools/1.0")
                .build()
                .unwrap_or_else(|_| reqwest::Client::new()),
        }
    }
    
    /// 使用自定义客户端创建 HTTP 请求工具
    pub fn with_client(client: reqwest::Client) -> Self {
        Self { client }
    }
}

#[async_trait]
impl Tool for HttpRequestTool {
    fn definition(&self) -> ToolDefinition {
        ToolDefinition {
            name: "http_request".to_string(),
            version: "1.0.0".to_string(),
            description: "发送 HTTP 请求，支持多种方法和自定义请求头".to_string(),
            category: "network".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "url".to_string(),
                    param_type: ParameterType::Url,
                    description: "请求的 URL 地址".to_string(),
                    required: true,
                    default_value: None,
                    constraints: None,
                },
                ToolParameter {
                    name: "method".to_string(),
                    param_type: ParameterType::String,
                    description: "HTTP 请求方法".to_string(),
                    required: false,
                    default_value: Some(serde_json::Value::String("GET".to_string())),
                    constraints: Some(ParameterConstraints {
                        enum_values: Some(vec![
                            serde_json::Value::String("GET".to_string()),
                            serde_json::Value::String("POST".to_string()),
                            serde_json::Value::String("PUT".to_string()),
                            serde_json::Value::String("DELETE".to_string()),
                            serde_json::Value::String("PATCH".to_string()),
                            serde_json::Value::String("HEAD".to_string()),
                            serde_json::Value::String("OPTIONS".to_string()),
                        ]),
                        ..Default::default()
                    }),
                },
                ToolParameter {
                    name: "headers".to_string(),
                    param_type: ParameterType::Object,
                    description: "请求头映射".to_string(),
                    required: false,
                    default_value: None,
                    constraints: None,
                },
                ToolParameter {
                    name: "body".to_string(),
                    param_type: ParameterType::String,
                    description: "请求体内容".to_string(),
                    required: false,
                    default_value: None,
                    constraints: None,
                },
                ToolParameter {
                    name: "timeout".to_string(),
                    param_type: ParameterType::Integer,
                    description: "请求超时时间（秒）".to_string(),
                    required: false,
                    default_value: Some(serde_json::Value::Number(serde_json::Number::from(30))),
                    constraints: Some(ParameterConstraints {
                        min_value: Some(1.0),
                        max_value: Some(300.0),
                        ..Default::default()
                    }),
                },
            ],
            return_type: ParameterType::Object,
            requires_sandbox: false,
            tags: vec!["http".to_string(), "network".to_string(), "request".to_string()],
            examples: vec![
                ToolExample {
                    description: "发送 GET 请求".to_string(),
                    input: {
                        let mut map = HashMap::new();
                        map.insert("url".to_string(), serde_json::Value::String("https://api.example.com/data".to_string()));
                        map.insert("method".to_string(), serde_json::Value::String("GET".to_string()));
                        map
                    },
                    expected_output: serde_json::json!({
                        "status": 200,
                        "headers": {},
                        "body": "response data"
                    }),
                },
                ToolExample {
                    description: "发送 POST 请求".to_string(),
                    input: {
                        let mut map = HashMap::new();
                        map.insert("url".to_string(), serde_json::Value::String("https://api.example.com/submit".to_string()));
                        map.insert("method".to_string(), serde_json::Value::String("POST".to_string()));
                        map.insert("body".to_string(), serde_json::Value::String(r#"{"key": "value"}"#.to_string()));
                        map.insert("headers".to_string(), serde_json::json!({"Content-Type": "application/json"}));
                        map
                    },
                    expected_output: serde_json::json!({
                        "status": 201,
                        "headers": {},
                        "body": "created"
                    }),
                },
            ],
        }
    }
    
    async fn execute(&self, request: ToolRequest) -> Result<ToolResult> {
        let start_time = std::time::Instant::now();
        
        // 验证参数
        self.validate_parameters(&request.parameters)?;
        
        // 获取参数
        let url = request.parameters.get("url")
            .and_then(|v| v.as_str())
            .ok_or_else(|| OrionError::ToolError("http_request: 缺少 url 参数".to_string()))?;
        
        let method = request.parameters.get("method")
            .and_then(|v| v.as_str())
            .unwrap_or("GET");
        
        let timeout = request.parameters.get("timeout")
            .and_then(|v| v.as_i64())
            .unwrap_or(30) as u64;
        
        // 安全检查：验证 URL 格式
        let parsed_url = Url::parse(url).map_err(|e| {
            OrionError::ToolError(format!("无效的 URL 格式: {}", e))
        })?;
        
        // 安全检查：只允许 HTTP 和 HTTPS 协议
        if !matches!(parsed_url.scheme(), "http" | "https") {
            return Ok(ToolResult::failure(
                request.id,
                format!("不支持的协议: {}", parsed_url.scheme()),
                start_time.elapsed().as_millis() as u64,
            ));
        }
        
        // 构建请求
        let mut req_builder = match method.to_uppercase().as_str() {
            "GET" => self.client.get(url),
            "POST" => self.client.post(url),
            "PUT" => self.client.put(url),
            "DELETE" => self.client.delete(url),
            "PATCH" => self.client.patch(url),
            "HEAD" => self.client.head(url),
            "OPTIONS" => self.client.request(reqwest::Method::OPTIONS, url),
            _ => {
                return Ok(ToolResult::failure(
                    request.id,
                    format!("不支持的 HTTP 方法: {}", method),
                    start_time.elapsed().as_millis() as u64,
                ));
            }
        };
        
        // 设置超时
        req_builder = req_builder.timeout(std::time::Duration::from_secs(timeout));
        
        // 添加请求头
        if let Some(headers) = request.parameters.get("headers") {
            if let Some(headers_obj) = headers.as_object() {
                for (key, value) in headers_obj {
                    if let Some(value_str) = value.as_str() {
                        req_builder = req_builder.header(key, value_str);
                    }
                }
            }
        }
        
        // 添加请求体
        if let Some(body) = request.parameters.get("body") {
            if let Some(body_str) = body.as_str() {
                req_builder = req_builder.body(body_str.to_string());
            }
        }
        
        // 发送请求
        tracing::info!("发送 HTTP 请求: {} {}", method, url);
        
        match req_builder.send().await {
            Ok(response) => {
                let status = response.status().as_u16();
                let headers: HashMap<String, String> = response.headers()
                    .iter()
                    .map(|(k, v)| (k.to_string(), v.to_str().unwrap_or("").to_string()))
                    .collect();
                
                match response.text().await {
                    Ok(body) => {
                        let result_data = serde_json::json!({
                            "status": status,
                            "headers": headers,
                            "body": body
                        });
                        
                        tracing::info!("HTTP 请求成功: {} - 状态码 {}", url, status);
                        
                        Ok(ToolResult::success(
                            request.id,
                            result_data,
                            start_time.elapsed().as_millis() as u64,
                        ))
                    }
                    Err(e) => {
                        tracing::error!("读取响应体失败: {} - {}", url, e);
                        Ok(ToolResult::failure(
                            request.id,
                            format!("读取响应体失败: {}", e),
                            start_time.elapsed().as_millis() as u64,
                        ))
                    }
                }
            }
            Err(e) => {
                tracing::error!("HTTP 请求失败: {} - {}", url, e);
                Ok(ToolResult::failure(
                    request.id,
                    format!("HTTP 请求失败: {}", e),
                    start_time.elapsed().as_millis() as u64,
                ))
            }
        }
    }
}

impl Default for HttpRequestTool {
    fn default() -> Self {
        Self::new()
    }
}
