//! # 导出配置功能
//!
//! 实现配置文件的导出功能。

use crate::error::{CliError, Result};
use crate::commands::config::{types::ExportConfig, utils::SecretKeyChecker};
use orion_core::config::OrionConfig;
use std::fs;

impl SecretKeyChecker for ExportConfig {}

impl ExportConfig {
    /// 执行导出配置
    pub async fn execute(&self) -> Result<()> {
        // 加载配置
        let config = OrionConfig::from_file(&self.config)
            .map_err(|e| CliError::ConfigError {
                error: format!("加载配置文件失败: {}", e),
            })?;
        
        // 根据格式导出
        let content = match self.format.as_str() {
            "json" => {
                let mut config_value = serde_json::to_value(&config)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化配置失败: {}", e),
                    })?;
                
                // 隐藏敏感信息
                if !self.include_secrets {
                    self.hide_secrets(&mut config_value);
                }
                
                serde_json::to_string_pretty(&config_value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化 JSON 失败: {}", e),
                    })?
            }
            "yaml" => {
                let mut config_value = serde_yaml::to_value(&config)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化配置失败: {}", e),
                    })?;
                
                // 隐藏敏感信息
                if !self.include_secrets {
                    self.hide_secrets_yaml(&mut config_value);
                }
                
                serde_yaml::to_string(&config_value)
                    .map_err(|e| CliError::SerializationError {
                        error: format!("序列化 YAML 失败: {}", e),
                    })?
            }
            "toml" | _ => {
                if self.include_secrets {
                    toml::to_string_pretty(&config)
                        .map_err(|e| CliError::SerializationError {
                            error: format!("序列化 TOML 失败: {}", e),
                        })?
                } else {
                    let content = fs::read_to_string(&self.config)
                        .map_err(|e| CliError::IoError {
                            error: format!("读取配置文件失败: {}", e),
                        })?;
                    self.hide_secrets_toml(&content)
                }
            }
        };
        
        // 创建输出目录（如果需要）
        if let Some(parent) = self.output.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| CliError::IoError {
                    error: format!("创建目录失败: {}", e),
                })?;
        }
        
        // 写入文件
        fs::write(&self.output, content)
            .map_err(|e| CliError::IoError {
                error: format!("写入文件失败: {}", e),
            })?;
        
        println!("✅ 配置已导出到: {}", self.output.display());
        println!("📝 格式: {}", self.format);
        
        if !self.include_secrets {
            println!("🔒 敏感信息已隐藏");
        }
        
        Ok(())
    }
}