//! # 工作流定义模块
//!
//! 包含工作流定义相关的数据结构和类型定义。
//! 定义了工作流的基本结构、参数、步骤类型等核心概念。

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::SystemTime;
use uuid::Uuid;

use super::config::WorkflowConfig;

/// 工作流定义
/// 
/// 描述一个完整的工作流模板，包含所有必要的元数据、参数定义、步骤配置等信息。
/// 工作流定义是不可变的模板，用于创建具体的工作流实例。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowDefinition {
    /// 工作流唯一标识符
    pub id: Uuid,
    /// 工作流名称，用于显示和识别
    pub name: String,
    /// 工作流版本号，支持版本管理
    pub version: String,
    /// 工作流详细描述
    pub description: String,
    /// 工作流标签，用于分类和搜索
    pub tags: Vec<String>,
    /// 输入参数定义列表
    pub input_parameters: Vec<WorkflowParameter>,
    /// 输出参数定义列表
    pub output_parameters: Vec<WorkflowParameter>,
    /// 工作流步骤列表
    pub steps: Vec<WorkflowStep>,
    /// 初始步骤ID，工作流执行的起点
    pub initial_step_id: Uuid,
    /// 工作流全局配置
    pub config: WorkflowConfig,
    /// 创建时间戳
    pub created_at: SystemTime,
    /// 最后更新时间戳
    pub updated_at: SystemTime,
}

/// 工作流参数定义
/// 
/// 定义工作流的输入或输出参数的结构和约束。
/// 支持类型检查、默认值、必需性验证等功能。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowParameter {
    /// 参数名称，必须唯一
    pub name: String,
    /// 参数数据类型（如：string, number, boolean, object, array）
    pub param_type: String,
    /// 参数描述信息
    pub description: String,
    /// 是否为必需参数
    pub required: bool,
    /// 参数默认值（JSON格式）
    pub default_value: Option<serde_json::Value>,
    /// 参数约束条件（如：最小值、最大值、正则表达式等）
    pub constraints: Option<serde_json::Value>,
}

/// 工作流步骤定义
/// 
/// 定义工作流中的单个执行步骤，包含步骤类型、配置、输入输出映射等信息。
/// 每个步骤都有唯一的ID，支持条件执行、重试、超时等高级功能。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowStep {
    /// 步骤唯一标识符
    pub id: Uuid,
    /// 步骤名称，用于显示和调试
    pub name: String,
    /// 步骤类型，决定执行逻辑
    pub step_type: StepType,
    /// 步骤描述信息
    pub description: String,
    /// 步骤配置参数
    pub config: super::config::StepConfig,
    /// 输入参数映射（参数名 -> 变量路径）
    pub input_mapping: HashMap<String, String>,
    /// 输出参数映射（输出名 -> 变量路径）
    pub output_mapping: HashMap<String, String>,
    /// 条件表达式，用于条件执行
    pub condition: Option<String>,
    /// 重试配置
    pub retry_config: Option<super::config::RetryConfig>,
    /// 超时时间（秒）
    pub timeout_seconds: Option<u64>,
    /// 下一步骤映射（条件 -> 步骤ID）
    pub next_steps: HashMap<String, Uuid>,
    /// 错误处理步骤ID
    pub error_handler: Option<Uuid>,
}

/// 步骤类型枚举
/// 
/// 定义工作流中支持的各种步骤类型。
/// 每种类型都有特定的执行逻辑和配置参数。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StepType {
    /// 工具调用步骤
    /// 
    /// 调用注册的工具或插件执行特定任务
    ToolCall {
        /// 要调用的工具名称
        tool_name: String,
    },
    
    /// LLM调用步骤
    /// 
    /// 调用大语言模型进行文本生成或分析
    LlmCall {
        /// 使用的模型名称
        model_name: String,
        /// 提示词模板
        prompt_template: String,
    },
    
    /// 条件分支步骤
    /// 
    /// 根据条件表达式的结果选择不同的执行路径
    Condition {
        /// 条件表达式
        expression: String,
    },
    
    /// 循环步骤
    /// 
    /// 重复执行一组步骤直到满足退出条件
    Loop {
        /// 循环类型
        loop_type: LoopType,
        /// 循环条件表达式
        condition: String,
        /// 最大迭代次数限制
        max_iterations: Option<u32>,
    },
    
    /// 并行执行步骤
    /// 
    /// 同时执行多个步骤，支持不同的等待策略
    Parallel {
        /// 并行执行的步骤ID列表
        parallel_steps: Vec<Uuid>,
        /// 等待策略
        wait_strategy: WaitStrategy,
    },
    
    /// 数据转换步骤
    /// 
    /// 使用脚本对数据进行转换处理
    Transform {
        /// 转换脚本代码
        script: String,
        /// 脚本语言类型
        language: String,
    },
    
    /// 延迟步骤
    /// 
    /// 暂停执行指定的时间
    Delay {
        /// 延迟时间（秒）
        duration_seconds: u64,
    },
    
    /// 人工干预步骤
    /// 
    /// 暂停工作流等待人工处理
    HumanIntervention {
        /// 干预类型标识
        intervention_type: String,
        /// 提示信息
        prompt: String,
    },
    
    /// 子工作流步骤
    /// 
    /// 调用另一个工作流作为子流程
    SubWorkflow {
        /// 子工作流定义ID
        workflow_id: Uuid,
    },
    
    /// 自定义步骤
    /// 
    /// 支持扩展的自定义步骤类型
    Custom {
        /// 自定义步骤类型标识
        custom_type: String,
        /// 自定义配置参数
        config: serde_json::Value,
    },
}

/// 循环类型枚举
/// 
/// 定义支持的循环控制结构类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LoopType {
    /// While循环 - 条件为真时继续执行
    While,
    /// For循环 - 指定次数的循环
    For,
    /// ForEach循环 - 遍历集合中的每个元素
    ForEach,
}

/// 等待策略枚举
/// 
/// 定义并行步骤的等待策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum WaitStrategy {
    /// 等待所有步骤完成
    All,
    /// 等待任意一个步骤完成
    Any,
    /// 等待指定数量的步骤完成
    Count(u32),
    /// 等待指定百分比的步骤完成
    Percentage(f64),
}
