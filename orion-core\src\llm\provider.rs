//! # LLM 提供商接口定义
//!
//! 定义 LLM 提供商的通用接口，支持多种 LLM 服务商的统一访问。

use crate::error::Result;
use super::types::{LlmRequest, LlmResponse, StreamChunk, ModelConfig};
use async_trait::async_trait;

/// LLM 提供商 trait
/// 
/// 所有 LLM 提供商都需要实现这个 trait，提供统一的接口来访问不同的 LLM 服务。
#[async_trait]
pub trait LlmProvider: Send + Sync {
    /// 提供商名称
    /// 
    /// 返回提供商的唯一标识符，如 "openai", "kimi", "anthropic" 等。
    fn name(&self) -> &str;
    
    /// 支持的模型列表
    /// 
    /// 返回该提供商支持的所有模型名称列表。
    async fn supported_models(&self) -> Result<Vec<String>>;
    
    /// 发送请求（非流式）
    /// 
    /// 发送 LLM 请求并等待完整响应。适用于需要完整结果的场景。
    /// 
    /// # 参数
    /// 
    /// * `request` - LLM 请求对象，包含消息、参数等信息
    /// 
    /// # 返回
    /// 
    /// 返回完整的 LLM 响应，包含生成的内容、token 使用情况等。
    async fn complete(&self, request: LlmRequest) -> Result<LlmResponse>;
    
    /// 发送流式请求
    /// 
    /// 发送 LLM 请求并返回流式响应通道。适用于需要实时显示生成过程的场景。
    /// 
    /// # 参数
    /// 
    /// * `request` - LLM 请求对象，包含消息、参数等信息
    /// 
    /// # 返回
    /// 
    /// 返回一个接收器，可以逐步接收流式响应块。
    async fn stream(&self, request: LlmRequest) -> Result<tokio::sync::mpsc::Receiver<Result<StreamChunk>>>;
    
    /// 验证配置
    /// 
    /// 验证提供商的配置是否正确，包括 API 密钥、模型名称等。
    /// 
    /// # 参数
    /// 
    /// * `config` - 模型配置对象
    /// 
    /// # 返回
    /// 
    /// 如果配置有效返回 Ok(())，否则返回错误信息。
    async fn validate_config(&self, config: &ModelConfig) -> Result<()>;
    
    /// 估算 token 数量
    /// 
    /// 根据文本内容估算需要的 token 数量。这是一个近似值，用于成本估算和限制检查。
    /// 
    /// # 参数
    /// 
    /// * `text` - 要估算的文本内容
    /// 
    /// # 返回
    /// 
    /// 估算的 token 数量
    fn estimate_tokens(&self, text: &str) -> u32 {
        // 简单估算：英文约 4 字符/token，中文约 1.5 字符/token
        let char_count = text.chars().count() as u32;
        let chinese_chars = text.chars().filter(|c| {
            let code = *c as u32;
            code >= 0x4E00 && code <= 0x9FFF
        }).count() as u32;
        let other_chars = char_count - chinese_chars;
        
        (chinese_chars as f32 / 1.5 + other_chars as f32 / 4.0).ceil() as u32
    }
}
