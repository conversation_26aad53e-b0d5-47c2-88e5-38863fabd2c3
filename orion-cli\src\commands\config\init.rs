//! # 初始化配置功能
//!
//! 实现配置文件的初始化和模板生成功能。

use crate::error::{CliError, Result};
use crate::commands::config::types::InitConfig;
use orion_core::config::OrionConfig;
use std::fs;

impl InitConfig {
    /// 执行初始化配置
    pub async fn execute(&self) -> Result<()> {
        // 检查文件是否已存在
        if self.output.exists() && !self.force {
            return Err(CliError::ConfigError {
                error: format!(
                    "配置文件 '{}' 已存在，使用 --force 覆盖",
                    self.output.display()
                ),
            });
        }
        
        // 根据模板创建配置
        let config = match self.template.as_str() {
            "minimal" => self.create_minimal_config(),
            "development" => self.create_development_config(),
            "production" => self.create_production_config(),
            "default" | _ => OrionConfig::default(),
        };
        
        // 生成配置内容
        let content = if self.with_examples {
            self.generate_config_with_examples(&config)?
        } else {
            toml::to_string_pretty(&config)
                .map_err(|e| CliError::SerializationError {
                    error: format!("序列化配置失败: {}", e),
                })?
        };
        
        // 创建目录（如果需要）
        if let Some(parent) = self.output.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| CliError::IoError {
                    error: format!("创建目录失败: {}", e),
                })?;
        }
        
        // 写入配置文件
        fs::write(&self.output, content)
            .map_err(|e| CliError::IoError {
                error: format!("写入配置文件失败: {}", e),
            })?;
        
        println!("✅ 配置文件已创建: {}", self.output.display());
        println!("📝 模板类型: {}", self.template);
        
        if self.with_examples {
            println!("💡 配置文件包含示例和注释，请根据需要修改");
        }
        
        Ok(())
    }
    
    /// 创建最小配置
    fn create_minimal_config(&self) -> OrionConfig {
        // 返回默认配置，因为所有字段都是必需的
        OrionConfig::default()
    }
    
    /// 创建开发环境配置
    fn create_development_config(&self) -> OrionConfig {
        let mut config = OrionConfig::default();

        // 开发环境特定设置
        config.logging.level = "debug".to_string();
        config.logging.enable_file = true;

        config.security.enable_sandbox = true;

        config
    }
    
    /// 创建生产环境配置
    fn create_production_config(&self) -> OrionConfig {
        let mut config = OrionConfig::default();

        // 生产环境特定设置
        config.logging.level = "info".to_string();
        config.logging.enable_file = true;
        config.logging.rotation_size = 100;
        config.logging.retention_count = 10;

        config.security.enable_sandbox = true;
        config.security.sandbox.timeout = 60;
        config.security.sandbox.memory_limit = 1024;
        config.security.sandbox.cpu_limit = 80;

        config
    }
    
    /// 生成带示例的配置
    fn generate_config_with_examples(&self, _config: &OrionConfig) -> Result<String> {
        let mut content = String::new();

        content.push_str("# Orion Agent 配置文件\n");
        content.push_str("# 更多信息请参考: https://github.com/your-org/orion\n\n");

        // 通用配置
        content.push_str("[general]\n");
        content.push_str("# 日志级别: trace, debug, info, warn, error\n");
        content.push_str("log_level = \"info\"\n");
        content.push_str("# 工作目录\n");
        content.push_str("work_dir = \"./orion-workspace\"\n");
        content.push_str("# 输出格式: table, json, yaml\n");
        content.push_str("output_format = \"table\"\n");
        content.push_str("# 是否启用颜色输出\n");
        content.push_str("enable_color = true\n");
        content.push_str("# 是否启用性能分析\n");
        content.push_str("enable_profiling = false\n\n");
        
        // API 配置
        content.push_str("[api]\n");
        content.push_str("# API 基础 URL\n");
        content.push_str("base_url = \"https://api.orion-ai.com\"\n");
        content.push_str("# API 密钥（可选）\n");
        content.push_str("# api_key = \"your-api-key\"\n");
        content.push_str("# 请求超时时间（秒）\n");
        content.push_str("timeout = 30\n");
        content.push_str("# 最大重试次数\n");
        content.push_str("max_retries = 3\n");
        content.push_str("# 重试间隔（秒）\n");
        content.push_str("retry_interval = 1\n\n");

        // Agent 配置
        content.push_str("[agents]\n");
        content.push_str("# 默认模型\n");
        content.push_str("default_model = \"gpt-4\"\n");
        content.push_str("# 最大并发 Agent 数量\n");
        content.push_str("max_concurrent = 5\n");
        content.push_str("# 是否自动启动\n");
        content.push_str("auto_start = false\n");
        content.push_str("# 默认超时时间（秒）\n");
        content.push_str("default_timeout = 300\n");
        content.push_str("# 最大上下文长度\n");
        content.push_str("max_context_length = 8192\n\n");
        
        // 工具配置
        content.push_str("[tools]\n");
        content.push_str("# 工具注册表 URL\n");
        content.push_str("registry_url = \"https://tools.orion-ai.com\"\n");
        content.push_str("# 是否自动更新\n");
        content.push_str("auto_update = true\n");
        content.push_str("# 缓存目录\n");
        content.push_str("cache_dir = \".cache/orion/tools\"\n");
        content.push_str("# 工具执行超时时间（秒）\n");
        content.push_str("execution_timeout = 60\n");
        content.push_str("# 最大并发工具执行数\n");
        content.push_str("max_concurrent_executions = 10\n\n");

        // 工作流配置
        content.push_str("[workflows]\n");
        content.push_str("# 模板目录\n");
        content.push_str("template_dir = \".config/orion/templates\"\n");
        content.push_str("# 最大执行时间（秒）\n");
        content.push_str("max_execution_time = 3600\n");
        content.push_str("# 是否自动保存\n");
        content.push_str("auto_save = true\n");
        content.push_str("# 最大并发工作流数\n");
        content.push_str("max_concurrent_workflows = 3\n\n");

        // 日志配置
        content.push_str("[logging]\n");
        content.push_str("# 日志级别: trace, debug, info, warn, error\n");
        content.push_str("level = \"info\"\n");
        content.push_str("# 日志格式\n");
        content.push_str("format = \"pretty\"\n");
        content.push_str("# 是否启用控制台输出\n");
        content.push_str("enable_console = true\n");
        content.push_str("# 是否启用文件输出\n");
        content.push_str("enable_file = false\n");
        content.push_str("# 日志轮转大小（MB）\n");
        content.push_str("rotation_size = 100\n");
        content.push_str("# 保留日志文件数量\n");
        content.push_str("retention_count = 10\n\n");

        // 安全配置
        content.push_str("[security]\n");
        content.push_str("# 是否启用沙箱\n");
        content.push_str("enable_sandbox = true\n");
        content.push_str("# 允许的域名列表\n");
        content.push_str("allowed_domains = [\"api.openai.com\", \"api.anthropic.com\"]\n");
        content.push_str("# 禁止的命令列表\n");
        content.push_str("blocked_commands = [\"rm\", \"del\", \"format\"]\n\n");

        content.push_str("[security.sandbox]\n");
        content.push_str("# 内存限制（MB）\n");
        content.push_str("memory_limit = 512\n");
        content.push_str("# CPU 限制（百分比）\n");
        content.push_str("cpu_limit = 50\n");
        content.push_str("# 执行超时时间（秒）\n");
        content.push_str("timeout = 300\n");
        content.push_str("# 临时目录\n");
        content.push_str("temp_dir = \"/tmp/orion-sandbox\"\n\n");
        
        Ok(content)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    
    #[tokio::test]
    async fn test_init_config() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("test.toml");
        
        let cmd = InitConfig {
            output: config_path.clone(),
            force: false,
            template: "default".to_string(),
            with_examples: false,
        };
        
        assert!(cmd.execute().await.is_ok());
        assert!(config_path.exists());
    }
}