//! # 沙箱执行器
//!
//! 负责在沙箱环境中执行命令和脚本。
//! 提供进程管理、资源限制和安全控制功能。

use super::types::{SandboxConfig, ExecutionResult, ExecutionContext, ScriptType};
use super::security::SecurityManager;
use super::monitor::ResourceMonitor;
use crate::error::{OrionError, Result};
use std::path::PathBuf;
use std::process::Stdio;
use std::time::{Duration, SystemTime};
use tempfile::TempDir;
use tokio::fs;
use tokio::process::Command;
use tokio::time::timeout;
use tracing;
use uuid::Uuid;

/// 沙箱执行器
/// 
/// 负责在受控环境中执行命令和脚本
pub struct SandboxExecutor {
    /// 安全管理器
    security_manager: SecurityManager,
}

impl SandboxExecutor {
    /// 创建新的执行器
    pub fn new() -> Self {
        Self {
            security_manager: SecurityManager::new(),
        }
    }

    /// 执行命令
    /// 
    /// 在沙箱环境中执行指定的命令
    pub async fn execute_command(
        &self,
        command: &str,
        args: &[&str],
        config: &SandboxConfig,
    ) -> Result<ExecutionResult> {
        let execution_id = Uuid::new_v4();
        let start_time = SystemTime::now();

        tracing::info!(
            "开始执行命令: {} {:?}, 执行ID: {}",
            command,
            args,
            execution_id
        );

        // 创建临时目录
        let temp_dir = TempDir::new()
            .map_err(|e| OrionError::SecurityError(format!("创建临时目录失败: {}", e)))?;

        // 创建执行上下文
        let mut context = ExecutionContext::new(execution_id, temp_dir);

        // 设置工作目录
        let working_dir = config.working_directory
            .clone()
            .unwrap_or_else(|| context.temp_dir.path().to_path_buf());

        // 构建命令
        let mut cmd = self.build_command(command, args, &working_dir, config)?;

        // 应用安全限制
        self.security_manager.apply_security_limits(&mut cmd, config)?;

        // 启动进程
        let child = cmd.spawn()
            .map_err(|e| OrionError::SecurityError(format!("启动进程失败: {}", e)))?;

        context.child = Some(child);

        // 等待执行完成（带超时）
        let timeout_duration = Duration::from_secs(config.timeout_seconds);
        let execution_result = timeout(timeout_duration, async {
            self.wait_for_completion(context).await
        }).await;

        let mut result = match execution_result {
            Ok(result) => result?,
            Err(_) => {
                // 超时处理
                tracing::warn!("执行超时: {}", execution_id);
                ExecutionResult {
                    id: execution_id,
                    exit_code: None,
                    stdout: String::new(),
                    stderr: String::new(),
                    execution_time_ms: 0,
                    peak_memory_mb: 0,
                    cpu_usage_percent: 0.0,
                    timed_out: true,
                    killed: true,
                    error: Some("执行超时".to_string()),
                    timestamp: SystemTime::now(),
                }
            }
        };

        result.execution_time_ms = start_time.elapsed().unwrap_or_default().as_millis() as u64;

        tracing::info!(
            "命令执行完成: {}, 耗时: {}ms, 退出码: {:?}",
            execution_id,
            result.execution_time_ms,
            result.exit_code
        );

        Ok(result)
    }

    /// 执行脚本
    /// 
    /// 在沙箱环境中执行指定类型的脚本
    pub async fn execute_script(
        &self,
        script_content: &str,
        script_type: ScriptType,
        config: &SandboxConfig,
    ) -> Result<ExecutionResult> {
        tracing::info!("开始执行脚本，类型: {:?}", script_type);

        // 创建临时脚本文件
        let temp_dir = TempDir::new()
            .map_err(|e| OrionError::SecurityError(format!("创建临时目录失败: {}", e)))?;

        let script_path = self.create_script_file(&temp_dir, script_content, script_type).await?;

        // 构建执行参数
        let interpreter = script_type.interpreter();
        let mut args = script_type.interpreter_args();
        args.push(script_path.to_str().unwrap());

        // 执行脚本
        self.execute_command(interpreter, &args, config).await
    }

    /// 构建命令
    fn build_command(
        &self,
        command: &str,
        args: &[&str],
        working_dir: &PathBuf,
        config: &SandboxConfig,
    ) -> Result<Command> {
        let mut cmd = Command::new(command);
        
        cmd.args(args)
            .current_dir(working_dir)
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .stdin(Stdio::null());

        // 设置环境变量
        for (key, value) in &config.environment {
            cmd.env(key, value);
        }

        Ok(cmd)
    }

    /// 创建脚本文件
    async fn create_script_file(
        &self,
        temp_dir: &TempDir,
        script_content: &str,
        script_type: ScriptType,
    ) -> Result<PathBuf> {
        let filename = format!("script.{}", script_type.file_extension());
        let script_path = temp_dir.path().join(filename);

        fs::write(&script_path, script_content).await
            .map_err(|e| OrionError::SecurityError(format!("写入脚本文件失败: {}", e)))?;

        // 为 Shell 脚本设置执行权限
        if matches!(script_type, ScriptType::Shell) {
            self.set_executable_permission(&script_path).await?;
        }

        Ok(script_path)
    }

    /// 设置文件执行权限（仅 Unix 系统）
    #[cfg(unix)]
    async fn set_executable_permission(&self, script_path: &PathBuf) -> Result<()> {
        use std::os::unix::fs::PermissionsExt;
        
        let mut perms = fs::metadata(script_path).await
            .map_err(|e| OrionError::SecurityError(format!("获取文件权限失败: {}", e)))?
            .permissions();
        
        perms.set_mode(0o755);
        
        fs::set_permissions(script_path, perms).await
            .map_err(|e| OrionError::SecurityError(format!("设置文件权限失败: {}", e)))?;

        Ok(())
    }

    /// 设置文件执行权限（Windows 系统 - 无操作）
    #[cfg(not(unix))]
    async fn set_executable_permission(&self, _script_path: &PathBuf) -> Result<()> {
        // Windows 系统不需要设置执行权限
        Ok(())
    }

    /// 等待执行完成
    async fn wait_for_completion(&self, mut context: ExecutionContext) -> Result<ExecutionResult> {
        let execution_id = context.id;
        
        // 获取子进程
        let child = context.child.take()
            .ok_or_else(|| OrionError::SecurityError("子进程不存在".to_string()))?;

        // 启动资源监控
        let monitor_handle = {
            let child_id = child.id();
            tokio::spawn(async move {
                ResourceMonitor::monitor_process(child_id).await
            })
        };

        // 等待进程完成
        let output = child.wait_with_output().await
            .map_err(|e| OrionError::SecurityError(format!("等待进程完成失败: {}", e)))?;

        // 停止资源监控
        monitor_handle.abort();

        // 获取监控结果
        let monitor_result = if let Ok(monitor) = monitor_handle.await {
            monitor
        } else {
            super::monitor::ProcessMonitorResult::default()
        };

        Ok(ExecutionResult {
            id: execution_id,
            exit_code: output.status.code(),
            stdout: String::from_utf8_lossy(&output.stdout).to_string(),
            stderr: String::from_utf8_lossy(&output.stderr).to_string(),
            execution_time_ms: 0, // 将在调用方设置
            peak_memory_mb: monitor_result.peak_memory_mb,
            cpu_usage_percent: monitor_result.cpu_usage_percent,
            timed_out: false,
            killed: false,
            error: if output.status.success() { 
                None 
            } else { 
                Some("进程非正常退出".to_string()) 
            },
            timestamp: SystemTime::now(),
        })
    }

    /// 终止执行
    pub async fn kill_execution(&self, context: &mut ExecutionContext) -> Result<()> {
        if let Some(ref mut child) = context.child {
            child.kill().await
                .map_err(|e| OrionError::SecurityError(format!("终止进程失败: {}", e)))?;
            
            tracing::info!("执行 {} 已被终止", context.id);
        }
        Ok(())
    }

    /// 检查命令是否被允许执行
    pub fn is_command_allowed(&self, command: &str, config: &SandboxConfig) -> bool {
        self.security_manager.is_command_allowed(command, config)
    }

    /// 验证脚本内容安全性
    pub fn validate_script_security(&self, script_content: &str, script_type: ScriptType) -> Result<()> {
        self.security_manager.validate_script_security(script_content, script_type)
    }
}

impl Default for SandboxExecutor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::sandbox::config::ConfigPresets;

    #[tokio::test]
    async fn test_executor_creation() {
        let executor = SandboxExecutor::new();
        assert!(executor.is_command_allowed("echo", &ConfigPresets::default()));
    }

    #[tokio::test]
    async fn test_simple_command_execution() {
        let executor = SandboxExecutor::new();
        let config = ConfigPresets::testing();

        // 测试简单命令（跨平台）
        #[cfg(target_os = "windows")]
        let result = executor.execute_command("cmd", &["/C", "echo", "Hello"], &config).await;

        #[cfg(not(target_os = "windows"))]
        let result = executor.execute_command("echo", &["Hello"], &config).await;

        if let Ok(result) = result {
            assert!(result.stdout.contains("Hello"));
            assert_eq!(result.exit_code, Some(0));
            assert!(!result.timed_out);
        }
    }

    #[tokio::test]
    async fn test_script_execution() {
        let executor = SandboxExecutor::new();
        let config = ConfigPresets::testing();

        let script_content = r#"
print("Hello from Python!")
"#;

        let result = executor.execute_script(script_content, ScriptType::Python, &config).await;
        
        // 注意：这个测试可能失败如果系统没有安装 Python
        if let Ok(result) = result {
            assert!(result.stdout.contains("Hello from Python!"));
            assert_eq!(result.exit_code, Some(0));
        }
    }

    #[tokio::test]
    async fn test_timeout_handling() {
        let executor = SandboxExecutor::new();
        let mut config = ConfigPresets::testing();
        config.timeout_seconds = 1; // 1秒超时

        // 创建一个会运行很长时间的命令
        #[cfg(target_os = "windows")]
        let result = executor.execute_command("ping", &["127.0.0.1", "-n", "10"], &config).await;

        #[cfg(not(target_os = "windows"))]
        let result = executor.execute_command("sleep", &["5"], &config).await;

        if let Ok(result) = result {
            assert!(result.timed_out);
            assert!(result.killed);
        }
    }
}
