//! # CLI 命令模块
//!
//! 提供 Orion CLI 的各种子命令实现。

pub mod run;
pub mod config;
pub mod version;
pub mod workflow;
pub mod tools;
pub mod agent;

use crate::error::Result;
use clap::Subcommand;
use serde::{Deserialize, Serialize};

/// CLI 子命令
#[derive(Debug, Clone, Subcommand, Serialize, Deserialize)]
pub enum Commands {
    /// 运行 Orion Agent
    Run(run::RunCommand),
    /// 配置管理
    Config(config::ConfigCommand),
    /// 显示版本信息
    Version(version::VersionCommand),
    /// 工作流管理
    Workflow(workflow::WorkflowCommand),
    /// 工具管理
    Tools(tools::ToolsCommand),
    /// Agent 管理
    Agent(agent::AgentCommand),
}

impl Commands {
    /// 执行命令
    pub async fn execute(&mut self) -> Result<()> {
        match self {
            Commands::Run(cmd) => cmd.execute().await,
            Commands::Config(cmd) => cmd.execute().await,
            Commands::Version(cmd) => cmd.execute().await,
            Commands::Workflow(cmd) => cmd.execute().await,
            Commands::Tools(cmd) => cmd.execute().await,
            Commands::Agent(cmd) => cmd.execute().await,
        }
    }
}