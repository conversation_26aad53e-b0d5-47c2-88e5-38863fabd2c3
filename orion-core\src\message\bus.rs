//! # 消息总线模块
//!
//! 本模块实现了 h2A 启发的高性能异步消息队列系统。
//! 消息总线是整个消息系统的核心，负责消息的路由、分发和管理。
//! 支持多消费者、优先级调度、Agent 注册管理等功能。

use crate::error::{OrionError, Result};
use super::types::{Message, AgentId};
use super::stats::StatsManager;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{mpsc, Mutex, RwLock};
use tokio::time::timeout;
use uuid::Uuid;

/// 消息总线 - h2A 启发的高性能异步消息队列
/// 
/// 消息总线是整个消息系统的核心组件，提供以下功能：
/// - 异步消息传递和路由
/// - Agent 注册和管理
/// - 消息优先级处理
/// - 统计信息收集
/// - 过期消息处理
/// - 广播和点对点通信
pub struct MessageBus {
    /// 消息发送通道
    /// 
    /// 用于向主消息队列发送消息
    sender: mpsc::UnboundedSender<Message>,
    
    /// 消息接收通道（使用 Arc<Mutex> 支持多消费者）
    /// 
    /// 主消息队列的接收端，支持多个消费者并发处理
    receiver: Arc<Mutex<mpsc::UnboundedReceiver<Message>>>,
    
    /// 消息路由表：Agent ID -> 消息处理器
    /// 
    /// 存储已注册 Agent 的消息通道，用于直接路由
    routes: Arc<RwLock<HashMap<AgentId, mpsc::UnboundedSender<Message>>>>,
    
    /// 统计信息管理器
    /// 
    /// 收集和管理消息系统的各种统计信息
    stats_manager: Arc<StatsManager>,
    
    /// 是否正在运行
    /// 
    /// 标识消息总线的运行状态
    running: Arc<RwLock<bool>>,
}

impl MessageBus {
    /// 创建新的消息总线
    /// 
    /// 初始化消息总线的所有组件，包括通道、路由表、统计管理器等
    /// 
    /// # 返回值
    /// 
    /// 返回新创建的消息总线实例
    pub fn new() -> Self {
        let (sender, receiver) = mpsc::unbounded_channel();
        
        Self {
            sender,
            receiver: Arc::new(Mutex::new(receiver)),
            routes: Arc::new(RwLock::new(HashMap::new())),
            stats_manager: Arc::new(StatsManager::default()),
            running: Arc::new(RwLock::new(false)),
        }
    }
    
    /// 使用自定义统计管理器创建消息总线
    /// 
    /// # 参数
    /// 
    /// * `stats_manager` - 自定义的统计管理器
    /// 
    /// # 返回值
    /// 
    /// 返回新创建的消息总线实例
    pub fn with_stats_manager(stats_manager: Arc<StatsManager>) -> Self {
        let (sender, receiver) = mpsc::unbounded_channel();
        
        Self {
            sender,
            receiver: Arc::new(Mutex::new(receiver)),
            routes: Arc::new(RwLock::new(HashMap::new())),
            stats_manager,
            running: Arc::new(RwLock::new(false)),
        }
    }
    
    /// 启动消息总线
    /// 
    /// 将消息总线标记为运行状态，开始接受消息处理
    /// 
    /// # 返回值
    /// 
    /// * `Ok(())` - 启动成功
    /// * `Err(OrionError)` - 如果总线已经在运行
    pub async fn start(&self) -> Result<()> {
        let mut running = self.running.write().await;
        if *running {
            return Err(OrionError::MessageBusError("消息总线已经在运行".to_string()));
        }
        *running = true;
        
        tracing::info!("消息总线启动成功");
        Ok(())
    }
    
    /// 停止消息总线
    /// 
    /// 将消息总线标记为停止状态，停止接受新的消息处理
    /// 
    /// # 返回值
    /// 
    /// 总是返回 Ok(())
    pub async fn stop(&self) -> Result<()> {
        let mut running = self.running.write().await;
        if !*running {
            return Ok(());
        }
        *running = false;
        
        tracing::info!("消息总线已停止");
        Ok(())
    }
    
    /// 检查消息总线是否正在运行
    /// 
    /// # 返回值
    /// 
    /// 如果正在运行返回 true，否则返回 false
    pub async fn is_running(&self) -> bool {
        *self.running.read().await
    }
    
    /// 注册 Agent 消息处理器
    /// 
    /// 为指定的 Agent 创建专用的消息通道，并将其注册到路由表中
    /// 
    /// # 参数
    /// 
    /// * `agent_id` - Agent 的唯一标识符
    /// 
    /// # 返回值
    /// 
    /// 返回 Agent 专用的消息接收器
    pub async fn register_agent(&self, agent_id: AgentId) -> mpsc::UnboundedReceiver<Message> {
        let (sender, receiver) = mpsc::unbounded_channel();
        
        let mut routes = self.routes.write().await;
        routes.insert(agent_id.clone(), sender);
        
        tracing::debug!("Agent {} 已注册到消息总线", agent_id);
        receiver
    }
    
    /// 注销 Agent
    /// 
    /// 从路由表中移除指定的 Agent
    /// 
    /// # 参数
    /// 
    /// * `agent_id` - 要注销的 Agent ID
    /// 
    /// # 返回值
    /// 
    /// 总是返回 Ok(())
    pub async fn unregister_agent(&self, agent_id: &AgentId) -> Result<()> {
        let mut routes = self.routes.write().await;
        routes.remove(agent_id);
        
        tracing::debug!("Agent {} 已从消息总线注销", agent_id);
        Ok(())
    }
    
    /// 发送消息
    /// 
    /// 将消息发送到目标 Agent 或主消息队列。
    /// 首先尝试直接路由到目标 Agent，如果 Agent 未注册则发送到主队列。
    /// 
    /// # 参数
    /// 
    /// * `message` - 要发送的消息
    /// 
    /// # 返回值
    /// 
    /// * `Ok(())` - 发送成功
    /// * `Err(OrionError)` - 发送失败（消息过期、队列关闭等）
    pub async fn send_message(&self, message: Message) -> Result<()> {
        // 检查消息是否过期
        if message.is_expired() {
            self.stats_manager.record_message_dropped().await;
            return Err(OrionError::MessageSendError {
                from: message.from.clone(),
                to: message.to.clone(),
            });
        }
        
        // 记录发送统计
        self.stats_manager.record_message_sent().await;
        
        // 尝试直接路由到目标 Agent
        let routes = self.routes.read().await;
        if let Some(agent_sender) = routes.get(&message.to) {
            if let Err(_) = agent_sender.send(message.clone()) {
                // Agent 接收器已关闭，从路由表中移除
                drop(routes);
                let mut routes = self.routes.write().await;
                routes.remove(&message.to);
                
                return Err(OrionError::MessageSendError {
                    from: message.from,
                    to: message.to,
                });
            }
        } else {
            // 目标 Agent 未注册，发送到主消息队列
            self.sender.send(message.clone())
                .map_err(|_| OrionError::MessageBusError("主消息队列已关闭".to_string()))?;
        }
        
        tracing::trace!(
            "消息已发送: {} -> {}, 优先级: {:?}, ID: {}",
            message.from,
            message.to,
            message.priority,
            message.id
        );
        
        Ok(())
    }
    
    /// 接收消息（带超时）
    /// 
    /// 从主消息队列接收消息，支持超时控制
    /// 
    /// # 参数
    /// 
    /// * `timeout_duration` - 超时时间
    /// 
    /// # 返回值
    /// 
    /// * `Ok(message)` - 成功接收到消息
    /// * `Err(OrionError)` - 接收失败（超时、队列关闭等）
    pub async fn receive_message(&self, timeout_duration: Duration) -> Result<Message> {
        let receiver = self.receiver.clone();
        
        match timeout(timeout_duration, async {
            let mut receiver = receiver.lock().await;
            receiver.recv().await
        }).await {
            Ok(Some(message)) => {
                // 记录接收统计
                self.stats_manager.record_message_received().await;
                
                Ok(message)
            }
            Ok(None) => Err(OrionError::MessageBusError("消息队列已关闭".to_string())),
            Err(_) => Err(OrionError::MessageReceiveTimeout {
                timeout_ms: timeout_duration.as_millis() as u64,
            }),
        }
    }
    
    /// 广播消息到所有注册的 Agent
    /// 
    /// 将消息发送给所有已注册的 Agent，为每个接收者生成独立的消息 ID
    /// 
    /// # 参数
    /// 
    /// * `message` - 要广播的消息
    /// 
    /// # 返回值
    /// 
    /// 总是返回 Ok(())，失败的 Agent 会被自动清理
    pub async fn broadcast_message(&self, mut message: Message) -> Result<()> {
        let routes = self.routes.read().await;
        let mut failed_agents = Vec::new();
        
        for (agent_id, sender) in routes.iter() {
            message.to = agent_id.clone();
            message.id = Uuid::new_v4(); // 为每个接收者生成新的消息 ID
            
            if let Err(_) = sender.send(message.clone()) {
                failed_agents.push(agent_id.clone());
            }
        }
        
        // 清理失败的 Agent
        if !failed_agents.is_empty() {
            drop(routes);
            let mut routes = self.routes.write().await;
            for agent_id in failed_agents {
                routes.remove(&agent_id);
                tracing::warn!("Agent {} 接收器已关闭，已从路由表移除", agent_id);
            }
        }
        
        Ok(())
    }
    
    /// 获取消息统计信息
    /// 
    /// # 返回值
    /// 
    /// 返回当前的统计信息
    pub async fn get_stats(&self) -> super::stats::MessageStats {
        self.stats_manager.get_stats().await
    }
    
    /// 获取统计管理器引用
    /// 
    /// # 返回值
    /// 
    /// 返回统计管理器的 Arc 引用
    pub fn get_stats_manager(&self) -> Arc<StatsManager> {
        self.stats_manager.clone()
    }
    
    /// 获取当前注册的 Agent 列表
    /// 
    /// # 返回值
    /// 
    /// 返回所有已注册 Agent 的 ID 列表
    pub async fn get_registered_agents(&self) -> Vec<AgentId> {
        let routes = self.routes.read().await;
        routes.keys().cloned().collect()
    }
    
    /// 检查 Agent 是否已注册
    /// 
    /// # 参数
    /// 
    /// * `agent_id` - 要检查的 Agent ID
    /// 
    /// # 返回值
    /// 
    /// 如果 Agent 已注册返回 true，否则返回 false
    pub async fn is_agent_registered(&self, agent_id: &AgentId) -> bool {
        let routes = self.routes.read().await;
        routes.contains_key(agent_id)
    }
    
    /// 获取当前队列长度
    /// 
    /// # 返回值
    /// 
    /// 返回当前主消息队列中待处理的消息数量
    pub async fn get_queue_length(&self) -> usize {
        // 注意：这是一个近似值，因为 mpsc 通道不提供精确的长度查询
        // 在实际实现中，可能需要维护一个单独的计数器
        0 // 占位实现
    }
    
    /// 清理过期消息（定期调用）
    /// 
    /// 在当前实现中，过期消息在发送时就被拦截，
    /// 这个方法主要用于统计和未来的扩展
    /// 
    /// # 返回值
    /// 
    /// 返回清理的消息数量
    pub async fn cleanup_expired_messages(&self) -> Result<usize> {
        // 这个方法在当前实现中主要用于统计，
        // 因为过期消息在发送时就被拦截了
        Ok(0)
    }
}

impl Default for MessageBus {
    fn default() -> Self {
        Self::new()
    }
}
